"""
Minimal Integration Tests

This module provides basic integration tests to verify that the core components
work together without complex service dependencies.
"""

import pytest
import sys
import os

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.core.schemas.switchboard_schemas import SwitchboardCreateSchema
from backend.core.schemas.user_schemas import UserCreateSchema
from backend.core.models.enums import SwitchboardType


class TestMinimalIntegration:
    """Test basic integration between schemas and models."""

    def test_switchboard_schema_with_enum(self):
        """Test that switchboard schema works with enum types."""
        data = {
            "name": "Test Switchboard",
            "project_id": 1,
            "location": "Test Location",
            "voltage_level_v": 415,
            "number_of_phases": 3,
            "type": SwitchboardType.MAIN,
        }

        schema = SwitchboardCreateSchema(**data)
        assert schema.name == "Test Switchboard"
        assert schema.type == SwitchboardType.MAIN
        assert schema.voltage_level_v == 415

    def test_user_schema_validation(self):
        """Test that user schema validation works correctly."""
        data = {
            "name": "John Smith",
            "email": "<EMAIL>",
            "password": "SecurePass123",
            "is_active": True,
        }

        schema = UserCreateSchema(**data)
        assert schema.name == "John Smith"
        assert schema.email == "<EMAIL>"
        assert schema.is_active is True

    def test_schema_validation_errors(self):
        """Test that schema validation properly catches errors."""
        from pydantic import ValidationError

        # Test invalid switchboard data
        with pytest.raises(ValidationError):
            SwitchboardCreateSchema(
                name="",  # Empty name should fail
                project_id=1,
                location="Test Location",
                voltage_level_v=415,
                number_of_phases=3,
                type=SwitchboardType.MAIN,
            )

        # Test invalid user data
        with pytest.raises(ValidationError):
            UserCreateSchema(
                name="John Smith",
                email="invalid-email",  # Invalid email format
                password="SecurePass123",
                is_active=True,
            )

    def test_enum_values(self):
        """Test that enum values are correctly defined."""
        assert SwitchboardType.MAIN.value == "Main"
        assert SwitchboardType.SUB_DISTRIBUTION.value == "Sub-distribution"

    def test_schema_serialization(self):
        """Test that schemas can be serialized to dictionaries."""
        switchboard_data = {
            "name": "Test Switchboard",
            "project_id": 1,
            "location": "Test Location",
            "voltage_level_v": 415,
            "number_of_phases": 3,
            "type": SwitchboardType.MAIN,
        }

        schema = SwitchboardCreateSchema(**switchboard_data)
        serialized = schema.model_dump()

        assert serialized["name"] == "Test Switchboard"
        assert serialized["type"] == SwitchboardType.MAIN
        assert isinstance(serialized, dict)

    def test_optional_fields(self):
        """Test that optional fields work correctly."""
        # Test user with optional email
        user_data = {
            "name": "John Smith",
            "password": "SecurePass123",
            "is_active": True,
            # email is optional
        }

        schema = UserCreateSchema(**user_data)
        assert schema.name == "John Smith"
        assert schema.email is None

    def test_default_values(self):
        """Test that default values are applied correctly."""
        user_data = {
            "name": "John Smith",
            "email": "<EMAIL>",
            "password": "SecurePass123",
            # is_active should default to True
        }

        schema = UserCreateSchema(**user_data)
        assert schema.is_active is True

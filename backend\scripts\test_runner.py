#!/usr/bin/env python3
"""
Test Runner Script for Ultimate Electrical Designer Backend

This script provides convenient commands for running different categories of tests
as defined in pyproject.toml. It supports the Test Suite Quality Improvement Action Plan.

Usage:
    python scripts/test_runner.py --help
    python scripts/test_runner.py unit
    python scripts/test_runner.py integration
    python scripts/test_runner.py security
    python scripts/test_runner.py all --coverage
"""

import argparse
import os
import subprocess
import sys
from pathlib import Path
from typing import List, Optional


class TestRunner:
    """Test runner for different test categories."""

    def __init__(self, backend_dir: Path):
        self.backend_dir = backend_dir
        self.base_cmd = ["python", "-m", "pytest"]

    def run_command(self, cmd: List[str], description: str) -> bool:
        """Run a command and return success status."""
        print(f"\n{'=' * 60}")
        print(f"Running: {description}")
        print(f"Command: {' '.join(cmd)}")
        print(f"{'=' * 60}")

        try:
            result = subprocess.run(cmd, cwd=self.backend_dir, check=True)
            print(f"✅ {description} completed successfully")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ {description} failed with exit code {e.returncode}")
            return False

    def unit_tests(self, coverage: bool = False) -> bool:
        """Run unit tests."""
        cmd = self.base_cmd + ["-m", "unit", "--tb=short", "-v"]
        if coverage:
            cmd.extend(
                [
                    "--cov=core",
                    "--cov=api",
                    "--cov=config",
                    "--cov-report=term-missing",
                    "--cov-report=html:htmlcov/unit",
                ]
            )

        return self.run_command(cmd, "Unit Tests")

    def integration_tests(self, coverage: bool = False) -> bool:
        """Run integration tests."""
        cmd = self.base_cmd + ["-m", "integration", "--tb=short", "-v"]
        if coverage:
            cmd.extend(
                [
                    "--cov=core",
                    "--cov=api",
                    "--cov-report=term-missing",
                    "--cov-report=html:htmlcov/integration",
                ]
            )

        return self.run_command(cmd, "Integration Tests")

    def api_tests(self, coverage: bool = False) -> bool:
        """Run API tests."""
        cmd = self.base_cmd + ["-m", "api", "--tb=short", "-v"]
        if coverage:
            cmd.extend(
                [
                    "--cov=api",
                    "--cov-report=term-missing",
                    "--cov-report=html:htmlcov/api",
                ]
            )

        return self.run_command(cmd, "API Tests")

    def database_tests(self, coverage: bool = False) -> bool:
        """Run database tests."""
        cmd = self.base_cmd + ["-m", "database", "--tb=short", "-v"]
        if coverage:
            cmd.extend(
                [
                    "--cov=core/repositories",
                    "--cov=core/database",
                    "--cov-report=term-missing",
                    "--cov-report=html:htmlcov/database",
                ]
            )

        return self.run_command(cmd, "Database Tests")

    def security_tests(self) -> bool:
        """Run security tests."""
        cmd = self.base_cmd + ["-m", "security", "--tb=short", "-v"]

        return self.run_command(cmd, "Security Tests")

    def performance_tests(self) -> bool:
        """Run performance tests."""
        cmd = self.base_cmd + [
            "-m",
            "performance",
            "--tb=short",
            "-v",
            "--benchmark-only",
        ]

        return self.run_command(cmd, "Performance Tests")

    def smoke_tests(self, coverage: bool = False) -> bool:
        """Run smoke tests."""
        cmd = self.base_cmd + ["-m", "smoke", "--tb=line", "-v", "--maxfail=5"]
        if coverage:
            cmd.extend(
                [
                    "--cov=core",
                    "--cov=api",
                    "--cov-report=term-missing",
                    "--cov-report=html:htmlcov/smoke",
                ]
            )

        return self.run_command(cmd, "Smoke Tests")

    def schema_tests(self, coverage: bool = False) -> bool:
        """Run schema validation tests."""
        cmd = self.base_cmd + ["-m", "schema", "--tb=short", "-v"]
        if coverage:
            cmd.extend(
                [
                    "--cov=core/schemas",
                    "--cov-report=term-missing",
                    "--cov-report=html:htmlcov/schemas",
                ]
            )

        return self.run_command(cmd, "Schema Tests")

    def service_tests(self, coverage: bool = False) -> bool:
        """Run service layer tests."""
        cmd = self.base_cmd + ["-m", "service", "--tb=short", "-v"]
        if coverage:
            cmd.extend(
                [
                    "--cov=core/services",
                    "--cov-report=term-missing",
                    "--cov-report=html:htmlcov/services",
                ]
            )

        return self.run_command(cmd, "Service Tests")

    def repository_tests(self, coverage: bool = False) -> bool:
        """Run repository layer tests."""
        cmd = self.base_cmd + ["-m", "repository", "--tb=short", "-v"]
        if coverage:
            cmd.extend(
                [
                    "--cov=core/repositories",
                    "--cov-report=term-missing",
                    "--cov-report=html:htmlcov/repositories",
                ]
            )

        return self.run_command(cmd, "Repository Tests")

    def entity_tests(self, entity: str, coverage: bool = False) -> bool:
        """Run tests for a specific entity."""
        cmd = self.base_cmd + ["-m", entity, "--tb=short", "-v"]
        if coverage:
            cmd.extend(
                [
                    f"--cov=core/models/{entity}",
                    f"--cov=core/schemas/{entity}",
                    f"--cov=core/services/{entity}",
                    f"--cov=core/repositories/{entity}",
                    f"--cov=api/v1/{entity}",
                    "--cov-report=term-missing",
                    f"--cov-report=html:htmlcov/{entity}",
                ]
            )

        return self.run_command(cmd, f"{entity.title()} Entity Tests")

    def all_tests(self, coverage: bool = False) -> bool:
        """Run all tests."""
        cmd = self.base_cmd + ["tests/", "--tb=short", "-v"]
        if coverage:
            cmd.extend(
                [
                    "--cov=core",
                    "--cov=api",
                    "--cov=config",
                    "--cov-report=term-missing",
                    "--cov-report=html:htmlcov",
                    "--cov-report=xml:coverage.xml",
                    "--cov-fail-under=90",
                ]
            )

        return self.run_command(cmd, "All Tests")

    def quality_check(self) -> bool:
        """Run code quality checks."""
        success = True

        # Ruff linting
        ruff_cmd = ["ruff", "check", ".", "--fix"]
        success &= self.run_command(ruff_cmd, "Ruff Linting")

        # Ruff formatting
        format_cmd = ["ruff", "format", "."]
        success &= self.run_command(format_cmd, "Ruff Formatting")

        # MyPy type checking
        mypy_cmd = ["mypy", "core", "api", "config"]
        success &= self.run_command(mypy_cmd, "MyPy Type Checking")

        # Bandit security check
        bandit_cmd = [
            "bandit",
            "-r",
            "core",
            "api",
            "config",
            "-f",
            "json",
            "-o",
            "bandit-report.json",
        ]
        success &= self.run_command(bandit_cmd, "Bandit Security Check")

        return success


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Test runner for Ultimate Electrical Designer Backend",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Test Categories:
  unit          Run unit tests
  integration   Run integration tests
  api           Run API tests
  database      Run database tests
  security      Run security tests
  performance   Run performance tests
  smoke         Run smoke tests
  schema        Run schema validation tests
  service       Run service layer tests
  repository    Run repository layer tests
  all           Run all tests
  quality       Run code quality checks

Entity-specific tests:
  project       Run project entity tests
  component     Run component entity tests
  heat_tracing  Run heat tracing entity tests
  electrical    Run electrical entity tests
  switchboard   Run switchboard entity tests
  user          Run user entity tests
  document      Run document entity tests

Examples:
  python scripts/test_runner.py unit --coverage
  python scripts/test_runner.py integration
  python scripts/test_runner.py project --coverage
  python scripts/test_runner.py all --coverage
  python scripts/test_runner.py quality
        """,
    )

    parser.add_argument(
        "test_type",
        choices=[
            "unit",
            "integration",
            "api",
            "database",
            "security",
            "performance",
            "smoke",
            "schema",
            "service",
            "repository",
            "all",
            "quality",
            "project",
            "component",
            "heat_tracing",
            "electrical",
            "switchboard",
            "user",
            "document",
        ],
        help="Type of tests to run",
    )

    parser.add_argument(
        "--coverage", action="store_true", help="Include coverage reporting"
    )

    parser.add_argument(
        "--backend-dir",
        type=Path,
        default=Path(__file__).parent.parent,
        help="Backend directory path",
    )

    args = parser.parse_args()

    # Set environment variables
    os.environ["PYTHONPATH"] = str(args.backend_dir)
    os.environ["DATABASE_URL"] = "sqlite:///:memory:"
    os.environ["TESTING"] = "true"

    runner = TestRunner(args.backend_dir)

    # Entity tests
    entities = [
        "project",
        "component",
        "heat_tracing",
        "electrical",
        "switchboard",
        "user",
        "document",
    ]
    if args.test_type in entities:
        success = runner.entity_tests(args.test_type, args.coverage)
    else:
        # Test category mapping
        test_methods = {
            "unit": runner.unit_tests,
            "integration": runner.integration_tests,
            "api": runner.api_tests,
            "database": runner.database_tests,
            "security": runner.security_tests,
            "performance": runner.performance_tests,
            "smoke": runner.smoke_tests,
            "schema": runner.schema_tests,
            "service": runner.service_tests,
            "repository": runner.repository_tests,
            "all": runner.all_tests,
            "quality": runner.quality_check,
        }

        method = test_methods[args.test_type]
        if args.test_type == "quality":
            success = method()
        else:
            success = method(args.coverage)

    if success:
        print(f"\n✅ {args.test_type.title()} tests completed successfully!")
        sys.exit(0)
    else:
        print(f"\n❌ {args.test_type.title()} tests failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()

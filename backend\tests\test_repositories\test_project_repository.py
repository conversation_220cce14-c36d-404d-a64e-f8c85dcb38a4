# backend/tests/test_repositories/test_project_repository.py
"""
Unit tests for ProjectRepository.

Tests data access layer functionality including CRUD operations,
querying, pagination, and error handling.
"""

import pytest
from datetime import datetime, timezone
from sqlalchemy.exc import IntegrityError

# Mark all tests in this file
pytestmark = [pytest.mark.unit, pytest.mark.repository, pytest.mark.database, pytest.mark.project]

from core.repositories.project_repository import ProjectRepository
from core.models.project import Project
from core.models.enums import InstallationEnvironment
from core.errors.exceptions import ProjectNotFoundError


class TestProjectRepository:
    """Test ProjectRepository functionality."""

    @pytest.fixture
    def project_repository(self, db_session):
        """Create a ProjectRepository instance for testing."""
        return ProjectRepository(db_session)

    def test_create_project(self, project_repository, sample_project_data):
        """Test creating a new project."""
        project = project_repository.create(sample_project_data)

        assert project.id is not None
        assert project.name == sample_project_data["name"]
        assert project.project_number == sample_project_data["project_number"]
        assert project.created_at is not None
        assert project.updated_at is not None
        assert not project.is_deleted

    def test_get_by_id_existing(self, project_repository, sample_project_orm):
        """Test getting project by ID when it exists."""
        project = project_repository.get_by_id(sample_project_orm.id)

        assert project is not None
        assert project.id == sample_project_orm.id
        assert project.name == sample_project_orm.name

    def test_get_by_id_nonexistent(self, project_repository):
        """Test getting project by ID when it doesn't exist."""
        project = project_repository.get_by_id(99999)
        assert project is None

    def test_get_by_code_existing(self, project_repository, sample_project_orm):
        """Test getting project by project number when it exists."""
        project = project_repository.get_by_code(sample_project_orm.project_number)

        assert project is not None
        assert project.id == sample_project_orm.id
        assert project.project_number == sample_project_orm.project_number

    def test_get_by_code_nonexistent(self, project_repository):
        """Test getting project by project number when it doesn't exist."""
        with pytest.raises(ProjectNotFoundError) as exc_info:
            project_repository.get_by_code("NONEXISTENT-001")

        assert "NONEXISTENT-001" in str(exc_info.value)

    def test_get_by_name_existing(self, project_repository, sample_project_orm):
        """Test getting project by name when it exists."""
        project = project_repository.get_by_name(sample_project_orm.name)

        assert project is not None
        assert project.id == sample_project_orm.id
        assert project.name == sample_project_orm.name

    def test_get_by_name_nonexistent(self, project_repository):
        """Test getting project by name when it doesn't exist."""
        project = project_repository.get_by_name("Nonexistent Project")
        assert project is None

    def test_get_all_projects(self, project_repository, multiple_projects_orm):
        """Test getting all projects."""
        projects = project_repository.get_all()

        assert len(projects) == len(multiple_projects_orm)

        # Check that projects are ordered by creation date (newest first)
        for i in range(len(projects) - 1):
            assert projects[i].created_at >= projects[i + 1].created_at

    def test_get_all_with_pagination(self, project_repository, multiple_projects_orm):
        """Test getting projects with pagination."""
        # Get first page
        page1 = project_repository.get_all(skip=0, limit=5)
        assert len(page1) == 5

        # Get second page
        page2 = project_repository.get_all(skip=5, limit=5)
        assert len(page2) == 5

        # Ensure no overlap
        page1_ids = {p.id for p in page1}
        page2_ids = {p.id for p in page2}
        assert page1_ids.isdisjoint(page2_ids)

    def test_get_active_projects(
        self, project_repository, multiple_projects_orm, db_session
    ):
        """Test getting only active (non-deleted) projects."""
        # Soft delete some projects
        for i in range(3):
            multiple_projects_orm[i].is_deleted = True
            multiple_projects_orm[i].deleted_at = datetime.now(timezone.utc)
        db_session.commit()

        active_projects = project_repository.get_active_projects()

        # Should return only non-deleted projects
        assert len(active_projects) == len(multiple_projects_orm) - 3
        for project in active_projects:
            assert not project.is_deleted

    def test_get_active_projects_with_pagination(
        self, project_repository, multiple_projects_orm
    ):
        """Test getting active projects with pagination."""
        active_projects = project_repository.get_active_projects(skip=2, limit=5)

        assert len(active_projects) <= 5
        for project in active_projects:
            assert not project.is_deleted

    def test_update_project(self, project_repository, sample_project_orm):
        """Test updating a project."""
        update_data = {
            "name": "Updated Project Name",
            "description": "Updated description",
            "max_ambient_temp_c": 45.0,
        }

        updated_project = project_repository.update_project(
            sample_project_orm.id, update_data
        )

        assert updated_project is not None
        assert updated_project.name == "Updated Project Name"
        assert updated_project.description == "Updated description"
        assert updated_project.max_ambient_temp_c == 45.0
        # Unchanged fields should remain the same
        assert updated_project.project_number == sample_project_orm.project_number

    def test_update_nonexistent_project(self, project_repository):
        """Test updating a project that doesn't exist."""
        update_data = {"name": "Updated Name"}

        result = project_repository.update_project(99999, update_data)
        assert result is None

    def test_soft_delete_project(self, project_repository, sample_project_orm):
        """Test soft deleting a project."""
        user_id = 123
        result = project_repository.soft_delete_project(sample_project_orm.id, user_id)

        assert result is True

        # Verify project is marked as deleted
        project = project_repository.get_by_id(sample_project_orm.id)
        assert project.is_deleted is True
        assert project.deleted_at is not None
        assert project.deleted_by_user_id == user_id

    def test_soft_delete_nonexistent_project(self, project_repository):
        """Test soft deleting a project that doesn't exist."""
        result = project_repository.soft_delete_project(99999, 123)
        assert result is False

    def test_soft_delete_already_deleted_project(
        self, project_repository, sample_project_orm, db_session
    ):
        """Test soft deleting a project that's already deleted."""
        # First deletion
        project_repository.soft_delete_project(sample_project_orm.id, 123)

        # Second deletion attempt
        result = project_repository.soft_delete_project(sample_project_orm.id, 456)
        assert result is False

    def test_search_projects_by_name(self, project_repository, multiple_projects_orm):
        """Test searching projects by name."""
        # Search for projects with "Project 1" in the name
        results = project_repository.search_projects("Project 1")

        # Should find projects like "Test Project 1", "Test Project 10", etc.
        assert len(results) > 0
        for project in results:
            assert "Project 1" in project.name

    def test_search_projects_by_project_number(
        self, project_repository, multiple_projects_orm
    ):
        """Test searching projects by project number."""
        results = project_repository.search_projects("HT-TEST-001")

        assert len(results) == 1
        assert results[0].project_number == "HT-TEST-001"

    def test_search_projects_by_description(
        self, project_repository, multiple_projects_orm
    ):
        """Test searching projects by description."""
        results = project_repository.search_projects("description")

        # All test projects have "description" in their description
        assert len(results) > 0
        for project in results:
            assert "description" in project.description.lower()

    def test_search_projects_case_insensitive(
        self, project_repository, multiple_projects_orm
    ):
        """Test that project search is case insensitive."""
        results_lower = project_repository.search_projects("test project")
        results_upper = project_repository.search_projects("TEST PROJECT")
        results_mixed = project_repository.search_projects("Test Project")

        # All searches should return the same results
        assert len(results_lower) == len(results_upper) == len(results_mixed)

    def test_search_projects_excludes_deleted(
        self, project_repository, multiple_projects_orm, db_session
    ):
        """Test that search excludes soft-deleted projects."""
        # Delete some projects
        for i in range(3):
            multiple_projects_orm[i].is_deleted = True
        db_session.commit()

        results = project_repository.search_projects("Test Project")

        # Should not include deleted projects
        for project in results:
            assert not project.is_deleted

    def test_search_projects_with_pagination(
        self, project_repository, multiple_projects_orm
    ):
        """Test searching projects with pagination."""
        results_page1 = project_repository.search_projects("Project", skip=0, limit=5)
        results_page2 = project_repository.search_projects("Project", skip=5, limit=5)

        assert len(results_page1) <= 5
        assert len(results_page2) <= 5

        # Ensure no overlap
        page1_ids = {p.id for p in results_page1}
        page2_ids = {p.id for p in results_page2}
        assert page1_ids.isdisjoint(page2_ids)

    def test_count_active_projects(
        self, project_repository, multiple_projects_orm, db_session
    ):
        """Test counting active projects."""
        # Delete some projects
        deleted_count = 3
        for i in range(deleted_count):
            multiple_projects_orm[i].is_deleted = True
        db_session.commit()

        count = project_repository.count_active_projects()

        expected_count = len(multiple_projects_orm) - deleted_count
        assert count == expected_count

    def test_count_active_projects_empty_db(self, project_repository):
        """Test counting active projects when database is empty."""
        count = project_repository.count_active_projects()
        assert count == 0

    def test_get_project_with_related_data(
        self, project_repository, sample_project_orm
    ):
        """Test getting project with eagerly loaded related data."""
        project = project_repository.get_project_with_related_data(
            sample_project_orm.id
        )

        assert project is not None
        assert project.id == sample_project_orm.id

        # Related collections should be loaded (even if empty)
        assert hasattr(project, "switchboards")
        assert hasattr(project, "pipes")
        assert hasattr(project, "vessels")
        assert hasattr(project, "electrical_nodes")
        assert hasattr(project, "cable_routes")

    def test_get_project_with_related_data_nonexistent(self, project_repository):
        """Test getting related data for nonexistent project."""
        project = project_repository.get_project_with_related_data(99999)
        assert project is None

    def test_unique_constraint_name(self, project_repository, sample_project_data):
        """Test that project name must be unique."""
        # Create first project
        project_repository.create(sample_project_data)

        # Try to create second project with same name
        duplicate_data = sample_project_data.copy()
        duplicate_data["project_number"] = "DIFFERENT-001"

        with pytest.raises(IntegrityError):
            project_repository.create(duplicate_data)

    def test_unique_constraint_project_number(
        self, project_repository, sample_project_data
    ):
        """Test that project number must be unique."""
        # Create first project
        project_repository.create(sample_project_data)

        # Try to create second project with same project number
        duplicate_data = sample_project_data.copy()
        duplicate_data["name"] = "Different Project Name"

        with pytest.raises(IntegrityError):
            project_repository.create(duplicate_data)

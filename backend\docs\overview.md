# Backend Codebase Overview

This document provides a high-level overview of the backend codebase structure within the `src/` directory.

For detailed architectural specifications of each layer and module, please refer to the linked documents.
For practical guides on specific tasks, see the [How-To Guides](how-to/).

For a comprehensive overview of the backend architecture, including principles and implemented layers, see the [Backend Architecture](backend-architecture.md).

## Architectural Layers and Modules

Here is a list of the main architectural layers and modules with links to their detailed specifications:

*   [API Layer](api/api-architecture.md)
*   [Configuration Layer](config/config-architecture.md)
*   Core Layers:
    *   [Calculations Layer](core/calculations/calculations-architecture.md)
    *   [Database Layer](core/database/architecture.md)
        *   [Migrations Architecture](core/database/migrations-architecture.md)
        *   [Seeding Architecture](core/database/seeding-architecture.md)
        *   [Transaction Architecture](core/database/transaction-architecture.md)
    *   [Data Import Layer](core/data_import/data_import-architecture.md)
    *   [Error Handling Layer](core/errors/errors-architecture.md)
    *   [Model Layer](core/models/models-architecture.md)
        *   [Model Validation Architecture](core/models/validation-architecture.md)
    *   [Reports Layer](core/reports/reports-architecture.md)
    *   [Repositories Layer](core/repositories/repositories-architecture.md)
    *   [Schemas Layer](core/schemas/schemas-architecture.md)
    *   [Services Layer](core/services/services-architecture.md)
    *   [Standards Layer](core/standards/standards-architecture.md)
*   [Lifespan Events](lifespan-architecture.md)
*   [Middleware Layer](middleware/middleware-architecture.md)
*   [Secrets Management](secrets/secrets-architecture.md)
*   [Security Layer](security/security-architecture.md)
*   [Telemetry Layer](telemetry/telemetry-architecture.md)
*   [Utility Functions](utils/utils-architecture.md)

src/
├── app.py                      # Main FastAPI application instance, wires up layers, registers middleware
├── [config/](config/config-architecture.md)                     # Application configuration settings
│   └── settings.py
│
├── core/
│   ├── models/                 # SQLAlchemy ORM models (your domain entities)
│   │   ├── __init__.py
│   │   ├── base.py
│   │   ├── enums.py            # Global enums used across models
│   │   ├── project.py
│   │   ├── components.py       # e.g., Pipe, Insulation
│   │   ├── heat_tracing.py     # e.g., Circuit, HeatLossSegment
│   │   ├── electrical.py       # e.g., Cable, Load
│   │   ├── switchboard.py      # e.g., DistributionBoard, Feeder
│   │   ├── users.py
│   │   ├── audit.py            # Audit trail related models
│   │   └── documents.py        # Models related to generated reports/documents
│   │
│   ├── repositories/           # Abstraction over data persistence (SQLAlchemy operations)
│   │   ├── __init__.py
│   │   ├── base_repository.py  # Generic CRUD operations
│   │   ├── project_repository.py
│   │   ├── component_repository.py
│   │   ├── heat_tracing_repository.py
│   │   ├── electrical_repository.py
│   │   ├── switchboard_repository.py
│   │   ├── user_repository.py
│   │   └── document_repository.py
│   │
│   ├── services/               # Business logic, orchestrates repositories and other services
│   │   ├── __init__.py
│   │   ├── project_service.py
│   │   ├── calculation_service.py
│   │   ├── circuit_design_service.py
│   │   ├── report_service.py
│   │   ├── user_service.py
│   │   └── import_export_service.py
│   │
│   ├── schemas/                # Pydantic models for request/response validation and serialization
│   │   ├── __init__.py
│   │   ├── error.py            # Pydantic schema for standardized error responses
│   │   ├── base.py             # Base schemas for common fields (ID, timestamps)
│   │   ├── project_schemas.py
│   │   ├── component_schemas.py
│   │   ├── heat_tracing_schemas.py
│   │   ├── electrical_schemas.py
│   │   ├── user_schemas.py
│   │   ├── report_schemas.py
│   │   └── import_schemas.py   # Schemas specific to data import validation
│   │
│   ├── database/               # Database connection, engine, and session management
│   │   ├── __init__.py
│   │   ├── session.py          # Provides session factory and dependency for FastAPI
│   │   └── engine.py           # Database engine creation and metadata setup
│   │
│   ├── calculations/           # Core engineering calculation logic and algorithms
│   │   ├── __init__.py
│   │   ├── heat_loss_calcs.py
│   │   ├── cable_sizing_calcs.py
│   │   ├── circuit_analysis_calcs.py
│   │   └── utils.py            # Internal calculation utilities (e.g., specific math functions)
│   │
│   ├── standards/              # Implementation of engineering standards and compliance rules
│   │   ├── __init__.py
│   │   ├── compliance_rules.py
│   │   ├── tr_50410.py         # Example: TR 50410 specific rules
│   │   └── hazardous_area.py   # Example: Hazardous area classification logic
│   │
│   ├── reports/                # Report generation module for native document templates (XLSX, DOCX to PDF)
│   │   ├── __init__.py
│   │   ├── templates/          # Native report templates (e.g., .docx, .xlsx)
│   │   │   └── ...
│   │   ├── data_preparators/   # Logic to prepare data for specific reports
│   │   │   └── ...
│   │   ├── document_populator/ # Logic to fill data into native templates
│   │   │   └── ...
│   │   ├── pdf_converter/      # Utility for converting populated documents to PDF
│   │   │   └── ...
│   │   └── circuit_reports/    # Specific report types (e.g., heat_loss_report.py)
│   │       └── ...
│   │
│   ├── data_import/            # Module for importing data from external sources (.xlsx, .json, .csv)
│   │   ├── __init__.py
│   │   ├── global_importer.py
│   │   ├── project_importer.py
│   │   ├── parsers/            # File format-specific parsing logic
│   │   │   └── ...
│   │   ├── validators/         # Import-specific validation (beyond schemas)
│   │   │   └── ...
│   │   └── mappers/            # Mapping parsed data to ORM models
│   │       └── ...
│   │
│   └── errors/                 # Custom exceptions, error registry, error factory, handlers
│       ├── __init__.py
│       ├── error_factory.py
│       ├── error_registry.py
│       ├── error_templates.py
│       └── exceptions.py
│
├── middleware/                 # HTTP Request/Response interceptors
│   ├── __init__.py
│   ├── base.py                 # Base middleware class/interface
│   ├── caching.py
│   ├── context.py              # Request-scoped context (user, request_id, etc.)
│   ├── data.py                 # Compression, content negotiation
│   ├── error_handling.py       # Global exception catcher & HTTP error formatter
│   ├── logging_middleware.py   # Request/response logging
│   ├── monitoring.py           # Performance metrics collection
│   ├── rate_limiting.py
│   ├── request.py              # Request ID injection, API versioning
│   └── security.py             # Authentication (JWT), Authorization, Security Headers
│
├── api/                        # Web Framework specific routing, controllers (views)
│   ├── __init__.py
│   ├── v1/                     # API Version 1
│   │   ├── __init__.py
│   │   ├── project_routes.py   # e.g., /v1/projects endpoint definitions
│   │   ├── component_routes.py
│   │   ├── heat_tracing_routes.py
│   │   ├── electrical_routes.py
│   │   ├── user_routes.py
│   │   ├── auth_routes.py      # Login/logout, token issuance
│   │   └── report_routes.py    # Endpoints for report generation
│   │
│   └── main_router.py          # Aggregates all versioned routes
│
└── utils/                      # General utility functions, helpers
    ├── __init__.py
    ├── file_operations.py      # Generic file handling (e.g., temporary file management)
    └── common_helpers.py       # Miscellaneous, truly generic helper functions (e.g., UUID generation, date formatting)

## Development Tools and Testing

The backend includes comprehensive development tools and testing infrastructure to ensure code quality and reliability.

### Configuration Files

- **`pyproject.toml`**: Main project configuration including dependencies, testing, and code quality tools
- **`Makefile`**: Convenient development commands for testing, linting, and quality checks
- **`scripts/test_runner.py`**: Comprehensive test runner script with category support

### Testing Framework

The backend uses pytest with comprehensive test categorization:

#### Test Categories
- **Unit Tests**: Individual component testing
- **Integration Tests**: Component interaction testing
- **API Tests**: Endpoint testing
- **Database Tests**: Repository and database layer testing
- **Security Tests**: Authentication and security validation
- **Performance Tests**: Load and benchmark testing
- **Smoke Tests**: Quick functionality validation

#### Layer-Specific Tests
- **Schema Tests**: Pydantic validation testing
- **Repository Tests**: Data access layer testing
- **Service Tests**: Business logic testing
- **Calculations Tests**: Engineering calculations testing
- **Standards Tests**: Compliance validation testing

#### Entity-Specific Tests
- **Project Tests**: Project entity testing
- **Component Tests**: Component entity testing
- **Heat Tracing Tests**: Heat tracing entity testing
- **Electrical Tests**: Electrical entity testing
- **Switchboard Tests**: Switchboard entity testing
- **User Tests**: User entity testing
- **Document Tests**: Document entity testing

### Development Commands

#### Quick Testing Commands
```bash
make test              # Run all tests with coverage
make test-unit         # Run unit tests
make test-integration  # Run integration tests
make test-api          # Run API tests
make test-security     # Run security tests
make test-performance  # Run performance tests
make test-smoke        # Run smoke tests
```

#### Layer-Specific Testing
```bash
make test-schemas      # Run schema validation tests
make test-services     # Run service layer tests
make test-repositories # Run repository layer tests
make test-database     # Run database tests
```

#### Entity-Specific Testing
```bash
make test-project      # Run project entity tests
make test-component    # Run component entity tests
make test-heat-tracing # Run heat tracing entity tests
make test-electrical   # Run electrical entity tests
make test-switchboard  # Run switchboard entity tests
make test-user         # Run user entity tests
make test-document     # Run document entity tests
```

#### Code Quality Commands
```bash
make quality           # Run all quality checks
make lint              # Run Ruff linting
make format            # Run Ruff formatting
make type-check        # Run MyPy type checking
make security-check    # Run Bandit security analysis
```

#### Development Workflows
```bash
make install           # Install dependencies
make dev-setup         # Setup development environment
make pre-commit        # Quick pre-commit validation
make pre-merge         # Full pre-merge validation
make test-improvement  # Test Suite Quality Improvement workflow
```

#### Database Management
```bash
make migrate-db        # Run database migrations
make create-migration  # Create new migration
```

#### Development Server
```bash
make run-dev           # Run development server
make run-prod          # Run production server
```

### Test Runner Script

For more granular control, use the test runner script:

```bash
# Run specific test categories with coverage
python scripts/test_runner.py unit --coverage
python scripts/test_runner.py integration --coverage
python scripts/test_runner.py api --coverage

# Run entity-specific tests
python scripts/test_runner.py project --coverage
python scripts/test_runner.py heat_tracing --coverage

# Run layer-specific tests
python scripts/test_runner.py schema --coverage
python scripts/test_runner.py service --coverage

# Run all tests
python scripts/test_runner.py all --coverage

# Run code quality checks
python scripts/test_runner.py quality
```

### Code Quality Tools

- **Ruff**: Fast Python linter and formatter with security-focused rules
- **MyPy**: Static type checking
- **Bandit**: Security vulnerability scanning
- **pytest-cov**: Code coverage analysis with >90% requirement

### Test Suite Quality Improvement

The backend includes a comprehensive Test Suite Quality Improvement Action Plan:

- **Current Status**: 481 tests with 76.7% pass rate (target: 100%)
- **Coverage**: 58% (target: >90%)
- **Structured Plan**: 4-phase improvement approach
- **Documentation**: Detailed guides in `docs/test-suite-improvement-prompt.md`

Use `make test-improvement` to run the complete improvement workflow.

2025-05-30 11:35:41 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:35:41 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:35:41 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:35:41 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:35:41 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HeatTracingRepository aggregate initialized (heat_tracing_repository.py:1016)
2025-05-30 11:36:53 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:36:54 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:36:54 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:36:54 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:36:54 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:36:54 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:36:54 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Searching for pipe with line tag: NON-EXISTENT in project 1 (heat_tracing_repository.py:123)
2025-05-30 11:36:54 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - ERROR - Database error searching for pipe line tag NON-EXISTENT: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[User(User)]'. Original exception was: When initializing mapper Mapper[User(User)], expression 'ActivityLog' failed to locate a name ('ActivityLog'). If this is a class name, consider adding this relationship() to the <class 'core.models.users.User'> class after both dependent classes have been defined. (heat_tracing_repository.py:145)
2025-05-30 11:36:55 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:36:55 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:36:55 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:36:55 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating heat loss calculation for pipe 999 (heat_tracing_repository.py:242)
2025-05-30 11:36:55 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:36:55 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting pipes for project 1 (heat_tracing_repository.py:286)
2025-05-30 11:36:55 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - ERROR - Database error counting pipes for project 1: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[User(User)]'. Original exception was: When initializing mapper Mapper[User(User)], expression 'ActivityLog' failed to locate a name ('ActivityLog'). If this is a class name, consider adding this relationship() to the <class 'core.models.users.User'> class after both dependent classes have been defined. (heat_tracing_repository.py:300)
2025-05-30 11:36:56 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:36:56 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:36:56 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:36:56 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:36:56 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:36:56 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:36:56 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:36:56 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:36:56 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:36:56 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:36:56 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:36:56 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HeatTracingRepository aggregate initialized (heat_tracing_repository.py:1016)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HeatTracingRepository aggregate initialized (heat_tracing_repository.py:1016)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Generating heat tracing summary for project 1 (heat_tracing_repository.py:1031)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting pipes for project 1 (heat_tracing_repository.py:286)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - ERROR - Database error counting pipes for project 1: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[User(User)]'. Original exception was: When initializing mapper Mapper[User(User)], expression 'ActivityLog' failed to locate a name ('ActivityLog'). If this is a class name, consider adding this relationship() to the <class 'core.models.users.User'> class after both dependent classes have been defined. (heat_tracing_repository.py:300)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - ERROR - Error generating project summary for 1: An unexpected database error occurred during pipe operation. (heat_tracing_repository.py:1056)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HeatTracingRepository aggregate initialized (heat_tracing_repository.py:1016)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Checking design readiness for project 1 (heat_tracing_repository.py:1072)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Generating heat tracing summary for project 1 (heat_tracing_repository.py:1031)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting pipes for project 1 (heat_tracing_repository.py:286)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - ERROR - Database error counting pipes for project 1: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[User(User)]'. Original exception was: When initializing mapper Mapper[User(User)], expression 'ActivityLog' failed to locate a name ('ActivityLog'). If this is a class name, consider adding this relationship() to the <class 'core.models.users.User'> class after both dependent classes have been defined. (heat_tracing_repository.py:300)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - ERROR - Error generating project summary for 1: An unexpected database error occurred during pipe operation. (heat_tracing_repository.py:1056)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - ERROR - Error checking design readiness for project 1: An unexpected database error occurred during pipe operation. (heat_tracing_repository.py:1120)
2025-05-30 11:37:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:37:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:37:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:37:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:37:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HeatTracingRepository aggregate initialized (heat_tracing_repository.py:1016)
2025-05-30 11:37:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:38:36 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:39:06 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:39:50 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:41:06 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:42:47 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:42:56 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes for project 1: skip=0, limit=100 (heat_tracing_repository.py:80)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieved 2 pipes for project 1 (heat_tracing_repository.py:99)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Searching for pipe with line tag: L-001 in project 1 (heat_tracing_repository.py:123)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found pipe: 'Test Pipe 001' (ID: 1) (heat_tracing_repository.py:138)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Searching for pipe with line tag: NON-EXISTENT in project 1 (heat_tracing_repository.py:123)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Pipe not found with line tag: NON-EXISTENT (heat_tracing_repository.py:140)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes without circuits for project 1 (heat_tracing_repository.py:162)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 1 pipes without circuits (heat_tracing_repository.py:178)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating heat loss calculation for pipe 1 (heat_tracing_repository.py:242)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Pipe 1 heat loss calculation updated successfully (heat_tracing_repository.py:264)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating heat loss calculation for pipe 999 (heat_tracing_repository.py:242)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Pipe 999 not found for heat loss update (heat_tracing_repository.py:248)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting pipes for project 1 (heat_tracing_repository.py:286)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total pipes in project 1: 0 (heat_tracing_repository.py:296)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting pipes for project 1 (heat_tracing_repository.py:286)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total pipes in project 1: 1 (heat_tracing_repository.py:296)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating heat loss calculation for pipe 1 (heat_tracing_repository.py:242)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Pipe 1 heat loss calculation updated successfully (heat_tracing_repository.py:264)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes with heat loss calculations for project 1 (heat_tracing_repository.py:199)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 1 pipes with heat loss calculations (heat_tracing_repository.py:217)
2025-05-30 11:43:07 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:43:07 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:43:07 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Searching for vessel with equipment tag: T-001 in project 1 (heat_tracing_repository.py:385)
2025-05-30 11:43:07 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found vessel: 'Test Vessel T-001' (ID: 1) (heat_tracing_repository.py:400)
2025-05-30 11:43:07 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:43:07 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving vessels without circuits for project 1 (heat_tracing_repository.py:426)
2025-05-30 11:43:07 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 1 vessels without circuits (heat_tracing_repository.py:442)
2025-05-30 11:43:07 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:43:07 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating heat loss calculation for vessel 1 (heat_tracing_repository.py:467)
2025-05-30 11:43:07 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Vessel 1 heat loss calculation updated successfully (heat_tracing_repository.py:489)
2025-05-30 11:43:16 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:43:16 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:43:16 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving HT circuits for feeder 1: skip=0, limit=100 (heat_tracing_repository.py:569)
2025-05-30 11:43:16 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieved 1 HT circuits for feeder 1 (heat_tracing_repository.py:588)
2025-05-30 11:43:16 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:43:16 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Searching for HT circuit for pipe 1 (heat_tracing_repository.py:611)
2025-05-30 11:43:16 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found HT circuit: 'HTC-001-A' (ID: 1) (heat_tracing_repository.py:620)
2025-05-30 11:43:16 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:43:16 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating load calculation for HT circuit 1 (heat_tracing_repository.py:729)
2025-05-30 11:43:16 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HT circuit 1 load calculation updated successfully (heat_tracing_repository.py:752)
2025-05-30 11:43:16 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:43:16 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating load calculation for HT circuit 1 (heat_tracing_repository.py:729)
2025-05-30 11:43:16 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HT circuit 1 load calculation updated successfully (heat_tracing_repository.py:752)
2025-05-30 11:43:16 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Calculating total load for feeder 1 (heat_tracing_repository.py:776)
2025-05-30 11:43:16 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total load for feeder 1: 1.25 kW (heat_tracing_repository.py:789)
2025-05-30 11:43:24 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:43:25 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:43:25 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:43:54 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:43:55 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:43:55 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:44:21 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:44:21 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:44:21 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving control circuits for switchboard 1: skip=0, limit=100 (heat_tracing_repository.py:833)
2025-05-30 11:44:21 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieved 1 control circuits for switchboard 1 (heat_tracing_repository.py:852)
2025-05-30 11:44:21 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:44:21 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving control circuits with limiting function for switchboard 1 (heat_tracing_repository.py:926)
2025-05-30 11:44:21 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 1 control circuits with limiting function (heat_tracing_repository.py:942)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HeatTracingRepository aggregate initialized (heat_tracing_repository.py:1016)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HeatTracingRepository aggregate initialized (heat_tracing_repository.py:1016)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Generating heat tracing summary for project 1 (heat_tracing_repository.py:1031)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting pipes for project 1 (heat_tracing_repository.py:286)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total pipes in project 1: 0 (heat_tracing_repository.py:296)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting vessels for project 1 (heat_tracing_repository.py:513)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total vessels in project 1: 0 (heat_tracing_repository.py:523)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes without circuits for project 1 (heat_tracing_repository.py:162)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 pipes without circuits (heat_tracing_repository.py:178)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving vessels without circuits for project 1 (heat_tracing_repository.py:426)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 vessels without circuits (heat_tracing_repository.py:442)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes with heat loss calculations for project 1 (heat_tracing_repository.py:199)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 pipes with heat loss calculations (heat_tracing_repository.py:217)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving vessels without circuits for project 1 (heat_tracing_repository.py:426)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 vessels without circuits (heat_tracing_repository.py:442)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Generated summary for project 1: {'project_id': 1, 'total_pipes': 0, 'total_vessels': 0, 'pipes_without_circuits': 0, 'vessels_without_circuits': 0, 'pipes_with_calculations': 0, 'vessels_with_calculations': 0} (heat_tracing_repository.py:1052)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HeatTracingRepository aggregate initialized (heat_tracing_repository.py:1016)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Checking design readiness for project 1 (heat_tracing_repository.py:1072)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Generating heat tracing summary for project 1 (heat_tracing_repository.py:1031)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting pipes for project 1 (heat_tracing_repository.py:286)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total pipes in project 1: 0 (heat_tracing_repository.py:296)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting vessels for project 1 (heat_tracing_repository.py:513)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total vessels in project 1: 0 (heat_tracing_repository.py:523)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes without circuits for project 1 (heat_tracing_repository.py:162)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 pipes without circuits (heat_tracing_repository.py:178)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving vessels without circuits for project 1 (heat_tracing_repository.py:426)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 vessels without circuits (heat_tracing_repository.py:442)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes with heat loss calculations for project 1 (heat_tracing_repository.py:199)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 pipes with heat loss calculations (heat_tracing_repository.py:217)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving vessels without circuits for project 1 (heat_tracing_repository.py:426)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 vessels without circuits (heat_tracing_repository.py:442)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Generated summary for project 1: {'project_id': 1, 'total_pipes': 0, 'total_vessels': 0, 'pipes_without_circuits': 0, 'vessels_without_circuits': 0, 'pipes_with_calculations': 0, 'vessels_with_calculations': 0} (heat_tracing_repository.py:1052)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Design readiness for project 1: 0.0% (heat_tracing_repository.py:1114)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes for project 1: skip=0, limit=100 (heat_tracing_repository.py:80)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieved 2 pipes for project 1 (heat_tracing_repository.py:99)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Searching for pipe with line tag: L-001 in project 1 (heat_tracing_repository.py:123)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found pipe: 'Test Pipe 001' (ID: 1) (heat_tracing_repository.py:138)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Searching for pipe with line tag: NON-EXISTENT in project 1 (heat_tracing_repository.py:123)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Pipe not found with line tag: NON-EXISTENT (heat_tracing_repository.py:140)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes without circuits for project 1 (heat_tracing_repository.py:162)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 1 pipes without circuits (heat_tracing_repository.py:178)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating heat loss calculation for pipe 1 (heat_tracing_repository.py:242)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Pipe 1 heat loss calculation updated successfully (heat_tracing_repository.py:264)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating heat loss calculation for pipe 999 (heat_tracing_repository.py:242)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Pipe 999 not found for heat loss update (heat_tracing_repository.py:248)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting pipes for project 1 (heat_tracing_repository.py:286)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total pipes in project 1: 0 (heat_tracing_repository.py:296)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting pipes for project 1 (heat_tracing_repository.py:286)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total pipes in project 1: 1 (heat_tracing_repository.py:296)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating heat loss calculation for pipe 1 (heat_tracing_repository.py:242)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Pipe 1 heat loss calculation updated successfully (heat_tracing_repository.py:264)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes with heat loss calculations for project 1 (heat_tracing_repository.py:199)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 1 pipes with heat loss calculations (heat_tracing_repository.py:217)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Searching for vessel with equipment tag: T-001 in project 1 (heat_tracing_repository.py:385)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found vessel: 'Test Vessel T-001' (ID: 1) (heat_tracing_repository.py:400)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving vessels without circuits for project 1 (heat_tracing_repository.py:426)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 1 vessels without circuits (heat_tracing_repository.py:442)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating heat loss calculation for vessel 1 (heat_tracing_repository.py:467)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Vessel 1 heat loss calculation updated successfully (heat_tracing_repository.py:489)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving HT circuits for feeder 1: skip=0, limit=100 (heat_tracing_repository.py:569)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieved 1 HT circuits for feeder 1 (heat_tracing_repository.py:588)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Searching for HT circuit for pipe 1 (heat_tracing_repository.py:611)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found HT circuit: 'HTC-001-A' (ID: 1) (heat_tracing_repository.py:620)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating load calculation for HT circuit 1 (heat_tracing_repository.py:729)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HT circuit 1 load calculation updated successfully (heat_tracing_repository.py:752)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating load calculation for HT circuit 1 (heat_tracing_repository.py:729)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HT circuit 1 load calculation updated successfully (heat_tracing_repository.py:752)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Calculating total load for feeder 1 (heat_tracing_repository.py:776)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total load for feeder 1: 1.25 kW (heat_tracing_repository.py:789)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving control circuits for switchboard 1: skip=0, limit=100 (heat_tracing_repository.py:833)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieved 1 control circuits for switchboard 1 (heat_tracing_repository.py:852)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving control circuits with limiting function for switchboard 1 (heat_tracing_repository.py:926)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 1 control circuits with limiting function (heat_tracing_repository.py:942)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HeatTracingRepository aggregate initialized (heat_tracing_repository.py:1016)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HeatTracingRepository aggregate initialized (heat_tracing_repository.py:1016)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Generating heat tracing summary for project 1 (heat_tracing_repository.py:1031)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting pipes for project 1 (heat_tracing_repository.py:286)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total pipes in project 1: 0 (heat_tracing_repository.py:296)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting vessels for project 1 (heat_tracing_repository.py:513)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total vessels in project 1: 0 (heat_tracing_repository.py:523)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes without circuits for project 1 (heat_tracing_repository.py:162)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 pipes without circuits (heat_tracing_repository.py:178)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving vessels without circuits for project 1 (heat_tracing_repository.py:426)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 vessels without circuits (heat_tracing_repository.py:442)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes with heat loss calculations for project 1 (heat_tracing_repository.py:199)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 pipes with heat loss calculations (heat_tracing_repository.py:217)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving vessels without circuits for project 1 (heat_tracing_repository.py:426)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 vessels without circuits (heat_tracing_repository.py:442)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Generated summary for project 1: {'project_id': 1, 'total_pipes': 0, 'total_vessels': 0, 'pipes_without_circuits': 0, 'vessels_without_circuits': 0, 'pipes_with_calculations': 0, 'vessels_with_calculations': 0} (heat_tracing_repository.py:1052)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HeatTracingRepository aggregate initialized (heat_tracing_repository.py:1016)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Checking design readiness for project 1 (heat_tracing_repository.py:1072)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Generating heat tracing summary for project 1 (heat_tracing_repository.py:1031)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting pipes for project 1 (heat_tracing_repository.py:286)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total pipes in project 1: 0 (heat_tracing_repository.py:296)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting vessels for project 1 (heat_tracing_repository.py:513)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total vessels in project 1: 0 (heat_tracing_repository.py:523)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes without circuits for project 1 (heat_tracing_repository.py:162)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 pipes without circuits (heat_tracing_repository.py:178)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving vessels without circuits for project 1 (heat_tracing_repository.py:426)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 vessels without circuits (heat_tracing_repository.py:442)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes with heat loss calculations for project 1 (heat_tracing_repository.py:199)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 pipes with heat loss calculations (heat_tracing_repository.py:217)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving vessels without circuits for project 1 (heat_tracing_repository.py:426)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 vessels without circuits (heat_tracing_repository.py:442)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Generated summary for project 1: {'project_id': 1, 'total_pipes': 0, 'total_vessels': 0, 'pipes_without_circuits': 0, 'vessels_without_circuits': 0, 'pipes_with_calculations': 0, 'vessels_with_calculations': 0} (heat_tracing_repository.py:1052)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Design readiness for project 1: 0.0% (heat_tracing_repository.py:1114)
2025-05-30 11:45:14 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes for project 1: skip=0, limit=100 (heat_tracing_repository.py:80)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieved 2 pipes for project 1 (heat_tracing_repository.py:99)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Searching for pipe with line tag: L-001 in project 1 (heat_tracing_repository.py:123)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found pipe: 'Test Pipe 001' (ID: 1) (heat_tracing_repository.py:138)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Searching for pipe with line tag: NON-EXISTENT in project 1 (heat_tracing_repository.py:123)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Pipe not found with line tag: NON-EXISTENT (heat_tracing_repository.py:140)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes without circuits for project 1 (heat_tracing_repository.py:162)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 1 pipes without circuits (heat_tracing_repository.py:178)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating heat loss calculation for pipe 1 (heat_tracing_repository.py:242)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Pipe 1 heat loss calculation updated successfully (heat_tracing_repository.py:264)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating heat loss calculation for pipe 999 (heat_tracing_repository.py:242)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Pipe 999 not found for heat loss update (heat_tracing_repository.py:248)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting pipes for project 1 (heat_tracing_repository.py:286)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total pipes in project 1: 0 (heat_tracing_repository.py:296)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting pipes for project 1 (heat_tracing_repository.py:286)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total pipes in project 1: 1 (heat_tracing_repository.py:296)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating heat loss calculation for pipe 1 (heat_tracing_repository.py:242)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Pipe 1 heat loss calculation updated successfully (heat_tracing_repository.py:264)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes with heat loss calculations for project 1 (heat_tracing_repository.py:199)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 1 pipes with heat loss calculations (heat_tracing_repository.py:217)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Searching for vessel with equipment tag: T-001 in project 1 (heat_tracing_repository.py:385)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found vessel: 'Test Vessel T-001' (ID: 1) (heat_tracing_repository.py:400)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving vessels without circuits for project 1 (heat_tracing_repository.py:426)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 1 vessels without circuits (heat_tracing_repository.py:442)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating heat loss calculation for vessel 1 (heat_tracing_repository.py:467)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Vessel 1 heat loss calculation updated successfully (heat_tracing_repository.py:489)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving HT circuits for feeder 1: skip=0, limit=100 (heat_tracing_repository.py:569)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieved 1 HT circuits for feeder 1 (heat_tracing_repository.py:588)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Searching for HT circuit for pipe 1 (heat_tracing_repository.py:611)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found HT circuit: 'HTC-001-A' (ID: 1) (heat_tracing_repository.py:620)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating load calculation for HT circuit 1 (heat_tracing_repository.py:729)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HT circuit 1 load calculation updated successfully (heat_tracing_repository.py:752)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating load calculation for HT circuit 1 (heat_tracing_repository.py:729)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HT circuit 1 load calculation updated successfully (heat_tracing_repository.py:752)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Calculating total load for feeder 1 (heat_tracing_repository.py:776)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total load for feeder 1: 1.25 kW (heat_tracing_repository.py:789)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving control circuits for switchboard 1: skip=0, limit=100 (heat_tracing_repository.py:833)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieved 1 control circuits for switchboard 1 (heat_tracing_repository.py:852)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving control circuits with limiting function for switchboard 1 (heat_tracing_repository.py:926)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 1 control circuits with limiting function (heat_tracing_repository.py:942)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HeatTracingRepository aggregate initialized (heat_tracing_repository.py:1016)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HeatTracingRepository aggregate initialized (heat_tracing_repository.py:1016)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Generating heat tracing summary for project 1 (heat_tracing_repository.py:1031)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting pipes for project 1 (heat_tracing_repository.py:286)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total pipes in project 1: 0 (heat_tracing_repository.py:296)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting vessels for project 1 (heat_tracing_repository.py:513)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total vessels in project 1: 0 (heat_tracing_repository.py:523)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes without circuits for project 1 (heat_tracing_repository.py:162)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 pipes without circuits (heat_tracing_repository.py:178)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving vessels without circuits for project 1 (heat_tracing_repository.py:426)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 vessels without circuits (heat_tracing_repository.py:442)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes with heat loss calculations for project 1 (heat_tracing_repository.py:199)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 pipes with heat loss calculations (heat_tracing_repository.py:217)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving vessels without circuits for project 1 (heat_tracing_repository.py:426)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 vessels without circuits (heat_tracing_repository.py:442)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Generated summary for project 1: {'project_id': 1, 'total_pipes': 0, 'total_vessels': 0, 'pipes_without_circuits': 0, 'vessels_without_circuits': 0, 'pipes_with_calculations': 0, 'vessels_with_calculations': 0} (heat_tracing_repository.py:1052)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HeatTracingRepository aggregate initialized (heat_tracing_repository.py:1016)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Checking design readiness for project 1 (heat_tracing_repository.py:1072)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Generating heat tracing summary for project 1 (heat_tracing_repository.py:1031)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting pipes for project 1 (heat_tracing_repository.py:286)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total pipes in project 1: 0 (heat_tracing_repository.py:296)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting vessels for project 1 (heat_tracing_repository.py:513)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total vessels in project 1: 0 (heat_tracing_repository.py:523)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes without circuits for project 1 (heat_tracing_repository.py:162)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 pipes without circuits (heat_tracing_repository.py:178)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving vessels without circuits for project 1 (heat_tracing_repository.py:426)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 vessels without circuits (heat_tracing_repository.py:442)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes with heat loss calculations for project 1 (heat_tracing_repository.py:199)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 pipes with heat loss calculations (heat_tracing_repository.py:217)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving vessels without circuits for project 1 (heat_tracing_repository.py:426)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 vessels without circuits (heat_tracing_repository.py:442)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Generated summary for project 1: {'project_id': 1, 'total_pipes': 0, 'total_vessels': 0, 'pipes_without_circuits': 0, 'vessels_without_circuits': 0, 'pipes_with_calculations': 0, 'vessels_with_calculations': 0} (heat_tracing_repository.py:1052)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Design readiness for project 1: 0.0% (heat_tracing_repository.py:1114)
2025-05-30 12:11:46 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:11:46 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Retrieving electrical nodes for project 1: skip=0, limit=100 (electrical_repository.py:82)
2025-05-30 12:11:46 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Retrieved 1 electrical nodes for project 1 (electrical_repository.py:101)
2025-05-30 12:11:46 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:11:46 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Retrieving electrical nodes for project 1: skip=0, limit=100 (electrical_repository.py:82)
2025-05-30 12:11:46 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - ERROR - Database error retrieving electrical nodes for project 1: Database error (electrical_repository.py:107)
2025-05-30 12:11:46 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:11:46 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Searching for electrical nodes with type: SWITCHBOARD_INCOMING in project 1 (electrical_repository.py:131)
2025-05-30 12:11:46 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Found 1 electrical nodes with type: SWITCHBOARD_INCOMING (electrical_repository.py:151)
2025-05-30 12:11:46 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Retrieving electrical nodes with capacity >= 50.0kVA for project 1 (electrical_repository.py:179)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Found 1 electrical nodes with sufficient capacity (electrical_repository.py:198)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Counting electrical nodes for project 1 (electrical_repository.py:265)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Total electrical nodes in project 1: 5 (electrical_repository.py:275)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Counting electrical nodes for project 1 (electrical_repository.py:265)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Total electrical nodes in project 1: None (electrical_repository.py:275)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:302)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Retrieving both cable routes for node 1 (electrical_repository.py:372)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Found 1 both cable routes for node 1 (electrical_repository.py:391)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:302)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:302)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Retrieving cable routes with installation method: CABLE_TRAY for project 1 (electrical_repository.py:419)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Found 1 cable routes with installation method: CABLE_TRAY (electrical_repository.py:437)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:302)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Retrieving cable routes with voltage drop > 3.0% for project 1 (electrical_repository.py:465)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Found 1 cable routes with high voltage drop (electrical_repository.py:488)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:743)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Retrieving load calculations for electrical node 2: skip=0, limit=100 (electrical_repository.py:810)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Retrieved 1 load calculations for electrical node 2 (electrical_repository.py:829)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:743)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Retrieving load calculations with type: heat_tracing for project 1 (electrical_repository.py:859)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Found 1 load calculations with type: heat_tracing (electrical_repository.py:879)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:743)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Calculating total power for electrical node 2 (electrical_repository.py:904)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Total power for electrical node 2: 5.5kW (electrical_repository.py:919)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:743)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Calculating total power for electrical node 2 (electrical_repository.py:904)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Total power for electrical node 2: 0.0kW (electrical_repository.py:919)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:948)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Retrieving voltage drop calculations for cable route 1: skip=0, limit=100 (electrical_repository.py:1015)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Retrieved 1 voltage drop calculations for cable route 1 (electrical_repository.py:1034)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:948)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Retrieving non-compliant voltage drop calculations for project 1 (electrical_repository.py:1063)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Found 1 non-compliant voltage drop calculations for project 1 (electrical_repository.py:1083)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:948)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Calculating average voltage drop for project 1 (electrical_repository.py:1158)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Average voltage drop for project 1: 3.2% (electrical_repository.py:1171)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:948)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Counting compliant voltage drop calculations for project 1 (electrical_repository.py:1197)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Compliant voltage drop calculations in project 1: 8 (electrical_repository.py:1212)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableSegmentRepository initialized (electrical_repository.py:730)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.services.electrical_service - DEBUG - ElectricalService initialized (electrical_service.py:148)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableSegmentRepository initialized (electrical_repository.py:730)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.services.electrical_service - DEBUG - ElectricalService initialized (electrical_service.py:148)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Starting voltage drop calculation for 20.8A load over 100.0m (electrical_service.py:236)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Voltage drop calculation completed: 0.9%, compliant: True (electrical_service.py:299)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableSegmentRepository initialized (electrical_repository.py:730)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.services.electrical_service - DEBUG - ElectricalService initialized (electrical_service.py:148)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Starting electrical standards validation for 1 standards (electrical_service.py:330)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Standards validation completed: compliant=True, violations=0, warnings=1 (electrical_service.py:376)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableSegmentRepository initialized (electrical_repository.py:730)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.services.electrical_service - DEBUG - ElectricalService initialized (electrical_service.py:148)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Calculating total load for electrical node 1 (electrical_service.py:457)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Load calculation completed for node 1: 6.0kW diversified load (electrical_service.py:544)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableSegmentRepository initialized (electrical_repository.py:730)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.services.electrical_service - DEBUG - ElectricalService initialized (electrical_service.py:148)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Calculating total load for electrical node 999 (electrical_service.py:457)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableSegmentRepository initialized (electrical_repository.py:730)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.services.electrical_service - DEBUG - ElectricalService initialized (electrical_service.py:148)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableSegmentRepository initialized (electrical_repository.py:730)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.services.electrical_service - DEBUG - ElectricalService initialized (electrical_service.py:148)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Starting voltage drop calculation for 20.8A load over 100.0m (electrical_service.py:236)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.services.electrical_service - ERROR - Voltage drop calculation failed: Voltage drop calculation failed (electrical_service.py:308)
Traceback (most recent call last):
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\electrical_service.py", line 242, in perform_voltage_drop_calculation
    voltage_drop_v = calculate_voltage_drop(
        current=inputs.load_current_a,
    ...<3 lines>...
        cable_reactance=inputs.cable_reactance_ohm_per_m,
    )
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1167, in __call__
    return self._mock_call(*args, **kwargs)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1171, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1226, in _execute_mock_call
    raise effect
Exception: Voltage drop calculation failed
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableSegmentRepository initialized (electrical_repository.py:730)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.services.electrical_service - DEBUG - ElectricalService initialized (electrical_service.py:148)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableSegmentRepository initialized (electrical_repository.py:730)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.services.electrical_service - DEBUG - ElectricalService initialized (electrical_service.py:148)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Starting cable sizing calculation for 5.0kW load (electrical_service.py:167)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Cable sizing calculation completed: HEATING_CABLE_20W, voltage drop: 0.9% (electrical_service.py:204)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableSegmentRepository initialized (electrical_repository.py:730)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.services.electrical_service - DEBUG - ElectricalService initialized (electrical_service.py:148)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Starting voltage drop calculation for 20.8A load over 100.0m (electrical_service.py:236)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Voltage drop calculation completed: 0.9%, compliant: True (electrical_service.py:299)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableSegmentRepository initialized (electrical_repository.py:730)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.services.electrical_service - DEBUG - ElectricalService initialized (electrical_service.py:148)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Starting electrical standards validation for 1 standards (electrical_service.py:330)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Standards validation completed: compliant=True, violations=0, warnings=1 (electrical_service.py:376)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableSegmentRepository initialized (electrical_repository.py:730)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.services.electrical_service - DEBUG - ElectricalService initialized (electrical_service.py:148)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Calculating total load for electrical node 1 (electrical_service.py:457)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Load calculation completed for node 1: 6.0kW diversified load (electrical_service.py:544)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableSegmentRepository initialized (electrical_repository.py:730)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.services.electrical_service - DEBUG - ElectricalService initialized (electrical_service.py:148)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Calculating total load for electrical node 999 (electrical_service.py:457)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableSegmentRepository initialized (electrical_repository.py:730)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.services.electrical_service - DEBUG - ElectricalService initialized (electrical_service.py:148)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Starting cable sizing calculation for 5.0kW load (electrical_service.py:167)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.services.electrical_service - ERROR - Cable sizing calculation failed: Calculation failed (electrical_service.py:213)
Traceback (most recent call last):
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\electrical_service.py", line 183, in perform_cable_sizing_calculation
    calc_result = self.calculation_service.calculate_cable_sizing(calc_input)
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1167, in __call__
    return self._mock_call(*args, **kwargs)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1171, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1226, in _execute_mock_call
    raise effect
Exception: Calculation failed
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableSegmentRepository initialized (electrical_repository.py:730)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.services.electrical_service - DEBUG - ElectricalService initialized (electrical_service.py:148)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Starting voltage drop calculation for 20.8A load over 100.0m (electrical_service.py:236)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.services.electrical_service - ERROR - Voltage drop calculation failed: Voltage drop calculation failed (electrical_service.py:308)
Traceback (most recent call last):
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\electrical_service.py", line 242, in perform_voltage_drop_calculation
    voltage_drop_v = calculate_voltage_drop(
        current=inputs.load_current_a,
    ...<3 lines>...
        cable_reactance=inputs.cable_reactance_ohm_per_m,
    )
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1167, in __call__
    return self._mock_call(*args, **kwargs)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1171, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1226, in _execute_mock_call
    raise effect
Exception: Voltage drop calculation failed
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableSegmentRepository initialized (electrical_repository.py:730)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.services.electrical_service - DEBUG - ElectricalService initialized (electrical_service.py:148)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Retrieving electrical nodes for project 1: skip=0, limit=100 (electrical_repository.py:82)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Retrieved 1 electrical nodes for project 1 (electrical_repository.py:101)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Retrieving electrical nodes for project 1: skip=0, limit=100 (electrical_repository.py:82)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - ERROR - Database error retrieving electrical nodes for project 1: Database error (electrical_repository.py:107)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Searching for electrical nodes with type: SWITCHBOARD_INCOMING in project 1 (electrical_repository.py:131)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Found 1 electrical nodes with type: SWITCHBOARD_INCOMING (electrical_repository.py:151)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Retrieving electrical nodes with capacity >= 50.0kVA for project 1 (electrical_repository.py:179)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Found 1 electrical nodes with sufficient capacity (electrical_repository.py:198)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Counting electrical nodes for project 1 (electrical_repository.py:265)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Total electrical nodes in project 1: 5 (electrical_repository.py:275)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Counting electrical nodes for project 1 (electrical_repository.py:265)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Total electrical nodes in project 1: None (electrical_repository.py:275)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Retrieving both cable routes for node 1 (electrical_repository.py:462)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Found 1 both cable routes for node 1 (electrical_repository.py:481)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Retrieving cable routes with installation method: CABLE_TRAY for project 1 (electrical_repository.py:509)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Found 1 cable routes with installation method: CABLE_TRAY (electrical_repository.py:527)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Retrieving cable routes with voltage drop > 3.0% for project 1 (electrical_repository.py:555)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Found 1 cable routes with high voltage drop (electrical_repository.py:578)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Retrieving load calculations for electrical node 2: skip=0, limit=100 (electrical_repository.py:992)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Retrieved 1 load calculations for electrical node 2 (electrical_repository.py:1011)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Retrieving load calculations with type: heat_tracing for project 1 (electrical_repository.py:1041)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Found 1 load calculations with type: heat_tracing (electrical_repository.py:1061)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Calculating total power for electrical node 2 (electrical_repository.py:1086)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Total power for electrical node 2: 5.5kW (electrical_repository.py:1101)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Calculating total power for electrical node 2 (electrical_repository.py:1086)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Total power for electrical node 2: 0.0kW (electrical_repository.py:1101)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Retrieving voltage drop calculations for cable route 1: skip=0, limit=100 (electrical_repository.py:1197)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Retrieved 1 voltage drop calculations for cable route 1 (electrical_repository.py:1216)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Retrieving non-compliant voltage drop calculations for project 1 (electrical_repository.py:1245)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Found 1 non-compliant voltage drop calculations for project 1 (electrical_repository.py:1265)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Calculating average voltage drop for project 1 (electrical_repository.py:1340)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Average voltage drop for project 1: 3.2% (electrical_repository.py:1353)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Counting compliant voltage drop calculations for project 1 (electrical_repository.py:1379)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Compliant voltage drop calculations in project 1: 8 (electrical_repository.py:1394)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableSegmentRepository initialized (electrical_repository.py:730)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.services.electrical_service - DEBUG - ElectricalService initialized (electrical_service.py:148)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Starting cable sizing calculation for 5.0kW load (electrical_service.py:167)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Cable sizing calculation completed: HEATING_CABLE_20W, voltage drop: 0.9% (electrical_service.py:204)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableSegmentRepository initialized (electrical_repository.py:730)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.services.electrical_service - DEBUG - ElectricalService initialized (electrical_service.py:148)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Starting voltage drop calculation for 20.8A load over 100.0m (electrical_service.py:236)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Voltage drop calculation completed: 0.9%, compliant: True (electrical_service.py:299)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableSegmentRepository initialized (electrical_repository.py:730)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.services.electrical_service - DEBUG - ElectricalService initialized (electrical_service.py:148)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Starting electrical standards validation for 1 standards (electrical_service.py:330)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Standards validation completed: compliant=True, violations=0, warnings=1 (electrical_service.py:376)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableSegmentRepository initialized (electrical_repository.py:730)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.services.electrical_service - DEBUG - ElectricalService initialized (electrical_service.py:148)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Calculating total load for electrical node 1 (electrical_service.py:457)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Load calculation completed for node 1: 6.0kW diversified load (electrical_service.py:544)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableSegmentRepository initialized (electrical_repository.py:730)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.services.electrical_service - DEBUG - ElectricalService initialized (electrical_service.py:148)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Calculating total load for electrical node 999 (electrical_service.py:457)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableSegmentRepository initialized (electrical_repository.py:730)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.services.electrical_service - DEBUG - ElectricalService initialized (electrical_service.py:148)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Starting cable sizing calculation for 5.0kW load (electrical_service.py:167)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.services.electrical_service - ERROR - Cable sizing calculation failed: Calculation failed (electrical_service.py:213)
Traceback (most recent call last):
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\electrical_service.py", line 183, in perform_cable_sizing_calculation
    calc_result = self.calculation_service.calculate_cable_sizing(calc_input)
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1167, in __call__
    return self._mock_call(*args, **kwargs)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1171, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1226, in _execute_mock_call
    raise effect
Exception: Calculation failed
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableSegmentRepository initialized (electrical_repository.py:730)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.services.electrical_service - DEBUG - ElectricalService initialized (electrical_service.py:148)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Starting voltage drop calculation for 20.8A load over 100.0m (electrical_service.py:236)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.services.electrical_service - ERROR - Voltage drop calculation failed: Voltage drop calculation failed (electrical_service.py:308)
Traceback (most recent call last):
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\electrical_service.py", line 242, in perform_voltage_drop_calculation
    voltage_drop_v = calculate_voltage_drop(
        current=inputs.load_current_a,
    ...<3 lines>...
        cable_reactance=inputs.cable_reactance_ohm_per_m,
    )
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1167, in __call__
    return self._mock_call(*args, **kwargs)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1171, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1226, in _execute_mock_call
    raise effect
Exception: Voltage drop calculation failed
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableSegmentRepository initialized (electrical_repository.py:730)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.services.electrical_service - DEBUG - ElectricalService initialized (electrical_service.py:148)
2025-06-01 21:10:55 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 21:10:55 - ultimate_electrical_designer.core.repositories.user_repository - DEBUG - UserRepository initialized (user_repository.py:51)
2025-06-01 21:11:55 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 21:11:55 - ultimate_electrical_designer.core.repositories.user_repository - DEBUG - UserRepository initialized (user_repository.py:51)
2025-06-01 21:13:16 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 21:13:16 - ultimate_electrical_designer.core.repositories.user_repository - DEBUG - UserRepository initialized (user_repository.py:51)
2025-06-01 21:14:00 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 21:14:00 - ultimate_electrical_designer.core.repositories.user_repository - DEBUG - UserRepository initialized (user_repository.py:51)
2025-06-01 21:55:57 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:06 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:06 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:06 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging event: CREATE, user_id=999, entity_type=EntityTypeEnum.PROJECT, entity_id=123 (activity_log_service.py:126)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging user action: user_id=1, action=INVALID_ACTION, entity_type=Project, entity_id=123 (activity_log_service.py:193)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging user action: user_id=1, action=CREATE, entity_type=InvalidEntity, entity_id=123 (activity_log_service.py:193)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Retrieving activity log: 999 (activity_log_service.py:331)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Retrieving activity logs for user 999: skip=0, limit=100 (activity_log_service.py:409)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging security event: event_type=UNAUTHORIZED_ACCESS, user_id=None, severity=INVALID_SEVERITY, threat_level=LOW (activity_log_service.py:553)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging security event: event_type=UNAUTHORIZED_ACCESS, user_id=None, severity=MEDIUM, threat_level=INVALID_LEVEL (activity_log_service.py:553)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Updating activity log: 999 (activity_log_service.py:791)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Deleting activity logs older than 365 days (activity_log_service.py:841)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Deleted 10 old activity logs (activity_log_service.py:852)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Deleting activity logs older than 0 days (activity_log_service.py:841)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Generating activity summary for user 1 from 2025-05-02 21:56:07.532442 to 2025-06-01 21:56:07.532447 (activity_log_service.py:752)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Generating activity summary for user 1 from 2025-06-01 21:56:07.534907 to 2025-05-31 21:56:07.534908 (activity_log_service.py:752)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging event: CREATE, user_id=None, entity_type=None, entity_id=None (activity_log_service.py:126)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - ERROR - Failed to log event CREATE: Database error (activity_log_service.py:163)
Traceback (most recent call last):
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\activity_log_service.py", line 149, in log_event
    activity_log = self.activity_log_repo.create(log_dict)
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1167, in __call__
    return self._mock_call(*args, **kwargs)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1171, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1226, in _execute_mock_call
    raise effect
sqlalchemy.exc.SQLAlchemyError: Database error
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging event: CREATE, user_id=None, entity_type=None, entity_id=None (activity_log_service.py:126)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - ERROR - Failed to log event CREATE: Repository error (activity_log_service.py:163)
Traceback (most recent call last):
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\activity_log_service.py", line 149, in log_event
    activity_log = self.activity_log_repo.create(log_dict)
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1167, in __call__
    return self._mock_call(*args, **kwargs)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1171, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1226, in _execute_mock_call
    raise effect
Exception: Repository error
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging event: CREATE, user_id=1, entity_type=EntityTypeEnum.PROJECT, entity_id=123 (activity_log_service.py:126)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Event logged successfully: CREATE, log_id=1 (activity_log_service.py:153)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging event: SYSTEM_START, user_id=None, entity_type=None, entity_id=None (activity_log_service.py:126)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Event logged successfully: SYSTEM_START, log_id=2 (activity_log_service.py:153)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging event: CREATE, user_id=999, entity_type=EntityTypeEnum.PROJECT, entity_id=123 (activity_log_service.py:126)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging user action: user_id=1, action=CREATE, entity_type=Project, entity_id=123 (activity_log_service.py:193)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging event: CREATE, user_id=1, entity_type=EntityTypeEnum.PROJECT, entity_id=123 (activity_log_service.py:126)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Event logged successfully: CREATE, log_id=1 (activity_log_service.py:153)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging user action: user_id=1, action=INVALID_ACTION, entity_type=Project, entity_id=123 (activity_log_service.py:193)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging user action: user_id=1, action=CREATE, entity_type=InvalidEntity, entity_id=123 (activity_log_service.py:193)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging authentication event: user_id=1, event_type=LOGIN, success=True (activity_log_service.py:248)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging event: LOGIN, user_id=1, entity_type=EntityTypeEnum.USER, entity_id=1 (activity_log_service.py:126)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Event logged successfully: LOGIN, log_id=1 (activity_log_service.py:153)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging authentication event: user_id=None, event_type=LOGIN, success=False (activity_log_service.py:248)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging event: LOGIN_FAILED, user_id=None, entity_type=EntityTypeEnum.USER, entity_id=None (activity_log_service.py:126)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Event logged successfully: LOGIN_FAILED, log_id=3 (activity_log_service.py:153)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging system event: event_type=SYSTEM_START (activity_log_service.py:291)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging event: SYSTEM_START, user_id=None, entity_type=None, entity_id=None (activity_log_service.py:126)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Event logged successfully: SYSTEM_START, log_id=1 (activity_log_service.py:153)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Retrieving activity log: 1 (activity_log_service.py:331)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Retrieving activity log: 999 (activity_log_service.py:331)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Retrieving activity logs for user 1: skip=0, limit=10 (activity_log_service.py:409)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Retrieving activity logs for user 999: skip=0, limit=100 (activity_log_service.py:409)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging security event: event_type=UNAUTHORIZED_ACCESS, user_id=1, severity=HIGH, threat_level=MEDIUM (activity_log_service.py:553)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging event: UNAUTHORIZED_ACCESS, user_id=1, entity_type=EntityTypeEnum.USER, entity_id=1 (activity_log_service.py:126)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Event logged successfully: UNAUTHORIZED_ACCESS, log_id=1 (activity_log_service.py:153)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging security event: event_type=UNAUTHORIZED_ACCESS, user_id=None, severity=INVALID_SEVERITY, threat_level=LOW (activity_log_service.py:553)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging security event: event_type=UNAUTHORIZED_ACCESS, user_id=None, severity=MEDIUM, threat_level=INVALID_LEVEL (activity_log_service.py:553)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Updating activity log: 1 (activity_log_service.py:791)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Activity log updated successfully: 1 (activity_log_service.py:814)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Updating activity log: 999 (activity_log_service.py:791)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Deleting activity logs older than 365 days (activity_log_service.py:841)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Deleted 10 old activity logs (activity_log_service.py:852)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Deleting activity logs older than 0 days (activity_log_service.py:841)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Generating audit report: type=user_activity, date_range=2025-05-02 21:56:58.494416 to 2025-06-01 21:56:58.494420 (activity_log_service.py:608)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Audit report generated successfully: c94e6fe8-6a71-433a-8aee-79ee2a703876, total_events=1 (activity_log_service.py:665)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Generating activity summary for user 1 from 2025-05-02 21:56:58.497279 to 2025-06-01 21:56:58.497282 (activity_log_service.py:752)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Generating activity summary for user 1 from 2025-06-01 21:56:58.499609 to 2025-05-31 21:56:58.499610 (activity_log_service.py:752)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging event: CREATE, user_id=None, entity_type=None, entity_id=None (activity_log_service.py:126)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - ERROR - Failed to log event CREATE: Database error (activity_log_service.py:163)
Traceback (most recent call last):
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\activity_log_service.py", line 149, in log_event
    activity_log = self.activity_log_repo.create(log_dict)
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1167, in __call__
    return self._mock_call(*args, **kwargs)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1171, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1226, in _execute_mock_call
    raise effect
sqlalchemy.exc.SQLAlchemyError: Database error
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging event: CREATE, user_id=None, entity_type=None, entity_id=None (activity_log_service.py:126)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - ERROR - Failed to log event CREATE: Repository error (activity_log_service.py:163)
Traceback (most recent call last):
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\activity_log_service.py", line 149, in log_event
    activity_log = self.activity_log_repo.create(log_dict)
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1167, in __call__
    return self._mock_call(*args, **kwargs)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1171, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1226, in _execute_mock_call
    raise effect
Exception: Repository error
2025-06-01 21:58:25 - ultimate_electrical_designer.api.v1.activity_log_routes - INFO - Creating activity log: EventTypeEnum.CREATE (activity_log_routes.py:132)
2025-06-01 21:58:25 - ultimate_electrical_designer.api.v1.activity_log_routes - INFO - Created activity log 1: EventTypeEnum.CREATE (activity_log_routes.py:144)
2025-06-01 21:58:34 - ultimate_electrical_designer.api.v1.activity_log_routes - INFO - Creating activity log: EventTypeEnum.CREATE (activity_log_routes.py:132)
2025-06-01 21:58:34 - ultimate_electrical_designer.api.v1.activity_log_routes - INFO - Created activity log 1: EventTypeEnum.CREATE (activity_log_routes.py:144)
2025-06-01 21:58:34 - ultimate_electrical_designer.api.v1.activity_log_routes - DEBUG - Retrieving activity log 1 (activity_log_routes.py:162)
2025-06-01 21:58:34 - ultimate_electrical_designer.api.v1.activity_log_routes - DEBUG - Retrieving activity log 999 (activity_log_routes.py:162)
2025-06-01 21:58:34 - ultimate_electrical_designer.api.v1.activity_log_routes - WARNING - Not found error: Activity log 999 not found (activity_log_routes.py:84)
2025-06-01 21:58:34 - ultimate_electrical_designer.api.v1.activity_log_routes - DEBUG - Listing activity logs: skip=0, limit=10 (activity_log_routes.py:203)
2025-06-01 21:58:34 - ultimate_electrical_designer.api.v1.activity_log_routes - DEBUG - Listing activity logs: skip=0, limit=10 (activity_log_routes.py:203)
2025-06-01 21:58:34 - ultimate_electrical_designer.api.v1.activity_log_routes - INFO - Updating activity log 1 (activity_log_routes.py:251)
2025-06-01 21:58:34 - ultimate_electrical_designer.api.v1.activity_log_routes - INFO - Updated activity log 1 (activity_log_routes.py:257)
2025-06-01 21:58:34 - ultimate_electrical_designer.api.v1.activity_log_routes - DEBUG - Retrieving activity logs for user 1: skip=0, limit=10 (activity_log_routes.py:284)
2025-06-01 21:58:34 - ultimate_electrical_designer.api.v1.activity_log_routes - DEBUG - Generating activity summary for user 1 from 2025-05-02 21:58:34.356552 to 2025-06-01 21:58:34.356559 (activity_log_routes.py:313)
2025-06-01 21:58:34 - ultimate_electrical_designer.api.v1.activity_log_routes - DEBUG - Retrieving activity logs for entity Project:123: skip=0, limit=10 (activity_log_routes.py:350)
2025-06-01 21:58:34 - ultimate_electrical_designer.api.v1.activity_log_routes - INFO - Logging security event: event_type=UNAUTHORIZED_ACCESS, user_id=1, severity=HIGH, threat_level=MEDIUM (activity_log_routes.py:421)
2025-06-01 21:58:34 - ultimate_electrical_designer.api.v1.activity_log_routes - INFO - Security event logged successfully: 1 (activity_log_routes.py:447)
2025-06-01 21:58:34 - ultimate_electrical_designer.api.v1.activity_log_routes - INFO - Deleting activity logs older than 365 days (activity_log_routes.py:567)
2025-06-01 21:58:34 - ultimate_electrical_designer.api.v1.activity_log_routes - INFO - Deleted 10 old activity logs (activity_log_routes.py:573)
2025-06-01 21:58:34 - ultimate_electrical_designer.api.v1.activity_log_routes - DEBUG - Retrieving activity log 1 (activity_log_routes.py:162)
2025-06-01 21:58:34 - ultimate_electrical_designer.api.v1.activity_log_routes - WARNING - Invalid input error: Invalid input provided (activity_log_routes.py:90)
2025-06-01 21:58:34 - ultimate_electrical_designer.api.v1.activity_log_routes - DEBUG - Retrieving activity log 1 (activity_log_routes.py:162)
2025-06-01 21:58:34 - ultimate_electrical_designer.api.v1.activity_log_routes - ERROR - Database error: Database connection failed (activity_log_routes.py:96)
2025-06-01 22:04:32 - ultimate_electrical_designer.api.v1.activity_log_routes - DEBUG - Retrieving security events for date range 2025-05-25 22:04:32.897309 to 2025-06-01 22:04:32.897317: skip=0, limit=10 (activity_log_routes.py:364)
2025-06-01 22:04:52 - ultimate_electrical_designer.api.v1.activity_log_routes - INFO - Creating activity log: EventTypeEnum.CREATE (activity_log_routes.py:132)
2025-06-01 22:04:52 - ultimate_electrical_designer.api.v1.activity_log_routes - INFO - Created activity log 1: EventTypeEnum.CREATE (activity_log_routes.py:144)
2025-06-01 22:04:52 - ultimate_electrical_designer.api.v1.activity_log_routes - DEBUG - Retrieving activity log 1 (activity_log_routes.py:577)
2025-06-01 22:04:52 - ultimate_electrical_designer.api.v1.activity_log_routes - DEBUG - Retrieving activity log 999 (activity_log_routes.py:577)
2025-06-01 22:04:52 - ultimate_electrical_designer.api.v1.activity_log_routes - WARNING - Not found error: Activity log 999 not found (activity_log_routes.py:84)
2025-06-01 22:04:52 - ultimate_electrical_designer.api.v1.activity_log_routes - DEBUG - Listing activity logs: skip=0, limit=10 (activity_log_routes.py:180)
2025-06-01 22:04:52 - ultimate_electrical_designer.api.v1.activity_log_routes - DEBUG - Listing activity logs: skip=0, limit=10 (activity_log_routes.py:180)
2025-06-01 22:04:52 - ultimate_electrical_designer.api.v1.activity_log_routes - INFO - Updating activity log 1 (activity_log_routes.py:228)
2025-06-01 22:04:52 - ultimate_electrical_designer.api.v1.activity_log_routes - INFO - Updated activity log 1 (activity_log_routes.py:234)
2025-06-01 22:04:52 - ultimate_electrical_designer.api.v1.activity_log_routes - DEBUG - Retrieving activity logs for user 1: skip=0, limit=10 (activity_log_routes.py:261)
2025-06-01 22:04:52 - ultimate_electrical_designer.api.v1.activity_log_routes - DEBUG - Generating activity summary for user 1 from 2025-05-02 22:04:52.144991 to 2025-06-01 22:04:52.144998 (activity_log_routes.py:290)
2025-06-01 22:04:52 - ultimate_electrical_designer.api.v1.activity_log_routes - DEBUG - Retrieving activity logs for entity Project:123: skip=0, limit=10 (activity_log_routes.py:327)
2025-06-01 22:04:52 - ultimate_electrical_designer.api.v1.activity_log_routes - DEBUG - Retrieving security events for date range 2025-05-25 22:04:52.173763 to 2025-06-01 22:04:52.173771: skip=0, limit=10 (activity_log_routes.py:364)
2025-06-01 22:04:52 - ultimate_electrical_designer.api.v1.activity_log_routes - INFO - Logging security event: event_type=UNAUTHORIZED_ACCESS, user_id=1, severity=HIGH, threat_level=MEDIUM (activity_log_routes.py:398)
2025-06-01 22:04:52 - ultimate_electrical_designer.api.v1.activity_log_routes - INFO - Security event logged successfully: 1 (activity_log_routes.py:424)
2025-06-01 22:04:52 - ultimate_electrical_designer.api.v1.activity_log_routes - DEBUG - Retrieving 50 most recent activity logs (activity_log_routes.py:490)
2025-06-01 22:04:52 - ultimate_electrical_designer.api.v1.activity_log_routes - DEBUG - Searching activity logs with term: project (activity_log_routes.py:517)
2025-06-01 22:04:52 - ultimate_electrical_designer.api.v1.activity_log_routes - INFO - Deleting activity logs older than 365 days (activity_log_routes.py:544)
2025-06-01 22:04:52 - ultimate_electrical_designer.api.v1.activity_log_routes - INFO - Deleted 10 old activity logs (activity_log_routes.py:550)
2025-06-01 22:04:52 - ultimate_electrical_designer.api.v1.activity_log_routes - DEBUG - Retrieving activity log 1 (activity_log_routes.py:577)
2025-06-01 22:04:52 - ultimate_electrical_designer.api.v1.activity_log_routes - WARNING - Invalid input error: Invalid input provided (activity_log_routes.py:90)
2025-06-01 22:04:52 - ultimate_electrical_designer.api.v1.activity_log_routes - DEBUG - Retrieving activity log 1 (activity_log_routes.py:577)
2025-06-01 22:04:52 - ultimate_electrical_designer.api.v1.activity_log_routes - ERROR - Database error: Database connection failed (activity_log_routes.py:96)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging event: CREATE, user_id=1, entity_type=EntityTypeEnum.PROJECT, entity_id=123 (activity_log_service.py:126)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Event logged successfully: CREATE, log_id=1 (activity_log_service.py:153)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging event: SYSTEM_START, user_id=None, entity_type=None, entity_id=None (activity_log_service.py:126)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Event logged successfully: SYSTEM_START, log_id=2 (activity_log_service.py:153)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging event: CREATE, user_id=999, entity_type=EntityTypeEnum.PROJECT, entity_id=123 (activity_log_service.py:126)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging user action: user_id=1, action=CREATE, entity_type=Project, entity_id=123 (activity_log_service.py:193)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging event: CREATE, user_id=1, entity_type=EntityTypeEnum.PROJECT, entity_id=123 (activity_log_service.py:126)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Event logged successfully: CREATE, log_id=1 (activity_log_service.py:153)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging user action: user_id=1, action=INVALID_ACTION, entity_type=Project, entity_id=123 (activity_log_service.py:193)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging user action: user_id=1, action=CREATE, entity_type=InvalidEntity, entity_id=123 (activity_log_service.py:193)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging authentication event: user_id=1, event_type=LOGIN, success=True (activity_log_service.py:248)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging event: LOGIN, user_id=1, entity_type=EntityTypeEnum.USER, entity_id=1 (activity_log_service.py:126)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Event logged successfully: LOGIN, log_id=1 (activity_log_service.py:153)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging authentication event: user_id=None, event_type=LOGIN, success=False (activity_log_service.py:248)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging event: LOGIN_FAILED, user_id=None, entity_type=EntityTypeEnum.USER, entity_id=None (activity_log_service.py:126)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Event logged successfully: LOGIN_FAILED, log_id=3 (activity_log_service.py:153)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging system event: event_type=SYSTEM_START (activity_log_service.py:291)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging event: SYSTEM_START, user_id=None, entity_type=None, entity_id=None (activity_log_service.py:126)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Event logged successfully: SYSTEM_START, log_id=1 (activity_log_service.py:153)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Retrieving activity log: 1 (activity_log_service.py:331)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Retrieving activity log: 999 (activity_log_service.py:331)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Retrieving activity logs for user 1: skip=0, limit=10 (activity_log_service.py:409)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Retrieving activity logs for user 999: skip=0, limit=100 (activity_log_service.py:409)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging security event: event_type=UNAUTHORIZED_ACCESS, user_id=1, severity=HIGH, threat_level=MEDIUM (activity_log_service.py:553)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging event: UNAUTHORIZED_ACCESS, user_id=1, entity_type=EntityTypeEnum.USER, entity_id=1 (activity_log_service.py:126)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Event logged successfully: UNAUTHORIZED_ACCESS, log_id=1 (activity_log_service.py:153)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging security event: event_type=UNAUTHORIZED_ACCESS, user_id=None, severity=INVALID_SEVERITY, threat_level=LOW (activity_log_service.py:553)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging security event: event_type=UNAUTHORIZED_ACCESS, user_id=None, severity=MEDIUM, threat_level=INVALID_LEVEL (activity_log_service.py:553)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Updating activity log: 1 (activity_log_service.py:791)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Activity log updated successfully: 1 (activity_log_service.py:814)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Updating activity log: 999 (activity_log_service.py:791)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Deleting activity logs older than 365 days (activity_log_service.py:841)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Deleted 10 old activity logs (activity_log_service.py:852)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Deleting activity logs older than 0 days (activity_log_service.py:841)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Generating audit report: type=user_activity, date_range=2025-05-02 22:05:03.340494 to 2025-06-01 22:05:03.340497 (activity_log_service.py:608)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Audit report generated successfully: e24b091d-8f26-445a-ac49-aee5aca6ba78, total_events=1 (activity_log_service.py:665)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Generating activity summary for user 1 from 2025-05-02 22:05:03.343312 to 2025-06-01 22:05:03.343316 (activity_log_service.py:752)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Generating activity summary for user 1 from 2025-06-01 22:05:03.345689 to 2025-05-31 22:05:03.345690 (activity_log_service.py:752)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging event: CREATE, user_id=None, entity_type=None, entity_id=None (activity_log_service.py:126)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - ERROR - Failed to log event CREATE: Database error (activity_log_service.py:163)
Traceback (most recent call last):
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\activity_log_service.py", line 149, in log_event
    activity_log = self.activity_log_repo.create(log_dict)
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1167, in __call__
    return self._mock_call(*args, **kwargs)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1171, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1226, in _execute_mock_call
    raise effect
sqlalchemy.exc.SQLAlchemyError: Database error
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging event: CREATE, user_id=None, entity_type=None, entity_id=None (activity_log_service.py:126)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - ERROR - Failed to log event CREATE: Repository error (activity_log_service.py:163)
Traceback (most recent call last):
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\activity_log_service.py", line 149, in log_event
    activity_log = self.activity_log_repo.create(log_dict)
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1167, in __call__
    return self._mock_call(*args, **kwargs)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1171, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1226, in _execute_mock_call
    raise effect
Exception: Repository error
2025-06-01 22:05:03 - ultimate_electrical_designer.api.v1.activity_log_routes - INFO - Creating activity log: EventTypeEnum.CREATE (activity_log_routes.py:132)
2025-06-01 22:05:03 - ultimate_electrical_designer.api.v1.activity_log_routes - INFO - Created activity log 1: EventTypeEnum.CREATE (activity_log_routes.py:144)
2025-06-01 22:05:03 - ultimate_electrical_designer.api.v1.activity_log_routes - DEBUG - Retrieving activity log 1 (activity_log_routes.py:577)
2025-06-01 22:05:03 - ultimate_electrical_designer.api.v1.activity_log_routes - DEBUG - Retrieving activity log 999 (activity_log_routes.py:577)
2025-06-01 22:05:03 - ultimate_electrical_designer.api.v1.activity_log_routes - WARNING - Not found error: Activity log 999 not found (activity_log_routes.py:84)
2025-06-01 22:05:03 - ultimate_electrical_designer.api.v1.activity_log_routes - DEBUG - Listing activity logs: skip=0, limit=10 (activity_log_routes.py:180)
2025-06-01 22:05:03 - ultimate_electrical_designer.api.v1.activity_log_routes - DEBUG - Listing activity logs: skip=0, limit=10 (activity_log_routes.py:180)
2025-06-01 22:05:03 - ultimate_electrical_designer.api.v1.activity_log_routes - INFO - Updating activity log 1 (activity_log_routes.py:228)
2025-06-01 22:05:03 - ultimate_electrical_designer.api.v1.activity_log_routes - INFO - Updated activity log 1 (activity_log_routes.py:234)
2025-06-01 22:05:03 - ultimate_electrical_designer.api.v1.activity_log_routes - DEBUG - Retrieving activity logs for user 1: skip=0, limit=10 (activity_log_routes.py:261)
2025-06-01 22:05:03 - ultimate_electrical_designer.api.v1.activity_log_routes - DEBUG - Generating activity summary for user 1 from 2025-05-02 22:05:03.619519 to 2025-06-01 22:05:03.619527 (activity_log_routes.py:290)
2025-06-01 22:05:03 - ultimate_electrical_designer.api.v1.activity_log_routes - DEBUG - Retrieving activity logs for entity Project:123: skip=0, limit=10 (activity_log_routes.py:327)
2025-06-01 22:05:03 - ultimate_electrical_designer.api.v1.activity_log_routes - DEBUG - Retrieving security events for date range 2025-05-25 22:05:03.656986 to 2025-06-01 22:05:03.657000: skip=0, limit=10 (activity_log_routes.py:364)
2025-06-01 22:05:03 - ultimate_electrical_designer.api.v1.activity_log_routes - INFO - Logging security event: event_type=UNAUTHORIZED_ACCESS, user_id=1, severity=HIGH, threat_level=MEDIUM (activity_log_routes.py:398)
2025-06-01 22:05:03 - ultimate_electrical_designer.api.v1.activity_log_routes - INFO - Security event logged successfully: 1 (activity_log_routes.py:424)
2025-06-01 22:05:03 - ultimate_electrical_designer.api.v1.activity_log_routes - DEBUG - Retrieving 50 most recent activity logs (activity_log_routes.py:490)
2025-06-01 22:05:03 - ultimate_electrical_designer.api.v1.activity_log_routes - DEBUG - Searching activity logs with term: project (activity_log_routes.py:517)
2025-06-01 22:05:03 - ultimate_electrical_designer.api.v1.activity_log_routes - INFO - Deleting activity logs older than 365 days (activity_log_routes.py:544)
2025-06-01 22:05:03 - ultimate_electrical_designer.api.v1.activity_log_routes - INFO - Deleted 10 old activity logs (activity_log_routes.py:550)
2025-06-01 22:05:03 - ultimate_electrical_designer.api.v1.activity_log_routes - DEBUG - Retrieving activity log 1 (activity_log_routes.py:577)
2025-06-01 22:05:03 - ultimate_electrical_designer.api.v1.activity_log_routes - WARNING - Invalid input error: Invalid input provided (activity_log_routes.py:90)
2025-06-01 22:05:03 - ultimate_electrical_designer.api.v1.activity_log_routes - DEBUG - Retrieving activity log 1 (activity_log_routes.py:577)
2025-06-01 22:05:03 - ultimate_electrical_designer.api.v1.activity_log_routes - ERROR - Database error: Database connection failed (activity_log_routes.py:96)

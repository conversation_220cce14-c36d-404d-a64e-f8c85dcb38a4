2025-05-30 11:35:41 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:35:41 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:35:41 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:35:41 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:35:41 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HeatTracingRepository aggregate initialized (heat_tracing_repository.py:1016)
2025-05-30 11:36:53 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:36:54 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:36:54 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:36:54 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:36:54 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:36:54 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:36:54 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Searching for pipe with line tag: NON-EXISTENT in project 1 (heat_tracing_repository.py:123)
2025-05-30 11:36:54 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - ERROR - Database error searching for pipe line tag NON-EXISTENT: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[User(User)]'. Original exception was: When initializing mapper Mapper[User(User)], expression 'ActivityLog' failed to locate a name ('ActivityLog'). If this is a class name, consider adding this relationship() to the <class 'core.models.users.User'> class after both dependent classes have been defined. (heat_tracing_repository.py:145)
2025-05-30 11:36:55 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:36:55 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:36:55 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:36:55 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating heat loss calculation for pipe 999 (heat_tracing_repository.py:242)
2025-05-30 11:36:55 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:36:55 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting pipes for project 1 (heat_tracing_repository.py:286)
2025-05-30 11:36:55 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - ERROR - Database error counting pipes for project 1: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[User(User)]'. Original exception was: When initializing mapper Mapper[User(User)], expression 'ActivityLog' failed to locate a name ('ActivityLog'). If this is a class name, consider adding this relationship() to the <class 'core.models.users.User'> class after both dependent classes have been defined. (heat_tracing_repository.py:300)
2025-05-30 11:36:56 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:36:56 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:36:56 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:36:56 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:36:56 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:36:56 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:36:56 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:36:56 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:36:56 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:36:56 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:36:56 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:36:56 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HeatTracingRepository aggregate initialized (heat_tracing_repository.py:1016)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HeatTracingRepository aggregate initialized (heat_tracing_repository.py:1016)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Generating heat tracing summary for project 1 (heat_tracing_repository.py:1031)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting pipes for project 1 (heat_tracing_repository.py:286)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - ERROR - Database error counting pipes for project 1: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[User(User)]'. Original exception was: When initializing mapper Mapper[User(User)], expression 'ActivityLog' failed to locate a name ('ActivityLog'). If this is a class name, consider adding this relationship() to the <class 'core.models.users.User'> class after both dependent classes have been defined. (heat_tracing_repository.py:300)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - ERROR - Error generating project summary for 1: An unexpected database error occurred during pipe operation. (heat_tracing_repository.py:1056)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HeatTracingRepository aggregate initialized (heat_tracing_repository.py:1016)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Checking design readiness for project 1 (heat_tracing_repository.py:1072)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Generating heat tracing summary for project 1 (heat_tracing_repository.py:1031)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting pipes for project 1 (heat_tracing_repository.py:286)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - ERROR - Database error counting pipes for project 1: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[User(User)]'. Original exception was: When initializing mapper Mapper[User(User)], expression 'ActivityLog' failed to locate a name ('ActivityLog'). If this is a class name, consider adding this relationship() to the <class 'core.models.users.User'> class after both dependent classes have been defined. (heat_tracing_repository.py:300)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - ERROR - Error generating project summary for 1: An unexpected database error occurred during pipe operation. (heat_tracing_repository.py:1056)
2025-05-30 11:36:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - ERROR - Error checking design readiness for project 1: An unexpected database error occurred during pipe operation. (heat_tracing_repository.py:1120)
2025-05-30 11:37:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:37:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:37:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:37:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:37:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HeatTracingRepository aggregate initialized (heat_tracing_repository.py:1016)
2025-05-30 11:37:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:38:36 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:39:06 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:39:50 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:41:06 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:42:47 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:42:56 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes for project 1: skip=0, limit=100 (heat_tracing_repository.py:80)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieved 2 pipes for project 1 (heat_tracing_repository.py:99)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Searching for pipe with line tag: L-001 in project 1 (heat_tracing_repository.py:123)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found pipe: 'Test Pipe 001' (ID: 1) (heat_tracing_repository.py:138)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Searching for pipe with line tag: NON-EXISTENT in project 1 (heat_tracing_repository.py:123)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Pipe not found with line tag: NON-EXISTENT (heat_tracing_repository.py:140)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes without circuits for project 1 (heat_tracing_repository.py:162)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 1 pipes without circuits (heat_tracing_repository.py:178)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating heat loss calculation for pipe 1 (heat_tracing_repository.py:242)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Pipe 1 heat loss calculation updated successfully (heat_tracing_repository.py:264)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating heat loss calculation for pipe 999 (heat_tracing_repository.py:242)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Pipe 999 not found for heat loss update (heat_tracing_repository.py:248)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting pipes for project 1 (heat_tracing_repository.py:286)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total pipes in project 1: 0 (heat_tracing_repository.py:296)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting pipes for project 1 (heat_tracing_repository.py:286)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total pipes in project 1: 1 (heat_tracing_repository.py:296)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating heat loss calculation for pipe 1 (heat_tracing_repository.py:242)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Pipe 1 heat loss calculation updated successfully (heat_tracing_repository.py:264)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes with heat loss calculations for project 1 (heat_tracing_repository.py:199)
2025-05-30 11:42:57 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 1 pipes with heat loss calculations (heat_tracing_repository.py:217)
2025-05-30 11:43:07 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:43:07 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:43:07 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Searching for vessel with equipment tag: T-001 in project 1 (heat_tracing_repository.py:385)
2025-05-30 11:43:07 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found vessel: 'Test Vessel T-001' (ID: 1) (heat_tracing_repository.py:400)
2025-05-30 11:43:07 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:43:07 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving vessels without circuits for project 1 (heat_tracing_repository.py:426)
2025-05-30 11:43:07 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 1 vessels without circuits (heat_tracing_repository.py:442)
2025-05-30 11:43:07 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:43:07 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating heat loss calculation for vessel 1 (heat_tracing_repository.py:467)
2025-05-30 11:43:07 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Vessel 1 heat loss calculation updated successfully (heat_tracing_repository.py:489)
2025-05-30 11:43:16 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:43:16 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:43:16 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving HT circuits for feeder 1: skip=0, limit=100 (heat_tracing_repository.py:569)
2025-05-30 11:43:16 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieved 1 HT circuits for feeder 1 (heat_tracing_repository.py:588)
2025-05-30 11:43:16 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:43:16 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Searching for HT circuit for pipe 1 (heat_tracing_repository.py:611)
2025-05-30 11:43:16 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found HT circuit: 'HTC-001-A' (ID: 1) (heat_tracing_repository.py:620)
2025-05-30 11:43:16 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:43:16 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating load calculation for HT circuit 1 (heat_tracing_repository.py:729)
2025-05-30 11:43:16 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HT circuit 1 load calculation updated successfully (heat_tracing_repository.py:752)
2025-05-30 11:43:16 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:43:16 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating load calculation for HT circuit 1 (heat_tracing_repository.py:729)
2025-05-30 11:43:16 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HT circuit 1 load calculation updated successfully (heat_tracing_repository.py:752)
2025-05-30 11:43:16 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Calculating total load for feeder 1 (heat_tracing_repository.py:776)
2025-05-30 11:43:16 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total load for feeder 1: 1.25 kW (heat_tracing_repository.py:789)
2025-05-30 11:43:24 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:43:25 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:43:25 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:43:54 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:43:55 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:43:55 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:44:21 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:44:21 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:44:21 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving control circuits for switchboard 1: skip=0, limit=100 (heat_tracing_repository.py:833)
2025-05-30 11:44:21 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieved 1 control circuits for switchboard 1 (heat_tracing_repository.py:852)
2025-05-30 11:44:21 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:44:21 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving control circuits with limiting function for switchboard 1 (heat_tracing_repository.py:926)
2025-05-30 11:44:21 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 1 control circuits with limiting function (heat_tracing_repository.py:942)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HeatTracingRepository aggregate initialized (heat_tracing_repository.py:1016)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HeatTracingRepository aggregate initialized (heat_tracing_repository.py:1016)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Generating heat tracing summary for project 1 (heat_tracing_repository.py:1031)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting pipes for project 1 (heat_tracing_repository.py:286)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total pipes in project 1: 0 (heat_tracing_repository.py:296)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting vessels for project 1 (heat_tracing_repository.py:513)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total vessels in project 1: 0 (heat_tracing_repository.py:523)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes without circuits for project 1 (heat_tracing_repository.py:162)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 pipes without circuits (heat_tracing_repository.py:178)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving vessels without circuits for project 1 (heat_tracing_repository.py:426)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 vessels without circuits (heat_tracing_repository.py:442)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes with heat loss calculations for project 1 (heat_tracing_repository.py:199)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 pipes with heat loss calculations (heat_tracing_repository.py:217)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving vessels without circuits for project 1 (heat_tracing_repository.py:426)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 vessels without circuits (heat_tracing_repository.py:442)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Generated summary for project 1: {'project_id': 1, 'total_pipes': 0, 'total_vessels': 0, 'pipes_without_circuits': 0, 'vessels_without_circuits': 0, 'pipes_with_calculations': 0, 'vessels_with_calculations': 0} (heat_tracing_repository.py:1052)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HeatTracingRepository aggregate initialized (heat_tracing_repository.py:1016)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Checking design readiness for project 1 (heat_tracing_repository.py:1072)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Generating heat tracing summary for project 1 (heat_tracing_repository.py:1031)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting pipes for project 1 (heat_tracing_repository.py:286)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total pipes in project 1: 0 (heat_tracing_repository.py:296)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting vessels for project 1 (heat_tracing_repository.py:513)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total vessels in project 1: 0 (heat_tracing_repository.py:523)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes without circuits for project 1 (heat_tracing_repository.py:162)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 pipes without circuits (heat_tracing_repository.py:178)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving vessels without circuits for project 1 (heat_tracing_repository.py:426)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 vessels without circuits (heat_tracing_repository.py:442)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes with heat loss calculations for project 1 (heat_tracing_repository.py:199)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 pipes with heat loss calculations (heat_tracing_repository.py:217)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving vessels without circuits for project 1 (heat_tracing_repository.py:426)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 vessels without circuits (heat_tracing_repository.py:442)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Generated summary for project 1: {'project_id': 1, 'total_pipes': 0, 'total_vessels': 0, 'pipes_without_circuits': 0, 'vessels_without_circuits': 0, 'pipes_with_calculations': 0, 'vessels_with_calculations': 0} (heat_tracing_repository.py:1052)
2025-05-30 11:44:30 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Design readiness for project 1: 0.0% (heat_tracing_repository.py:1114)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes for project 1: skip=0, limit=100 (heat_tracing_repository.py:80)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieved 2 pipes for project 1 (heat_tracing_repository.py:99)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Searching for pipe with line tag: L-001 in project 1 (heat_tracing_repository.py:123)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found pipe: 'Test Pipe 001' (ID: 1) (heat_tracing_repository.py:138)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Searching for pipe with line tag: NON-EXISTENT in project 1 (heat_tracing_repository.py:123)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Pipe not found with line tag: NON-EXISTENT (heat_tracing_repository.py:140)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes without circuits for project 1 (heat_tracing_repository.py:162)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 1 pipes without circuits (heat_tracing_repository.py:178)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating heat loss calculation for pipe 1 (heat_tracing_repository.py:242)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Pipe 1 heat loss calculation updated successfully (heat_tracing_repository.py:264)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating heat loss calculation for pipe 999 (heat_tracing_repository.py:242)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Pipe 999 not found for heat loss update (heat_tracing_repository.py:248)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting pipes for project 1 (heat_tracing_repository.py:286)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total pipes in project 1: 0 (heat_tracing_repository.py:296)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting pipes for project 1 (heat_tracing_repository.py:286)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total pipes in project 1: 1 (heat_tracing_repository.py:296)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating heat loss calculation for pipe 1 (heat_tracing_repository.py:242)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Pipe 1 heat loss calculation updated successfully (heat_tracing_repository.py:264)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes with heat loss calculations for project 1 (heat_tracing_repository.py:199)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 1 pipes with heat loss calculations (heat_tracing_repository.py:217)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Searching for vessel with equipment tag: T-001 in project 1 (heat_tracing_repository.py:385)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found vessel: 'Test Vessel T-001' (ID: 1) (heat_tracing_repository.py:400)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving vessels without circuits for project 1 (heat_tracing_repository.py:426)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 1 vessels without circuits (heat_tracing_repository.py:442)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating heat loss calculation for vessel 1 (heat_tracing_repository.py:467)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Vessel 1 heat loss calculation updated successfully (heat_tracing_repository.py:489)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving HT circuits for feeder 1: skip=0, limit=100 (heat_tracing_repository.py:569)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieved 1 HT circuits for feeder 1 (heat_tracing_repository.py:588)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Searching for HT circuit for pipe 1 (heat_tracing_repository.py:611)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found HT circuit: 'HTC-001-A' (ID: 1) (heat_tracing_repository.py:620)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating load calculation for HT circuit 1 (heat_tracing_repository.py:729)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HT circuit 1 load calculation updated successfully (heat_tracing_repository.py:752)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating load calculation for HT circuit 1 (heat_tracing_repository.py:729)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HT circuit 1 load calculation updated successfully (heat_tracing_repository.py:752)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Calculating total load for feeder 1 (heat_tracing_repository.py:776)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total load for feeder 1: 1.25 kW (heat_tracing_repository.py:789)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving control circuits for switchboard 1: skip=0, limit=100 (heat_tracing_repository.py:833)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieved 1 control circuits for switchboard 1 (heat_tracing_repository.py:852)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving control circuits with limiting function for switchboard 1 (heat_tracing_repository.py:926)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 1 control circuits with limiting function (heat_tracing_repository.py:942)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HeatTracingRepository aggregate initialized (heat_tracing_repository.py:1016)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HeatTracingRepository aggregate initialized (heat_tracing_repository.py:1016)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Generating heat tracing summary for project 1 (heat_tracing_repository.py:1031)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting pipes for project 1 (heat_tracing_repository.py:286)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total pipes in project 1: 0 (heat_tracing_repository.py:296)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting vessels for project 1 (heat_tracing_repository.py:513)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total vessels in project 1: 0 (heat_tracing_repository.py:523)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes without circuits for project 1 (heat_tracing_repository.py:162)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 pipes without circuits (heat_tracing_repository.py:178)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving vessels without circuits for project 1 (heat_tracing_repository.py:426)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 vessels without circuits (heat_tracing_repository.py:442)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes with heat loss calculations for project 1 (heat_tracing_repository.py:199)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 pipes with heat loss calculations (heat_tracing_repository.py:217)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving vessels without circuits for project 1 (heat_tracing_repository.py:426)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 vessels without circuits (heat_tracing_repository.py:442)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Generated summary for project 1: {'project_id': 1, 'total_pipes': 0, 'total_vessels': 0, 'pipes_without_circuits': 0, 'vessels_without_circuits': 0, 'pipes_with_calculations': 0, 'vessels_with_calculations': 0} (heat_tracing_repository.py:1052)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HeatTracingRepository aggregate initialized (heat_tracing_repository.py:1016)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Checking design readiness for project 1 (heat_tracing_repository.py:1072)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Generating heat tracing summary for project 1 (heat_tracing_repository.py:1031)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting pipes for project 1 (heat_tracing_repository.py:286)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total pipes in project 1: 0 (heat_tracing_repository.py:296)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting vessels for project 1 (heat_tracing_repository.py:513)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total vessels in project 1: 0 (heat_tracing_repository.py:523)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes without circuits for project 1 (heat_tracing_repository.py:162)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 pipes without circuits (heat_tracing_repository.py:178)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving vessels without circuits for project 1 (heat_tracing_repository.py:426)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 vessels without circuits (heat_tracing_repository.py:442)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes with heat loss calculations for project 1 (heat_tracing_repository.py:199)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 pipes with heat loss calculations (heat_tracing_repository.py:217)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving vessels without circuits for project 1 (heat_tracing_repository.py:426)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 vessels without circuits (heat_tracing_repository.py:442)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Generated summary for project 1: {'project_id': 1, 'total_pipes': 0, 'total_vessels': 0, 'pipes_without_circuits': 0, 'vessels_without_circuits': 0, 'pipes_with_calculations': 0, 'vessels_with_calculations': 0} (heat_tracing_repository.py:1052)
2025-05-30 11:44:44 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Design readiness for project 1: 0.0% (heat_tracing_repository.py:1114)
2025-05-30 11:45:14 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes for project 1: skip=0, limit=100 (heat_tracing_repository.py:80)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieved 2 pipes for project 1 (heat_tracing_repository.py:99)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Searching for pipe with line tag: L-001 in project 1 (heat_tracing_repository.py:123)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found pipe: 'Test Pipe 001' (ID: 1) (heat_tracing_repository.py:138)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Searching for pipe with line tag: NON-EXISTENT in project 1 (heat_tracing_repository.py:123)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Pipe not found with line tag: NON-EXISTENT (heat_tracing_repository.py:140)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes without circuits for project 1 (heat_tracing_repository.py:162)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 1 pipes without circuits (heat_tracing_repository.py:178)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating heat loss calculation for pipe 1 (heat_tracing_repository.py:242)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Pipe 1 heat loss calculation updated successfully (heat_tracing_repository.py:264)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating heat loss calculation for pipe 999 (heat_tracing_repository.py:242)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Pipe 999 not found for heat loss update (heat_tracing_repository.py:248)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting pipes for project 1 (heat_tracing_repository.py:286)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total pipes in project 1: 0 (heat_tracing_repository.py:296)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting pipes for project 1 (heat_tracing_repository.py:286)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total pipes in project 1: 1 (heat_tracing_repository.py:296)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating heat loss calculation for pipe 1 (heat_tracing_repository.py:242)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Pipe 1 heat loss calculation updated successfully (heat_tracing_repository.py:264)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes with heat loss calculations for project 1 (heat_tracing_repository.py:199)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 1 pipes with heat loss calculations (heat_tracing_repository.py:217)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Searching for vessel with equipment tag: T-001 in project 1 (heat_tracing_repository.py:385)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found vessel: 'Test Vessel T-001' (ID: 1) (heat_tracing_repository.py:400)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving vessels without circuits for project 1 (heat_tracing_repository.py:426)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 1 vessels without circuits (heat_tracing_repository.py:442)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating heat loss calculation for vessel 1 (heat_tracing_repository.py:467)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Vessel 1 heat loss calculation updated successfully (heat_tracing_repository.py:489)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving HT circuits for feeder 1: skip=0, limit=100 (heat_tracing_repository.py:569)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieved 1 HT circuits for feeder 1 (heat_tracing_repository.py:588)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Searching for HT circuit for pipe 1 (heat_tracing_repository.py:611)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found HT circuit: 'HTC-001-A' (ID: 1) (heat_tracing_repository.py:620)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating load calculation for HT circuit 1 (heat_tracing_repository.py:729)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HT circuit 1 load calculation updated successfully (heat_tracing_repository.py:752)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating load calculation for HT circuit 1 (heat_tracing_repository.py:729)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HT circuit 1 load calculation updated successfully (heat_tracing_repository.py:752)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Calculating total load for feeder 1 (heat_tracing_repository.py:776)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total load for feeder 1: 1.25 kW (heat_tracing_repository.py:789)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving control circuits for switchboard 1: skip=0, limit=100 (heat_tracing_repository.py:833)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieved 1 control circuits for switchboard 1 (heat_tracing_repository.py:852)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving control circuits with limiting function for switchboard 1 (heat_tracing_repository.py:926)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 1 control circuits with limiting function (heat_tracing_repository.py:942)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HeatTracingRepository aggregate initialized (heat_tracing_repository.py:1016)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HeatTracingRepository aggregate initialized (heat_tracing_repository.py:1016)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Generating heat tracing summary for project 1 (heat_tracing_repository.py:1031)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting pipes for project 1 (heat_tracing_repository.py:286)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total pipes in project 1: 0 (heat_tracing_repository.py:296)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting vessels for project 1 (heat_tracing_repository.py:513)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total vessels in project 1: 0 (heat_tracing_repository.py:523)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes without circuits for project 1 (heat_tracing_repository.py:162)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 pipes without circuits (heat_tracing_repository.py:178)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving vessels without circuits for project 1 (heat_tracing_repository.py:426)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 vessels without circuits (heat_tracing_repository.py:442)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes with heat loss calculations for project 1 (heat_tracing_repository.py:199)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 pipes with heat loss calculations (heat_tracing_repository.py:217)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving vessels without circuits for project 1 (heat_tracing_repository.py:426)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 vessels without circuits (heat_tracing_repository.py:442)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Generated summary for project 1: {'project_id': 1, 'total_pipes': 0, 'total_vessels': 0, 'pipes_without_circuits': 0, 'vessels_without_circuits': 0, 'pipes_with_calculations': 0, 'vessels_with_calculations': 0} (heat_tracing_repository.py:1052)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HeatTracingRepository aggregate initialized (heat_tracing_repository.py:1016)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Checking design readiness for project 1 (heat_tracing_repository.py:1072)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Generating heat tracing summary for project 1 (heat_tracing_repository.py:1031)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting pipes for project 1 (heat_tracing_repository.py:286)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total pipes in project 1: 0 (heat_tracing_repository.py:296)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting vessels for project 1 (heat_tracing_repository.py:513)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total vessels in project 1: 0 (heat_tracing_repository.py:523)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes without circuits for project 1 (heat_tracing_repository.py:162)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 pipes without circuits (heat_tracing_repository.py:178)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving vessels without circuits for project 1 (heat_tracing_repository.py:426)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 vessels without circuits (heat_tracing_repository.py:442)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes with heat loss calculations for project 1 (heat_tracing_repository.py:199)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 pipes with heat loss calculations (heat_tracing_repository.py:217)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving vessels without circuits for project 1 (heat_tracing_repository.py:426)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 vessels without circuits (heat_tracing_repository.py:442)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Generated summary for project 1: {'project_id': 1, 'total_pipes': 0, 'total_vessels': 0, 'pipes_without_circuits': 0, 'vessels_without_circuits': 0, 'pipes_with_calculations': 0, 'vessels_with_calculations': 0} (heat_tracing_repository.py:1052)
2025-05-30 11:45:15 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Design readiness for project 1: 0.0% (heat_tracing_repository.py:1114)
2025-05-30 12:11:46 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:11:46 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Retrieving electrical nodes for project 1: skip=0, limit=100 (electrical_repository.py:82)
2025-05-30 12:11:46 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Retrieved 1 electrical nodes for project 1 (electrical_repository.py:101)
2025-05-30 12:11:46 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:11:46 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Retrieving electrical nodes for project 1: skip=0, limit=100 (electrical_repository.py:82)
2025-05-30 12:11:46 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - ERROR - Database error retrieving electrical nodes for project 1: Database error (electrical_repository.py:107)
2025-05-30 12:11:46 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:11:46 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Searching for electrical nodes with type: SWITCHBOARD_INCOMING in project 1 (electrical_repository.py:131)
2025-05-30 12:11:46 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Found 1 electrical nodes with type: SWITCHBOARD_INCOMING (electrical_repository.py:151)
2025-05-30 12:11:46 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Retrieving electrical nodes with capacity >= 50.0kVA for project 1 (electrical_repository.py:179)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Found 1 electrical nodes with sufficient capacity (electrical_repository.py:198)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Counting electrical nodes for project 1 (electrical_repository.py:265)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Total electrical nodes in project 1: 5 (electrical_repository.py:275)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Counting electrical nodes for project 1 (electrical_repository.py:265)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Total electrical nodes in project 1: None (electrical_repository.py:275)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:302)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Retrieving both cable routes for node 1 (electrical_repository.py:372)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Found 1 both cable routes for node 1 (electrical_repository.py:391)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:302)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:302)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Retrieving cable routes with installation method: CABLE_TRAY for project 1 (electrical_repository.py:419)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Found 1 cable routes with installation method: CABLE_TRAY (electrical_repository.py:437)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:302)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Retrieving cable routes with voltage drop > 3.0% for project 1 (electrical_repository.py:465)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Found 1 cable routes with high voltage drop (electrical_repository.py:488)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:743)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Retrieving load calculations for electrical node 2: skip=0, limit=100 (electrical_repository.py:810)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Retrieved 1 load calculations for electrical node 2 (electrical_repository.py:829)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:743)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Retrieving load calculations with type: heat_tracing for project 1 (electrical_repository.py:859)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Found 1 load calculations with type: heat_tracing (electrical_repository.py:879)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:743)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Calculating total power for electrical node 2 (electrical_repository.py:904)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Total power for electrical node 2: 5.5kW (electrical_repository.py:919)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:743)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Calculating total power for electrical node 2 (electrical_repository.py:904)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Total power for electrical node 2: 0.0kW (electrical_repository.py:919)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:948)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Retrieving voltage drop calculations for cable route 1: skip=0, limit=100 (electrical_repository.py:1015)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Retrieved 1 voltage drop calculations for cable route 1 (electrical_repository.py:1034)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:948)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Retrieving non-compliant voltage drop calculations for project 1 (electrical_repository.py:1063)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Found 1 non-compliant voltage drop calculations for project 1 (electrical_repository.py:1083)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:948)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Calculating average voltage drop for project 1 (electrical_repository.py:1158)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Average voltage drop for project 1: 3.2% (electrical_repository.py:1171)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:948)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Counting compliant voltage drop calculations for project 1 (electrical_repository.py:1197)
2025-05-30 12:11:47 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Compliant voltage drop calculations in project 1: 8 (electrical_repository.py:1212)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableSegmentRepository initialized (electrical_repository.py:730)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.services.electrical_service - DEBUG - ElectricalService initialized (electrical_service.py:148)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableSegmentRepository initialized (electrical_repository.py:730)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.services.electrical_service - DEBUG - ElectricalService initialized (electrical_service.py:148)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Starting voltage drop calculation for 20.8A load over 100.0m (electrical_service.py:236)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Voltage drop calculation completed: 0.9%, compliant: True (electrical_service.py:299)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableSegmentRepository initialized (electrical_repository.py:730)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.services.electrical_service - DEBUG - ElectricalService initialized (electrical_service.py:148)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Starting electrical standards validation for 1 standards (electrical_service.py:330)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Standards validation completed: compliant=True, violations=0, warnings=1 (electrical_service.py:376)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableSegmentRepository initialized (electrical_repository.py:730)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.services.electrical_service - DEBUG - ElectricalService initialized (electrical_service.py:148)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Calculating total load for electrical node 1 (electrical_service.py:457)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Load calculation completed for node 1: 6.0kW diversified load (electrical_service.py:544)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableSegmentRepository initialized (electrical_repository.py:730)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.services.electrical_service - DEBUG - ElectricalService initialized (electrical_service.py:148)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Calculating total load for electrical node 999 (electrical_service.py:457)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableSegmentRepository initialized (electrical_repository.py:730)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.services.electrical_service - DEBUG - ElectricalService initialized (electrical_service.py:148)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableSegmentRepository initialized (electrical_repository.py:730)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.services.electrical_service - DEBUG - ElectricalService initialized (electrical_service.py:148)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Starting voltage drop calculation for 20.8A load over 100.0m (electrical_service.py:236)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.services.electrical_service - ERROR - Voltage drop calculation failed: Voltage drop calculation failed (electrical_service.py:308)
Traceback (most recent call last):
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\electrical_service.py", line 242, in perform_voltage_drop_calculation
    voltage_drop_v = calculate_voltage_drop(
        current=inputs.load_current_a,
    ...<3 lines>...
        cable_reactance=inputs.cable_reactance_ohm_per_m,
    )
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1167, in __call__
    return self._mock_call(*args, **kwargs)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1171, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1226, in _execute_mock_call
    raise effect
Exception: Voltage drop calculation failed
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableSegmentRepository initialized (electrical_repository.py:730)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-05-30 12:24:43 - ultimate_electrical_designer.backend.core.services.electrical_service - DEBUG - ElectricalService initialized (electrical_service.py:148)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableSegmentRepository initialized (electrical_repository.py:730)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.services.electrical_service - DEBUG - ElectricalService initialized (electrical_service.py:148)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Starting cable sizing calculation for 5.0kW load (electrical_service.py:167)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Cable sizing calculation completed: HEATING_CABLE_20W, voltage drop: 0.9% (electrical_service.py:204)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableSegmentRepository initialized (electrical_repository.py:730)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.services.electrical_service - DEBUG - ElectricalService initialized (electrical_service.py:148)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Starting voltage drop calculation for 20.8A load over 100.0m (electrical_service.py:236)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Voltage drop calculation completed: 0.9%, compliant: True (electrical_service.py:299)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableSegmentRepository initialized (electrical_repository.py:730)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.services.electrical_service - DEBUG - ElectricalService initialized (electrical_service.py:148)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Starting electrical standards validation for 1 standards (electrical_service.py:330)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Standards validation completed: compliant=True, violations=0, warnings=1 (electrical_service.py:376)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableSegmentRepository initialized (electrical_repository.py:730)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.services.electrical_service - DEBUG - ElectricalService initialized (electrical_service.py:148)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Calculating total load for electrical node 1 (electrical_service.py:457)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Load calculation completed for node 1: 6.0kW diversified load (electrical_service.py:544)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableSegmentRepository initialized (electrical_repository.py:730)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.services.electrical_service - DEBUG - ElectricalService initialized (electrical_service.py:148)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Calculating total load for electrical node 999 (electrical_service.py:457)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableSegmentRepository initialized (electrical_repository.py:730)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.services.electrical_service - DEBUG - ElectricalService initialized (electrical_service.py:148)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Starting cable sizing calculation for 5.0kW load (electrical_service.py:167)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.services.electrical_service - ERROR - Cable sizing calculation failed: Calculation failed (electrical_service.py:213)
Traceback (most recent call last):
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\electrical_service.py", line 183, in perform_cable_sizing_calculation
    calc_result = self.calculation_service.calculate_cable_sizing(calc_input)
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1167, in __call__
    return self._mock_call(*args, **kwargs)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1171, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1226, in _execute_mock_call
    raise effect
Exception: Calculation failed
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableSegmentRepository initialized (electrical_repository.py:730)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.services.electrical_service - DEBUG - ElectricalService initialized (electrical_service.py:148)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Starting voltage drop calculation for 20.8A load over 100.0m (electrical_service.py:236)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.services.electrical_service - ERROR - Voltage drop calculation failed: Voltage drop calculation failed (electrical_service.py:308)
Traceback (most recent call last):
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\electrical_service.py", line 242, in perform_voltage_drop_calculation
    voltage_drop_v = calculate_voltage_drop(
        current=inputs.load_current_a,
    ...<3 lines>...
        cable_reactance=inputs.cable_reactance_ohm_per_m,
    )
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1167, in __call__
    return self._mock_call(*args, **kwargs)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1171, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1226, in _execute_mock_call
    raise effect
Exception: Voltage drop calculation failed
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableSegmentRepository initialized (electrical_repository.py:730)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-05-30 12:25:30 - ultimate_electrical_designer.backend.core.services.electrical_service - DEBUG - ElectricalService initialized (electrical_service.py:148)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Retrieving electrical nodes for project 1: skip=0, limit=100 (electrical_repository.py:82)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Retrieved 1 electrical nodes for project 1 (electrical_repository.py:101)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Retrieving electrical nodes for project 1: skip=0, limit=100 (electrical_repository.py:82)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - ERROR - Database error retrieving electrical nodes for project 1: Database error (electrical_repository.py:107)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Searching for electrical nodes with type: SWITCHBOARD_INCOMING in project 1 (electrical_repository.py:131)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Found 1 electrical nodes with type: SWITCHBOARD_INCOMING (electrical_repository.py:151)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Retrieving electrical nodes with capacity >= 50.0kVA for project 1 (electrical_repository.py:179)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Found 1 electrical nodes with sufficient capacity (electrical_repository.py:198)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Counting electrical nodes for project 1 (electrical_repository.py:265)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Total electrical nodes in project 1: 5 (electrical_repository.py:275)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Counting electrical nodes for project 1 (electrical_repository.py:265)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Total electrical nodes in project 1: None (electrical_repository.py:275)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Retrieving both cable routes for node 1 (electrical_repository.py:462)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Found 1 both cable routes for node 1 (electrical_repository.py:481)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Retrieving cable routes with installation method: CABLE_TRAY for project 1 (electrical_repository.py:509)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Found 1 cable routes with installation method: CABLE_TRAY (electrical_repository.py:527)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Retrieving cable routes with voltage drop > 3.0% for project 1 (electrical_repository.py:555)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Found 1 cable routes with high voltage drop (electrical_repository.py:578)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Retrieving load calculations for electrical node 2: skip=0, limit=100 (electrical_repository.py:992)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Retrieved 1 load calculations for electrical node 2 (electrical_repository.py:1011)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Retrieving load calculations with type: heat_tracing for project 1 (electrical_repository.py:1041)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Found 1 load calculations with type: heat_tracing (electrical_repository.py:1061)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Calculating total power for electrical node 2 (electrical_repository.py:1086)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Total power for electrical node 2: 5.5kW (electrical_repository.py:1101)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Calculating total power for electrical node 2 (electrical_repository.py:1086)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Total power for electrical node 2: 0.0kW (electrical_repository.py:1101)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Retrieving voltage drop calculations for cable route 1: skip=0, limit=100 (electrical_repository.py:1197)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Retrieved 1 voltage drop calculations for cable route 1 (electrical_repository.py:1216)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Retrieving non-compliant voltage drop calculations for project 1 (electrical_repository.py:1245)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Found 1 non-compliant voltage drop calculations for project 1 (electrical_repository.py:1265)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Calculating average voltage drop for project 1 (electrical_repository.py:1340)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Average voltage drop for project 1: 3.2% (electrical_repository.py:1353)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Counting compliant voltage drop calculations for project 1 (electrical_repository.py:1379)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - Compliant voltage drop calculations in project 1: 8 (electrical_repository.py:1394)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableSegmentRepository initialized (electrical_repository.py:730)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.services.electrical_service - DEBUG - ElectricalService initialized (electrical_service.py:148)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Starting cable sizing calculation for 5.0kW load (electrical_service.py:167)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Cable sizing calculation completed: HEATING_CABLE_20W, voltage drop: 0.9% (electrical_service.py:204)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableSegmentRepository initialized (electrical_repository.py:730)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.services.electrical_service - DEBUG - ElectricalService initialized (electrical_service.py:148)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Starting voltage drop calculation for 20.8A load over 100.0m (electrical_service.py:236)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Voltage drop calculation completed: 0.9%, compliant: True (electrical_service.py:299)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableSegmentRepository initialized (electrical_repository.py:730)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.services.electrical_service - DEBUG - ElectricalService initialized (electrical_service.py:148)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Starting electrical standards validation for 1 standards (electrical_service.py:330)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Standards validation completed: compliant=True, violations=0, warnings=1 (electrical_service.py:376)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableSegmentRepository initialized (electrical_repository.py:730)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.services.electrical_service - DEBUG - ElectricalService initialized (electrical_service.py:148)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Calculating total load for electrical node 1 (electrical_service.py:457)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Load calculation completed for node 1: 6.0kW diversified load (electrical_service.py:544)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableSegmentRepository initialized (electrical_repository.py:730)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.services.electrical_service - DEBUG - ElectricalService initialized (electrical_service.py:148)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Calculating total load for electrical node 999 (electrical_service.py:457)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableSegmentRepository initialized (electrical_repository.py:730)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.services.electrical_service - DEBUG - ElectricalService initialized (electrical_service.py:148)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Starting cable sizing calculation for 5.0kW load (electrical_service.py:167)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.services.electrical_service - ERROR - Cable sizing calculation failed: Calculation failed (electrical_service.py:213)
Traceback (most recent call last):
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\electrical_service.py", line 183, in perform_cable_sizing_calculation
    calc_result = self.calculation_service.calculate_cable_sizing(calc_input)
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1167, in __call__
    return self._mock_call(*args, **kwargs)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1171, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1226, in _execute_mock_call
    raise effect
Exception: Calculation failed
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableSegmentRepository initialized (electrical_repository.py:730)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.services.electrical_service - DEBUG - ElectricalService initialized (electrical_service.py:148)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.services.electrical_service - INFO - Starting voltage drop calculation for 20.8A load over 100.0m (electrical_service.py:236)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.services.electrical_service - ERROR - Voltage drop calculation failed: Voltage drop calculation failed (electrical_service.py:308)
Traceback (most recent call last):
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\electrical_service.py", line 242, in perform_voltage_drop_calculation
    voltage_drop_v = calculate_voltage_drop(
        current=inputs.load_current_a,
    ...<3 lines>...
        cable_reactance=inputs.cable_reactance_ohm_per_m,
    )
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1167, in __call__
    return self._mock_call(*args, **kwargs)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1171, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1226, in _execute_mock_call
    raise effect
Exception: Voltage drop calculation failed
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - CableSegmentRepository initialized (electrical_repository.py:730)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-05-30 12:25:58 - ultimate_electrical_designer.backend.core.services.electrical_service - DEBUG - ElectricalService initialized (electrical_service.py:148)
2025-06-01 21:10:55 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 21:10:55 - ultimate_electrical_designer.core.repositories.user_repository - DEBUG - UserRepository initialized (user_repository.py:51)
2025-06-01 21:11:55 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 21:11:55 - ultimate_electrical_designer.core.repositories.user_repository - DEBUG - UserRepository initialized (user_repository.py:51)
2025-06-01 21:13:16 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 21:13:16 - ultimate_electrical_designer.core.repositories.user_repository - DEBUG - UserRepository initialized (user_repository.py:51)
2025-06-01 21:14:00 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 21:14:00 - ultimate_electrical_designer.core.repositories.user_repository - DEBUG - UserRepository initialized (user_repository.py:51)
2025-06-01 21:55:57 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:06 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:06 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:06 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging event: CREATE, user_id=999, entity_type=EntityTypeEnum.PROJECT, entity_id=123 (activity_log_service.py:126)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging user action: user_id=1, action=INVALID_ACTION, entity_type=Project, entity_id=123 (activity_log_service.py:193)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging user action: user_id=1, action=CREATE, entity_type=InvalidEntity, entity_id=123 (activity_log_service.py:193)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Retrieving activity log: 999 (activity_log_service.py:331)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Retrieving activity logs for user 999: skip=0, limit=100 (activity_log_service.py:409)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging security event: event_type=UNAUTHORIZED_ACCESS, user_id=None, severity=INVALID_SEVERITY, threat_level=LOW (activity_log_service.py:553)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging security event: event_type=UNAUTHORIZED_ACCESS, user_id=None, severity=MEDIUM, threat_level=INVALID_LEVEL (activity_log_service.py:553)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Updating activity log: 999 (activity_log_service.py:791)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Deleting activity logs older than 365 days (activity_log_service.py:841)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Deleted 10 old activity logs (activity_log_service.py:852)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Deleting activity logs older than 0 days (activity_log_service.py:841)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Generating activity summary for user 1 from 2025-05-02 21:56:07.532442 to 2025-06-01 21:56:07.532447 (activity_log_service.py:752)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Generating activity summary for user 1 from 2025-06-01 21:56:07.534907 to 2025-05-31 21:56:07.534908 (activity_log_service.py:752)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging event: CREATE, user_id=None, entity_type=None, entity_id=None (activity_log_service.py:126)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - ERROR - Failed to log event CREATE: Database error (activity_log_service.py:163)
Traceback (most recent call last):
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\activity_log_service.py", line 149, in log_event
    activity_log = self.activity_log_repo.create(log_dict)
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1167, in __call__
    return self._mock_call(*args, **kwargs)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1171, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1226, in _execute_mock_call
    raise effect
sqlalchemy.exc.SQLAlchemyError: Database error
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging event: CREATE, user_id=None, entity_type=None, entity_id=None (activity_log_service.py:126)
2025-06-01 21:56:07 - ultimate_electrical_designer.core.services.activity_log_service - ERROR - Failed to log event CREATE: Repository error (activity_log_service.py:163)
Traceback (most recent call last):
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\activity_log_service.py", line 149, in log_event
    activity_log = self.activity_log_repo.create(log_dict)
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1167, in __call__
    return self._mock_call(*args, **kwargs)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1171, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1226, in _execute_mock_call
    raise effect
Exception: Repository error
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging event: CREATE, user_id=1, entity_type=EntityTypeEnum.PROJECT, entity_id=123 (activity_log_service.py:126)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Event logged successfully: CREATE, log_id=1 (activity_log_service.py:153)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging event: SYSTEM_START, user_id=None, entity_type=None, entity_id=None (activity_log_service.py:126)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Event logged successfully: SYSTEM_START, log_id=2 (activity_log_service.py:153)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging event: CREATE, user_id=999, entity_type=EntityTypeEnum.PROJECT, entity_id=123 (activity_log_service.py:126)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging user action: user_id=1, action=CREATE, entity_type=Project, entity_id=123 (activity_log_service.py:193)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging event: CREATE, user_id=1, entity_type=EntityTypeEnum.PROJECT, entity_id=123 (activity_log_service.py:126)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Event logged successfully: CREATE, log_id=1 (activity_log_service.py:153)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging user action: user_id=1, action=INVALID_ACTION, entity_type=Project, entity_id=123 (activity_log_service.py:193)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging user action: user_id=1, action=CREATE, entity_type=InvalidEntity, entity_id=123 (activity_log_service.py:193)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging authentication event: user_id=1, event_type=LOGIN, success=True (activity_log_service.py:248)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging event: LOGIN, user_id=1, entity_type=EntityTypeEnum.USER, entity_id=1 (activity_log_service.py:126)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Event logged successfully: LOGIN, log_id=1 (activity_log_service.py:153)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging authentication event: user_id=None, event_type=LOGIN, success=False (activity_log_service.py:248)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging event: LOGIN_FAILED, user_id=None, entity_type=EntityTypeEnum.USER, entity_id=None (activity_log_service.py:126)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Event logged successfully: LOGIN_FAILED, log_id=3 (activity_log_service.py:153)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging system event: event_type=SYSTEM_START (activity_log_service.py:291)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging event: SYSTEM_START, user_id=None, entity_type=None, entity_id=None (activity_log_service.py:126)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Event logged successfully: SYSTEM_START, log_id=1 (activity_log_service.py:153)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Retrieving activity log: 1 (activity_log_service.py:331)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Retrieving activity log: 999 (activity_log_service.py:331)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Retrieving activity logs for user 1: skip=0, limit=10 (activity_log_service.py:409)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Retrieving activity logs for user 999: skip=0, limit=100 (activity_log_service.py:409)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging security event: event_type=UNAUTHORIZED_ACCESS, user_id=1, severity=HIGH, threat_level=MEDIUM (activity_log_service.py:553)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging event: UNAUTHORIZED_ACCESS, user_id=1, entity_type=EntityTypeEnum.USER, entity_id=1 (activity_log_service.py:126)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Event logged successfully: UNAUTHORIZED_ACCESS, log_id=1 (activity_log_service.py:153)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging security event: event_type=UNAUTHORIZED_ACCESS, user_id=None, severity=INVALID_SEVERITY, threat_level=LOW (activity_log_service.py:553)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging security event: event_type=UNAUTHORIZED_ACCESS, user_id=None, severity=MEDIUM, threat_level=INVALID_LEVEL (activity_log_service.py:553)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Updating activity log: 1 (activity_log_service.py:791)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Activity log updated successfully: 1 (activity_log_service.py:814)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Updating activity log: 999 (activity_log_service.py:791)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Deleting activity logs older than 365 days (activity_log_service.py:841)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Deleted 10 old activity logs (activity_log_service.py:852)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Deleting activity logs older than 0 days (activity_log_service.py:841)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Generating audit report: type=user_activity, date_range=2025-05-02 21:56:58.494416 to 2025-06-01 21:56:58.494420 (activity_log_service.py:608)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Audit report generated successfully: c94e6fe8-6a71-433a-8aee-79ee2a703876, total_events=1 (activity_log_service.py:665)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Generating activity summary for user 1 from 2025-05-02 21:56:58.497279 to 2025-06-01 21:56:58.497282 (activity_log_service.py:752)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Generating activity summary for user 1 from 2025-06-01 21:56:58.499609 to 2025-05-31 21:56:58.499610 (activity_log_service.py:752)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging event: CREATE, user_id=None, entity_type=None, entity_id=None (activity_log_service.py:126)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - ERROR - Failed to log event CREATE: Database error (activity_log_service.py:163)
Traceback (most recent call last):
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\activity_log_service.py", line 149, in log_event
    activity_log = self.activity_log_repo.create(log_dict)
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1167, in __call__
    return self._mock_call(*args, **kwargs)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1171, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1226, in _execute_mock_call
    raise effect
sqlalchemy.exc.SQLAlchemyError: Database error
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging event: CREATE, user_id=None, entity_type=None, entity_id=None (activity_log_service.py:126)
2025-06-01 21:56:58 - ultimate_electrical_designer.core.services.activity_log_service - ERROR - Failed to log event CREATE: Repository error (activity_log_service.py:163)
Traceback (most recent call last):
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\activity_log_service.py", line 149, in log_event
    activity_log = self.activity_log_repo.create(log_dict)
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1167, in __call__
    return self._mock_call(*args, **kwargs)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1171, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1226, in _execute_mock_call
    raise effect
Exception: Repository error
2025-06-01 21:58:25 - ultimate_electrical_designer.api.v1.activity_log_routes - INFO - Creating activity log: EventTypeEnum.CREATE (activity_log_routes.py:132)
2025-06-01 21:58:25 - ultimate_electrical_designer.api.v1.activity_log_routes - INFO - Created activity log 1: EventTypeEnum.CREATE (activity_log_routes.py:144)
2025-06-01 21:58:34 - ultimate_electrical_designer.api.v1.activity_log_routes - INFO - Creating activity log: EventTypeEnum.CREATE (activity_log_routes.py:132)
2025-06-01 21:58:34 - ultimate_electrical_designer.api.v1.activity_log_routes - INFO - Created activity log 1: EventTypeEnum.CREATE (activity_log_routes.py:144)
2025-06-01 21:58:34 - ultimate_electrical_designer.api.v1.activity_log_routes - DEBUG - Retrieving activity log 1 (activity_log_routes.py:162)
2025-06-01 21:58:34 - ultimate_electrical_designer.api.v1.activity_log_routes - DEBUG - Retrieving activity log 999 (activity_log_routes.py:162)
2025-06-01 21:58:34 - ultimate_electrical_designer.api.v1.activity_log_routes - WARNING - Not found error: Activity log 999 not found (activity_log_routes.py:84)
2025-06-01 21:58:34 - ultimate_electrical_designer.api.v1.activity_log_routes - DEBUG - Listing activity logs: skip=0, limit=10 (activity_log_routes.py:203)
2025-06-01 21:58:34 - ultimate_electrical_designer.api.v1.activity_log_routes - DEBUG - Listing activity logs: skip=0, limit=10 (activity_log_routes.py:203)
2025-06-01 21:58:34 - ultimate_electrical_designer.api.v1.activity_log_routes - INFO - Updating activity log 1 (activity_log_routes.py:251)
2025-06-01 21:58:34 - ultimate_electrical_designer.api.v1.activity_log_routes - INFO - Updated activity log 1 (activity_log_routes.py:257)
2025-06-01 21:58:34 - ultimate_electrical_designer.api.v1.activity_log_routes - DEBUG - Retrieving activity logs for user 1: skip=0, limit=10 (activity_log_routes.py:284)
2025-06-01 21:58:34 - ultimate_electrical_designer.api.v1.activity_log_routes - DEBUG - Generating activity summary for user 1 from 2025-05-02 21:58:34.356552 to 2025-06-01 21:58:34.356559 (activity_log_routes.py:313)
2025-06-01 21:58:34 - ultimate_electrical_designer.api.v1.activity_log_routes - DEBUG - Retrieving activity logs for entity Project:123: skip=0, limit=10 (activity_log_routes.py:350)
2025-06-01 21:58:34 - ultimate_electrical_designer.api.v1.activity_log_routes - INFO - Logging security event: event_type=UNAUTHORIZED_ACCESS, user_id=1, severity=HIGH, threat_level=MEDIUM (activity_log_routes.py:421)
2025-06-01 21:58:34 - ultimate_electrical_designer.api.v1.activity_log_routes - INFO - Security event logged successfully: 1 (activity_log_routes.py:447)
2025-06-01 21:58:34 - ultimate_electrical_designer.api.v1.activity_log_routes - INFO - Deleting activity logs older than 365 days (activity_log_routes.py:567)
2025-06-01 21:58:34 - ultimate_electrical_designer.api.v1.activity_log_routes - INFO - Deleted 10 old activity logs (activity_log_routes.py:573)
2025-06-01 21:58:34 - ultimate_electrical_designer.api.v1.activity_log_routes - DEBUG - Retrieving activity log 1 (activity_log_routes.py:162)
2025-06-01 21:58:34 - ultimate_electrical_designer.api.v1.activity_log_routes - WARNING - Invalid input error: Invalid input provided (activity_log_routes.py:90)
2025-06-01 21:58:34 - ultimate_electrical_designer.api.v1.activity_log_routes - DEBUG - Retrieving activity log 1 (activity_log_routes.py:162)
2025-06-01 21:58:34 - ultimate_electrical_designer.api.v1.activity_log_routes - ERROR - Database error: Database connection failed (activity_log_routes.py:96)
2025-06-01 22:04:32 - ultimate_electrical_designer.api.v1.activity_log_routes - DEBUG - Retrieving security events for date range 2025-05-25 22:04:32.897309 to 2025-06-01 22:04:32.897317: skip=0, limit=10 (activity_log_routes.py:364)
2025-06-01 22:04:52 - ultimate_electrical_designer.api.v1.activity_log_routes - INFO - Creating activity log: EventTypeEnum.CREATE (activity_log_routes.py:132)
2025-06-01 22:04:52 - ultimate_electrical_designer.api.v1.activity_log_routes - INFO - Created activity log 1: EventTypeEnum.CREATE (activity_log_routes.py:144)
2025-06-01 22:04:52 - ultimate_electrical_designer.api.v1.activity_log_routes - DEBUG - Retrieving activity log 1 (activity_log_routes.py:577)
2025-06-01 22:04:52 - ultimate_electrical_designer.api.v1.activity_log_routes - DEBUG - Retrieving activity log 999 (activity_log_routes.py:577)
2025-06-01 22:04:52 - ultimate_electrical_designer.api.v1.activity_log_routes - WARNING - Not found error: Activity log 999 not found (activity_log_routes.py:84)
2025-06-01 22:04:52 - ultimate_electrical_designer.api.v1.activity_log_routes - DEBUG - Listing activity logs: skip=0, limit=10 (activity_log_routes.py:180)
2025-06-01 22:04:52 - ultimate_electrical_designer.api.v1.activity_log_routes - DEBUG - Listing activity logs: skip=0, limit=10 (activity_log_routes.py:180)
2025-06-01 22:04:52 - ultimate_electrical_designer.api.v1.activity_log_routes - INFO - Updating activity log 1 (activity_log_routes.py:228)
2025-06-01 22:04:52 - ultimate_electrical_designer.api.v1.activity_log_routes - INFO - Updated activity log 1 (activity_log_routes.py:234)
2025-06-01 22:04:52 - ultimate_electrical_designer.api.v1.activity_log_routes - DEBUG - Retrieving activity logs for user 1: skip=0, limit=10 (activity_log_routes.py:261)
2025-06-01 22:04:52 - ultimate_electrical_designer.api.v1.activity_log_routes - DEBUG - Generating activity summary for user 1 from 2025-05-02 22:04:52.144991 to 2025-06-01 22:04:52.144998 (activity_log_routes.py:290)
2025-06-01 22:04:52 - ultimate_electrical_designer.api.v1.activity_log_routes - DEBUG - Retrieving activity logs for entity Project:123: skip=0, limit=10 (activity_log_routes.py:327)
2025-06-01 22:04:52 - ultimate_electrical_designer.api.v1.activity_log_routes - DEBUG - Retrieving security events for date range 2025-05-25 22:04:52.173763 to 2025-06-01 22:04:52.173771: skip=0, limit=10 (activity_log_routes.py:364)
2025-06-01 22:04:52 - ultimate_electrical_designer.api.v1.activity_log_routes - INFO - Logging security event: event_type=UNAUTHORIZED_ACCESS, user_id=1, severity=HIGH, threat_level=MEDIUM (activity_log_routes.py:398)
2025-06-01 22:04:52 - ultimate_electrical_designer.api.v1.activity_log_routes - INFO - Security event logged successfully: 1 (activity_log_routes.py:424)
2025-06-01 22:04:52 - ultimate_electrical_designer.api.v1.activity_log_routes - DEBUG - Retrieving 50 most recent activity logs (activity_log_routes.py:490)
2025-06-01 22:04:52 - ultimate_electrical_designer.api.v1.activity_log_routes - DEBUG - Searching activity logs with term: project (activity_log_routes.py:517)
2025-06-01 22:04:52 - ultimate_electrical_designer.api.v1.activity_log_routes - INFO - Deleting activity logs older than 365 days (activity_log_routes.py:544)
2025-06-01 22:04:52 - ultimate_electrical_designer.api.v1.activity_log_routes - INFO - Deleted 10 old activity logs (activity_log_routes.py:550)
2025-06-01 22:04:52 - ultimate_electrical_designer.api.v1.activity_log_routes - DEBUG - Retrieving activity log 1 (activity_log_routes.py:577)
2025-06-01 22:04:52 - ultimate_electrical_designer.api.v1.activity_log_routes - WARNING - Invalid input error: Invalid input provided (activity_log_routes.py:90)
2025-06-01 22:04:52 - ultimate_electrical_designer.api.v1.activity_log_routes - DEBUG - Retrieving activity log 1 (activity_log_routes.py:577)
2025-06-01 22:04:52 - ultimate_electrical_designer.api.v1.activity_log_routes - ERROR - Database error: Database connection failed (activity_log_routes.py:96)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging event: CREATE, user_id=1, entity_type=EntityTypeEnum.PROJECT, entity_id=123 (activity_log_service.py:126)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Event logged successfully: CREATE, log_id=1 (activity_log_service.py:153)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging event: SYSTEM_START, user_id=None, entity_type=None, entity_id=None (activity_log_service.py:126)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Event logged successfully: SYSTEM_START, log_id=2 (activity_log_service.py:153)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging event: CREATE, user_id=999, entity_type=EntityTypeEnum.PROJECT, entity_id=123 (activity_log_service.py:126)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging user action: user_id=1, action=CREATE, entity_type=Project, entity_id=123 (activity_log_service.py:193)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging event: CREATE, user_id=1, entity_type=EntityTypeEnum.PROJECT, entity_id=123 (activity_log_service.py:126)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Event logged successfully: CREATE, log_id=1 (activity_log_service.py:153)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging user action: user_id=1, action=INVALID_ACTION, entity_type=Project, entity_id=123 (activity_log_service.py:193)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging user action: user_id=1, action=CREATE, entity_type=InvalidEntity, entity_id=123 (activity_log_service.py:193)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging authentication event: user_id=1, event_type=LOGIN, success=True (activity_log_service.py:248)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging event: LOGIN, user_id=1, entity_type=EntityTypeEnum.USER, entity_id=1 (activity_log_service.py:126)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Event logged successfully: LOGIN, log_id=1 (activity_log_service.py:153)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging authentication event: user_id=None, event_type=LOGIN, success=False (activity_log_service.py:248)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging event: LOGIN_FAILED, user_id=None, entity_type=EntityTypeEnum.USER, entity_id=None (activity_log_service.py:126)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Event logged successfully: LOGIN_FAILED, log_id=3 (activity_log_service.py:153)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging system event: event_type=SYSTEM_START (activity_log_service.py:291)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging event: SYSTEM_START, user_id=None, entity_type=None, entity_id=None (activity_log_service.py:126)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Event logged successfully: SYSTEM_START, log_id=1 (activity_log_service.py:153)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Retrieving activity log: 1 (activity_log_service.py:331)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Retrieving activity log: 999 (activity_log_service.py:331)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Retrieving activity logs for user 1: skip=0, limit=10 (activity_log_service.py:409)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Retrieving activity logs for user 999: skip=0, limit=100 (activity_log_service.py:409)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging security event: event_type=UNAUTHORIZED_ACCESS, user_id=1, severity=HIGH, threat_level=MEDIUM (activity_log_service.py:553)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging event: UNAUTHORIZED_ACCESS, user_id=1, entity_type=EntityTypeEnum.USER, entity_id=1 (activity_log_service.py:126)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Event logged successfully: UNAUTHORIZED_ACCESS, log_id=1 (activity_log_service.py:153)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging security event: event_type=UNAUTHORIZED_ACCESS, user_id=None, severity=INVALID_SEVERITY, threat_level=LOW (activity_log_service.py:553)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging security event: event_type=UNAUTHORIZED_ACCESS, user_id=None, severity=MEDIUM, threat_level=INVALID_LEVEL (activity_log_service.py:553)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Updating activity log: 1 (activity_log_service.py:791)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Activity log updated successfully: 1 (activity_log_service.py:814)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Updating activity log: 999 (activity_log_service.py:791)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Deleting activity logs older than 365 days (activity_log_service.py:841)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Deleted 10 old activity logs (activity_log_service.py:852)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Deleting activity logs older than 0 days (activity_log_service.py:841)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Generating audit report: type=user_activity, date_range=2025-05-02 22:05:03.340494 to 2025-06-01 22:05:03.340497 (activity_log_service.py:608)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Audit report generated successfully: e24b091d-8f26-445a-ac49-aee5aca6ba78, total_events=1 (activity_log_service.py:665)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Generating activity summary for user 1 from 2025-05-02 22:05:03.343312 to 2025-06-01 22:05:03.343316 (activity_log_service.py:752)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Generating activity summary for user 1 from 2025-06-01 22:05:03.345689 to 2025-05-31 22:05:03.345690 (activity_log_service.py:752)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging event: CREATE, user_id=None, entity_type=None, entity_id=None (activity_log_service.py:126)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - ERROR - Failed to log event CREATE: Database error (activity_log_service.py:163)
Traceback (most recent call last):
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\activity_log_service.py", line 149, in log_event
    activity_log = self.activity_log_repo.create(log_dict)
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1167, in __call__
    return self._mock_call(*args, **kwargs)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1171, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1226, in _execute_mock_call
    raise effect
sqlalchemy.exc.SQLAlchemyError: Database error
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging event: CREATE, user_id=None, entity_type=None, entity_id=None (activity_log_service.py:126)
2025-06-01 22:05:03 - ultimate_electrical_designer.core.services.activity_log_service - ERROR - Failed to log event CREATE: Repository error (activity_log_service.py:163)
Traceback (most recent call last):
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\activity_log_service.py", line 149, in log_event
    activity_log = self.activity_log_repo.create(log_dict)
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1167, in __call__
    return self._mock_call(*args, **kwargs)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1171, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1226, in _execute_mock_call
    raise effect
Exception: Repository error
2025-06-01 22:05:03 - ultimate_electrical_designer.api.v1.activity_log_routes - INFO - Creating activity log: EventTypeEnum.CREATE (activity_log_routes.py:132)
2025-06-01 22:05:03 - ultimate_electrical_designer.api.v1.activity_log_routes - INFO - Created activity log 1: EventTypeEnum.CREATE (activity_log_routes.py:144)
2025-06-01 22:05:03 - ultimate_electrical_designer.api.v1.activity_log_routes - DEBUG - Retrieving activity log 1 (activity_log_routes.py:577)
2025-06-01 22:05:03 - ultimate_electrical_designer.api.v1.activity_log_routes - DEBUG - Retrieving activity log 999 (activity_log_routes.py:577)
2025-06-01 22:05:03 - ultimate_electrical_designer.api.v1.activity_log_routes - WARNING - Not found error: Activity log 999 not found (activity_log_routes.py:84)
2025-06-01 22:05:03 - ultimate_electrical_designer.api.v1.activity_log_routes - DEBUG - Listing activity logs: skip=0, limit=10 (activity_log_routes.py:180)
2025-06-01 22:05:03 - ultimate_electrical_designer.api.v1.activity_log_routes - DEBUG - Listing activity logs: skip=0, limit=10 (activity_log_routes.py:180)
2025-06-01 22:05:03 - ultimate_electrical_designer.api.v1.activity_log_routes - INFO - Updating activity log 1 (activity_log_routes.py:228)
2025-06-01 22:05:03 - ultimate_electrical_designer.api.v1.activity_log_routes - INFO - Updated activity log 1 (activity_log_routes.py:234)
2025-06-01 22:05:03 - ultimate_electrical_designer.api.v1.activity_log_routes - DEBUG - Retrieving activity logs for user 1: skip=0, limit=10 (activity_log_routes.py:261)
2025-06-01 22:05:03 - ultimate_electrical_designer.api.v1.activity_log_routes - DEBUG - Generating activity summary for user 1 from 2025-05-02 22:05:03.619519 to 2025-06-01 22:05:03.619527 (activity_log_routes.py:290)
2025-06-01 22:05:03 - ultimate_electrical_designer.api.v1.activity_log_routes - DEBUG - Retrieving activity logs for entity Project:123: skip=0, limit=10 (activity_log_routes.py:327)
2025-06-01 22:05:03 - ultimate_electrical_designer.api.v1.activity_log_routes - DEBUG - Retrieving security events for date range 2025-05-25 22:05:03.656986 to 2025-06-01 22:05:03.657000: skip=0, limit=10 (activity_log_routes.py:364)
2025-06-01 22:05:03 - ultimate_electrical_designer.api.v1.activity_log_routes - INFO - Logging security event: event_type=UNAUTHORIZED_ACCESS, user_id=1, severity=HIGH, threat_level=MEDIUM (activity_log_routes.py:398)
2025-06-01 22:05:03 - ultimate_electrical_designer.api.v1.activity_log_routes - INFO - Security event logged successfully: 1 (activity_log_routes.py:424)
2025-06-01 22:05:03 - ultimate_electrical_designer.api.v1.activity_log_routes - DEBUG - Retrieving 50 most recent activity logs (activity_log_routes.py:490)
2025-06-01 22:05:03 - ultimate_electrical_designer.api.v1.activity_log_routes - DEBUG - Searching activity logs with term: project (activity_log_routes.py:517)
2025-06-01 22:05:03 - ultimate_electrical_designer.api.v1.activity_log_routes - INFO - Deleting activity logs older than 365 days (activity_log_routes.py:544)
2025-06-01 22:05:03 - ultimate_electrical_designer.api.v1.activity_log_routes - INFO - Deleted 10 old activity logs (activity_log_routes.py:550)
2025-06-01 22:05:03 - ultimate_electrical_designer.api.v1.activity_log_routes - DEBUG - Retrieving activity log 1 (activity_log_routes.py:577)
2025-06-01 22:05:03 - ultimate_electrical_designer.api.v1.activity_log_routes - WARNING - Invalid input error: Invalid input provided (activity_log_routes.py:90)
2025-06-01 22:05:03 - ultimate_electrical_designer.api.v1.activity_log_routes - DEBUG - Retrieving activity log 1 (activity_log_routes.py:577)
2025-06-01 22:05:03 - ultimate_electrical_designer.api.v1.activity_log_routes - ERROR - Database error: Database connection failed (activity_log_routes.py:96)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Retrieving electrical nodes for project 1: skip=0, limit=100 (electrical_repository.py:82)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Retrieved 1 electrical nodes for project 1 (electrical_repository.py:101)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Retrieving electrical nodes for project 1: skip=0, limit=100 (electrical_repository.py:82)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.electrical_repository - ERROR - Database error retrieving electrical nodes for project 1: Database error (electrical_repository.py:107)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Searching for electrical nodes with type: SWITCHBOARD_INCOMING in project 1 (electrical_repository.py:131)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Found 1 electrical nodes with type: SWITCHBOARD_INCOMING (electrical_repository.py:151)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Retrieving electrical nodes with capacity >= 50.0kVA for project 1 (electrical_repository.py:179)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Found 1 electrical nodes with sufficient capacity (electrical_repository.py:198)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Counting electrical nodes for project 1 (electrical_repository.py:265)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Total electrical nodes in project 1: 5 (electrical_repository.py:275)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Counting electrical nodes for project 1 (electrical_repository.py:265)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Total electrical nodes in project 1: None (electrical_repository.py:275)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Retrieving both cable routes for node 1 (electrical_repository.py:462)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Found 1 both cable routes for node 1 (electrical_repository.py:481)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Retrieving cable routes with installation method: CABLE_TRAY for project 1 (electrical_repository.py:509)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Found 1 cable routes with installation method: CABLE_TRAY (electrical_repository.py:527)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Retrieving cable routes with voltage drop > 3.0% for project 1 (electrical_repository.py:555)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Found 1 cable routes with high voltage drop (electrical_repository.py:578)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Retrieving load calculations for electrical node 2: skip=0, limit=100 (electrical_repository.py:992)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Retrieved 1 load calculations for electrical node 2 (electrical_repository.py:1011)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Retrieving load calculations with type: heat_tracing for project 1 (electrical_repository.py:1041)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Found 1 load calculations with type: heat_tracing (electrical_repository.py:1061)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Calculating total power for electrical node 2 (electrical_repository.py:1086)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Total power for electrical node 2: 5.5kW (electrical_repository.py:1101)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Calculating total power for electrical node 2 (electrical_repository.py:1086)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Total power for electrical node 2: 0.0kW (electrical_repository.py:1101)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Retrieving voltage drop calculations for cable route 1: skip=0, limit=100 (electrical_repository.py:1197)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Retrieved 1 voltage drop calculations for cable route 1 (electrical_repository.py:1216)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Retrieving non-compliant voltage drop calculations for project 1 (electrical_repository.py:1245)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Found 1 non-compliant voltage drop calculations for project 1 (electrical_repository.py:1265)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Calculating average voltage drop for project 1 (electrical_repository.py:1340)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Average voltage drop for project 1: 3.2% (electrical_repository.py:1353)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Counting compliant voltage drop calculations for project 1 (electrical_repository.py:1379)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Compliant voltage drop calculations in project 1: 8 (electrical_repository.py:1394)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes for project 1: skip=0, limit=100 (heat_tracing_repository.py:80)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieved 2 pipes for project 1 (heat_tracing_repository.py:99)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Searching for pipe with line tag: L-001 in project 1 (heat_tracing_repository.py:123)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found pipe: 'Test Pipe 001' (ID: 1) (heat_tracing_repository.py:138)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Searching for pipe with line tag: NON-EXISTENT in project 1 (heat_tracing_repository.py:123)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Pipe not found with line tag: NON-EXISTENT (heat_tracing_repository.py:140)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes without circuits for project 1 (heat_tracing_repository.py:162)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 1 pipes without circuits (heat_tracing_repository.py:178)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating heat loss calculation for pipe 1 (heat_tracing_repository.py:242)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Pipe 1 heat loss calculation updated successfully (heat_tracing_repository.py:264)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating heat loss calculation for pipe 999 (heat_tracing_repository.py:242)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Pipe 999 not found for heat loss update (heat_tracing_repository.py:248)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting pipes for project 1 (heat_tracing_repository.py:286)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total pipes in project 1: 0 (heat_tracing_repository.py:296)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting pipes for project 1 (heat_tracing_repository.py:286)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total pipes in project 1: 1 (heat_tracing_repository.py:296)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating heat loss calculation for pipe 1 (heat_tracing_repository.py:242)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Pipe 1 heat loss calculation updated successfully (heat_tracing_repository.py:264)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes with heat loss calculations for project 1 (heat_tracing_repository.py:199)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 1 pipes with heat loss calculations (heat_tracing_repository.py:217)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Searching for vessel with equipment tag: T-001 in project 1 (heat_tracing_repository.py:385)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found vessel: 'Test Vessel T-001' (ID: 1) (heat_tracing_repository.py:400)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving vessels without circuits for project 1 (heat_tracing_repository.py:426)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 1 vessels without circuits (heat_tracing_repository.py:442)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating heat loss calculation for vessel 1 (heat_tracing_repository.py:467)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Vessel 1 heat loss calculation updated successfully (heat_tracing_repository.py:489)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving HT circuits for feeder 1: skip=0, limit=100 (heat_tracing_repository.py:569)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieved 1 HT circuits for feeder 1 (heat_tracing_repository.py:588)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Searching for HT circuit for pipe 1 (heat_tracing_repository.py:611)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found HT circuit: 'HTC-001-A' (ID: 1) (heat_tracing_repository.py:620)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating load calculation for HT circuit 1 (heat_tracing_repository.py:729)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HT circuit 1 load calculation updated successfully (heat_tracing_repository.py:752)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating load calculation for HT circuit 1 (heat_tracing_repository.py:729)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HT circuit 1 load calculation updated successfully (heat_tracing_repository.py:752)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Calculating total load for feeder 1 (heat_tracing_repository.py:776)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total load for feeder 1: 1.25 kW (heat_tracing_repository.py:789)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving control circuits for switchboard 1: skip=0, limit=100 (heat_tracing_repository.py:833)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieved 1 control circuits for switchboard 1 (heat_tracing_repository.py:852)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving control circuits with limiting function for switchboard 1 (heat_tracing_repository.py:926)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 1 control circuits with limiting function (heat_tracing_repository.py:942)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HeatTracingRepository aggregate initialized (heat_tracing_repository.py:1016)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HeatTracingRepository aggregate initialized (heat_tracing_repository.py:1016)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Generating heat tracing summary for project 1 (heat_tracing_repository.py:1031)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting pipes for project 1 (heat_tracing_repository.py:286)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total pipes in project 1: 0 (heat_tracing_repository.py:296)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting vessels for project 1 (heat_tracing_repository.py:513)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total vessels in project 1: 0 (heat_tracing_repository.py:523)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes without circuits for project 1 (heat_tracing_repository.py:162)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 pipes without circuits (heat_tracing_repository.py:178)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving vessels without circuits for project 1 (heat_tracing_repository.py:426)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 vessels without circuits (heat_tracing_repository.py:442)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes with heat loss calculations for project 1 (heat_tracing_repository.py:199)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 pipes with heat loss calculations (heat_tracing_repository.py:217)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving vessels without circuits for project 1 (heat_tracing_repository.py:426)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 vessels without circuits (heat_tracing_repository.py:442)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Generated summary for project 1: {'project_id': 1, 'total_pipes': 0, 'total_vessels': 0, 'pipes_without_circuits': 0, 'vessels_without_circuits': 0, 'pipes_with_calculations': 0, 'vessels_with_calculations': 0} (heat_tracing_repository.py:1052)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HeatTracingRepository aggregate initialized (heat_tracing_repository.py:1016)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Checking design readiness for project 1 (heat_tracing_repository.py:1072)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Generating heat tracing summary for project 1 (heat_tracing_repository.py:1031)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting pipes for project 1 (heat_tracing_repository.py:286)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total pipes in project 1: 0 (heat_tracing_repository.py:296)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting vessels for project 1 (heat_tracing_repository.py:513)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total vessels in project 1: 0 (heat_tracing_repository.py:523)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes without circuits for project 1 (heat_tracing_repository.py:162)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 pipes without circuits (heat_tracing_repository.py:178)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving vessels without circuits for project 1 (heat_tracing_repository.py:426)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 vessels without circuits (heat_tracing_repository.py:442)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes with heat loss calculations for project 1 (heat_tracing_repository.py:199)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 pipes with heat loss calculations (heat_tracing_repository.py:217)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving vessels without circuits for project 1 (heat_tracing_repository.py:426)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 vessels without circuits (heat_tracing_repository.py:442)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Generated summary for project 1: {'project_id': 1, 'total_pipes': 0, 'total_vessels': 0, 'pipes_without_circuits': 0, 'vessels_without_circuits': 0, 'pipes_with_calculations': 0, 'vessels_with_calculations': 0} (heat_tracing_repository.py:1052)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Design readiness for project 1: 0.0% (heat_tracing_repository.py:1114)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Searching for project with code: HT-TEST-001 (project_repository.py:56)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Found project: 'Test Heat Tracing Project' (ID: 1) (project_repository.py:66)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Searching for project with code: NONEXISTENT-001 (project_repository.py:56)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Project not found with code: NONEXISTENT-001 (project_repository.py:63)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Searching for project with name: Test Heat Tracing Project (project_repository.py:89)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Found project: 'Test Heat Tracing Project' (ID: 1) (project_repository.py:96)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Searching for project with name: Nonexistent Project (project_repository.py:89)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Project not found with name: Nonexistent Project (project_repository.py:98)
2025-06-01 23:39:40 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:39:41 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:39:41 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:39:41 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Retrieving active projects: skip=0, limit=100 (project_repository.py:121)
2025-06-01 23:39:41 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Retrieved 12 active projects (project_repository.py:133)
2025-06-01 23:39:41 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:39:41 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Retrieving active projects: skip=2, limit=5 (project_repository.py:121)
2025-06-01 23:39:41 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Retrieved 5 active projects (project_repository.py:133)
2025-06-01 23:39:41 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:39:41 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Updating project 1 with data: ['name', 'description', 'max_ambient_temp_c'] (project_repository.py:157)
2025-06-01 23:39:41 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Project 1 updated successfully (project_repository.py:178)
2025-06-01 23:39:41 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:39:41 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Updating project 99999 with data: ['name'] (project_repository.py:157)
2025-06-01 23:39:41 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Project 99999 not found for update (project_repository.py:165)
2025-06-01 23:39:41 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:39:41 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Soft deleting project 1 (project_repository.py:201)
2025-06-01 23:39:41 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Project 1 soft deleted successfully (project_repository.py:223)
2025-06-01 23:39:41 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:39:41 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Soft deleting project 99999 (project_repository.py:201)
2025-06-01 23:39:41 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Project 99999 not found or already deleted (project_repository.py:226)
2025-06-01 23:39:41 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:39:41 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Soft deleting project 1 (project_repository.py:201)
2025-06-01 23:39:41 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Project 1 soft deleted successfully (project_repository.py:223)
2025-06-01 23:39:41 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Soft deleting project 1 (project_repository.py:201)
2025-06-01 23:39:41 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Project 1 not found or already deleted (project_repository.py:226)
2025-06-01 23:39:41 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:39:41 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Searching projects for term: 'Project 1' (project_repository.py:295)
2025-06-01 23:39:41 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Found 7 projects matching 'Project 1' (project_repository.py:318)
2025-06-01 23:39:41 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:39:41 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Searching projects for term: 'HT-TEST-001' (project_repository.py:295)
2025-06-01 23:39:41 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Found 1 projects matching 'HT-TEST-001' (project_repository.py:318)
2025-06-01 23:39:41 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:39:41 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Searching projects for term: 'description' (project_repository.py:295)
2025-06-01 23:39:41 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Found 15 projects matching 'description' (project_repository.py:318)
2025-06-01 23:39:41 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:39:41 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Searching projects for term: 'test project' (project_repository.py:295)
2025-06-01 23:39:41 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Found 15 projects matching 'test project' (project_repository.py:318)
2025-06-01 23:39:41 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Searching projects for term: 'TEST PROJECT' (project_repository.py:295)
2025-06-01 23:39:41 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Found 15 projects matching 'TEST PROJECT' (project_repository.py:318)
2025-06-01 23:39:41 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Searching projects for term: 'Test Project' (project_repository.py:295)
2025-06-01 23:39:41 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Found 15 projects matching 'Test Project' (project_repository.py:318)
2025-06-01 23:39:41 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:39:41 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Searching projects for term: 'Test Project' (project_repository.py:295)
2025-06-01 23:39:41 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Found 12 projects matching 'Test Project' (project_repository.py:318)
2025-06-01 23:39:41 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:39:41 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Searching projects for term: 'Project' (project_repository.py:295)
2025-06-01 23:39:41 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Found 5 projects matching 'Project' (project_repository.py:318)
2025-06-01 23:39:41 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Searching projects for term: 'Project' (project_repository.py:295)
2025-06-01 23:39:41 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Found 5 projects matching 'Project' (project_repository.py:318)
2025-06-01 23:39:41 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:39:41 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Counting active projects (project_repository.py:336)
2025-06-01 23:39:41 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Total active projects: 12 (project_repository.py:346)
2025-06-01 23:39:41 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:39:41 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Counting active projects (project_repository.py:336)
2025-06-01 23:39:41 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Total active projects: 0 (project_repository.py:346)
2025-06-01 23:39:41 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:39:41 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Retrieving project 1 with related data (project_repository.py:247)
2025-06-01 23:39:41 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Retrieved project with related data: 'Test Heat Tracing Project' (ID: 1) (project_repository.py:264)
2025-06-01 23:39:41 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:39:41 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Retrieving project 99999 with related data (project_repository.py:247)
2025-06-01 23:39:41 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Project 99999 not found (project_repository.py:268)
2025-06-01 23:39:41 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:39:41 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Retrieving electrical nodes for project 1: skip=0, limit=100 (electrical_repository.py:82)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Retrieved 1 electrical nodes for project 1 (electrical_repository.py:101)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Retrieving electrical nodes for project 1: skip=0, limit=100 (electrical_repository.py:82)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.electrical_repository - ERROR - Database error retrieving electrical nodes for project 1: Database error (electrical_repository.py:107)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Searching for electrical nodes with type: SWITCHBOARD_INCOMING in project 1 (electrical_repository.py:131)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Found 1 electrical nodes with type: SWITCHBOARD_INCOMING (electrical_repository.py:151)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Retrieving electrical nodes with capacity >= 50.0kVA for project 1 (electrical_repository.py:179)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Found 1 electrical nodes with sufficient capacity (electrical_repository.py:198)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Counting electrical nodes for project 1 (electrical_repository.py:265)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Total electrical nodes in project 1: 5 (electrical_repository.py:275)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Counting electrical nodes for project 1 (electrical_repository.py:265)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Total electrical nodes in project 1: None (electrical_repository.py:275)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Retrieving both cable routes for node 1 (electrical_repository.py:462)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Found 1 both cable routes for node 1 (electrical_repository.py:481)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Retrieving cable routes with installation method: CABLE_TRAY for project 1 (electrical_repository.py:509)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Found 1 cable routes with installation method: CABLE_TRAY (electrical_repository.py:527)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Retrieving cable routes with voltage drop > 3.0% for project 1 (electrical_repository.py:555)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Found 1 cable routes with high voltage drop (electrical_repository.py:578)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Retrieving load calculations for electrical node 2: skip=0, limit=100 (electrical_repository.py:992)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Retrieved 1 load calculations for electrical node 2 (electrical_repository.py:1011)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Retrieving load calculations with type: heat_tracing for project 1 (electrical_repository.py:1041)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Found 1 load calculations with type: heat_tracing (electrical_repository.py:1061)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Calculating total power for electrical node 2 (electrical_repository.py:1086)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Total power for electrical node 2: 5.5kW (electrical_repository.py:1101)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Calculating total power for electrical node 2 (electrical_repository.py:1086)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Total power for electrical node 2: 0.0kW (electrical_repository.py:1101)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Retrieving voltage drop calculations for cable route 1: skip=0, limit=100 (electrical_repository.py:1197)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Retrieved 1 voltage drop calculations for cable route 1 (electrical_repository.py:1216)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Retrieving non-compliant voltage drop calculations for project 1 (electrical_repository.py:1245)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Found 1 non-compliant voltage drop calculations for project 1 (electrical_repository.py:1265)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Calculating average voltage drop for project 1 (electrical_repository.py:1340)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Average voltage drop for project 1: 3.2% (electrical_repository.py:1353)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Counting compliant voltage drop calculations for project 1 (electrical_repository.py:1379)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Compliant voltage drop calculations in project 1: 8 (electrical_repository.py:1394)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes for project 1: skip=0, limit=100 (heat_tracing_repository.py:80)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieved 2 pipes for project 1 (heat_tracing_repository.py:99)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Searching for pipe with line tag: L-001 in project 1 (heat_tracing_repository.py:123)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found pipe: 'Test Pipe 001' (ID: 1) (heat_tracing_repository.py:138)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Searching for pipe with line tag: NON-EXISTENT in project 1 (heat_tracing_repository.py:123)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Pipe not found with line tag: NON-EXISTENT (heat_tracing_repository.py:140)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes without circuits for project 1 (heat_tracing_repository.py:162)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 1 pipes without circuits (heat_tracing_repository.py:178)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating heat loss calculation for pipe 1 (heat_tracing_repository.py:242)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Pipe 1 heat loss calculation updated successfully (heat_tracing_repository.py:264)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating heat loss calculation for pipe 999 (heat_tracing_repository.py:242)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Pipe 999 not found for heat loss update (heat_tracing_repository.py:248)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting pipes for project 1 (heat_tracing_repository.py:286)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total pipes in project 1: 0 (heat_tracing_repository.py:296)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting pipes for project 1 (heat_tracing_repository.py:286)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total pipes in project 1: 1 (heat_tracing_repository.py:296)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating heat loss calculation for pipe 1 (heat_tracing_repository.py:242)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Pipe 1 heat loss calculation updated successfully (heat_tracing_repository.py:264)
2025-06-01 23:43:01 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes with heat loss calculations for project 1 (heat_tracing_repository.py:199)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 1 pipes with heat loss calculations (heat_tracing_repository.py:217)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Searching for vessel with equipment tag: T-001 in project 1 (heat_tracing_repository.py:385)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found vessel: 'Test Vessel T-001' (ID: 1) (heat_tracing_repository.py:400)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving vessels without circuits for project 1 (heat_tracing_repository.py:426)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 1 vessels without circuits (heat_tracing_repository.py:442)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating heat loss calculation for vessel 1 (heat_tracing_repository.py:467)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Vessel 1 heat loss calculation updated successfully (heat_tracing_repository.py:489)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving HT circuits for feeder 1: skip=0, limit=100 (heat_tracing_repository.py:569)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieved 1 HT circuits for feeder 1 (heat_tracing_repository.py:588)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Searching for HT circuit for pipe 1 (heat_tracing_repository.py:611)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found HT circuit: 'HTC-001-A' (ID: 1) (heat_tracing_repository.py:620)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating load calculation for HT circuit 1 (heat_tracing_repository.py:729)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HT circuit 1 load calculation updated successfully (heat_tracing_repository.py:752)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating load calculation for HT circuit 1 (heat_tracing_repository.py:729)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HT circuit 1 load calculation updated successfully (heat_tracing_repository.py:752)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Calculating total load for feeder 1 (heat_tracing_repository.py:776)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total load for feeder 1: 1.25 kW (heat_tracing_repository.py:789)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving control circuits for switchboard 1: skip=0, limit=100 (heat_tracing_repository.py:833)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieved 1 control circuits for switchboard 1 (heat_tracing_repository.py:852)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving control circuits with limiting function for switchboard 1 (heat_tracing_repository.py:926)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 1 control circuits with limiting function (heat_tracing_repository.py:942)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HeatTracingRepository aggregate initialized (heat_tracing_repository.py:1016)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HeatTracingRepository aggregate initialized (heat_tracing_repository.py:1016)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Generating heat tracing summary for project 1 (heat_tracing_repository.py:1031)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting pipes for project 1 (heat_tracing_repository.py:286)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total pipes in project 1: 0 (heat_tracing_repository.py:296)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting vessels for project 1 (heat_tracing_repository.py:513)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total vessels in project 1: 0 (heat_tracing_repository.py:523)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes without circuits for project 1 (heat_tracing_repository.py:162)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 pipes without circuits (heat_tracing_repository.py:178)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving vessels without circuits for project 1 (heat_tracing_repository.py:426)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 vessels without circuits (heat_tracing_repository.py:442)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes with heat loss calculations for project 1 (heat_tracing_repository.py:199)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 pipes with heat loss calculations (heat_tracing_repository.py:217)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving vessels without circuits for project 1 (heat_tracing_repository.py:426)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 vessels without circuits (heat_tracing_repository.py:442)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Generated summary for project 1: {'project_id': 1, 'total_pipes': 0, 'total_vessels': 0, 'pipes_without_circuits': 0, 'vessels_without_circuits': 0, 'pipes_with_calculations': 0, 'vessels_with_calculations': 0} (heat_tracing_repository.py:1052)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HeatTracingRepository aggregate initialized (heat_tracing_repository.py:1016)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Checking design readiness for project 1 (heat_tracing_repository.py:1072)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Generating heat tracing summary for project 1 (heat_tracing_repository.py:1031)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting pipes for project 1 (heat_tracing_repository.py:286)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total pipes in project 1: 0 (heat_tracing_repository.py:296)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting vessels for project 1 (heat_tracing_repository.py:513)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total vessels in project 1: 0 (heat_tracing_repository.py:523)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes without circuits for project 1 (heat_tracing_repository.py:162)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 pipes without circuits (heat_tracing_repository.py:178)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving vessels without circuits for project 1 (heat_tracing_repository.py:426)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 vessels without circuits (heat_tracing_repository.py:442)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes with heat loss calculations for project 1 (heat_tracing_repository.py:199)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 pipes with heat loss calculations (heat_tracing_repository.py:217)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving vessels without circuits for project 1 (heat_tracing_repository.py:426)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 vessels without circuits (heat_tracing_repository.py:442)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Generated summary for project 1: {'project_id': 1, 'total_pipes': 0, 'total_vessels': 0, 'pipes_without_circuits': 0, 'vessels_without_circuits': 0, 'pipes_with_calculations': 0, 'vessels_with_calculations': 0} (heat_tracing_repository.py:1052)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Design readiness for project 1: 0.0% (heat_tracing_repository.py:1114)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Searching for project with code: HT-TEST-001 (project_repository.py:56)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Found project: 'Test Heat Tracing Project' (ID: 1) (project_repository.py:66)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Searching for project with code: NONEXISTENT-001 (project_repository.py:56)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Project not found with code: NONEXISTENT-001 (project_repository.py:63)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Searching for project with name: Test Heat Tracing Project (project_repository.py:89)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Found project: 'Test Heat Tracing Project' (ID: 1) (project_repository.py:96)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Searching for project with name: Nonexistent Project (project_repository.py:89)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Project not found with name: Nonexistent Project (project_repository.py:98)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Retrieving active projects: skip=0, limit=100 (project_repository.py:121)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Retrieved 12 active projects (project_repository.py:133)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Retrieving active projects: skip=2, limit=5 (project_repository.py:121)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Retrieved 5 active projects (project_repository.py:133)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Updating project 1 with data: ['name', 'description', 'max_ambient_temp_c'] (project_repository.py:157)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Project 1 updated successfully (project_repository.py:178)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Updating project 99999 with data: ['name'] (project_repository.py:157)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Project 99999 not found for update (project_repository.py:165)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Soft deleting project 1 (project_repository.py:201)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Project 1 soft deleted successfully (project_repository.py:223)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Soft deleting project 99999 (project_repository.py:201)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Project 99999 not found or already deleted (project_repository.py:226)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Soft deleting project 1 (project_repository.py:201)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Project 1 soft deleted successfully (project_repository.py:223)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Soft deleting project 1 (project_repository.py:201)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Project 1 not found or already deleted (project_repository.py:226)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Searching projects for term: 'Project 1' (project_repository.py:295)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Found 7 projects matching 'Project 1' (project_repository.py:318)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Searching projects for term: 'HT-TEST-001' (project_repository.py:295)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Found 1 projects matching 'HT-TEST-001' (project_repository.py:318)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Searching projects for term: 'description' (project_repository.py:295)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Found 15 projects matching 'description' (project_repository.py:318)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Searching projects for term: 'test project' (project_repository.py:295)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Found 15 projects matching 'test project' (project_repository.py:318)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Searching projects for term: 'TEST PROJECT' (project_repository.py:295)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Found 15 projects matching 'TEST PROJECT' (project_repository.py:318)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Searching projects for term: 'Test Project' (project_repository.py:295)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Found 15 projects matching 'Test Project' (project_repository.py:318)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Searching projects for term: 'Test Project' (project_repository.py:295)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Found 12 projects matching 'Test Project' (project_repository.py:318)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Searching projects for term: 'Project' (project_repository.py:295)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Found 5 projects matching 'Project' (project_repository.py:318)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Searching projects for term: 'Project' (project_repository.py:295)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Found 5 projects matching 'Project' (project_repository.py:318)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Counting active projects (project_repository.py:336)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Total active projects: 12 (project_repository.py:346)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Counting active projects (project_repository.py:336)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Total active projects: 0 (project_repository.py:346)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Retrieving project 1 with related data (project_repository.py:247)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Retrieved project with related data: 'Test Heat Tracing Project' (ID: 1) (project_repository.py:264)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Retrieving project 99999 with related data (project_repository.py:247)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Project 99999 not found (project_repository.py:268)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:43:02 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Retrieving electrical nodes for project 1: skip=0, limit=100 (electrical_repository.py:82)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Retrieved 1 electrical nodes for project 1 (electrical_repository.py:101)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Retrieving electrical nodes for project 1: skip=0, limit=100 (electrical_repository.py:82)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.electrical_repository - ERROR - Database error retrieving electrical nodes for project 1: Database error (electrical_repository.py:107)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Searching for electrical nodes with type: SWITCHBOARD_INCOMING in project 1 (electrical_repository.py:131)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Found 1 electrical nodes with type: SWITCHBOARD_INCOMING (electrical_repository.py:151)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Retrieving electrical nodes with capacity >= 50.0kVA for project 1 (electrical_repository.py:179)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Found 1 electrical nodes with sufficient capacity (electrical_repository.py:198)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Counting electrical nodes for project 1 (electrical_repository.py:265)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Total electrical nodes in project 1: 5 (electrical_repository.py:275)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Counting electrical nodes for project 1 (electrical_repository.py:265)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Total electrical nodes in project 1: None (electrical_repository.py:275)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Retrieving both cable routes for node 1 (electrical_repository.py:462)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Found 1 both cable routes for node 1 (electrical_repository.py:481)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Retrieving cable routes with installation method: CABLE_TRAY for project 1 (electrical_repository.py:509)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Found 1 cable routes with installation method: CABLE_TRAY (electrical_repository.py:527)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Retrieving cable routes with voltage drop > 3.0% for project 1 (electrical_repository.py:555)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Found 1 cable routes with high voltage drop (electrical_repository.py:578)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Retrieving load calculations for electrical node 2: skip=0, limit=100 (electrical_repository.py:992)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Retrieved 1 load calculations for electrical node 2 (electrical_repository.py:1011)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Retrieving load calculations with type: heat_tracing for project 1 (electrical_repository.py:1041)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Found 1 load calculations with type: heat_tracing (electrical_repository.py:1061)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Calculating total power for electrical node 2 (electrical_repository.py:1086)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Total power for electrical node 2: 5.5kW (electrical_repository.py:1101)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Calculating total power for electrical node 2 (electrical_repository.py:1086)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Total power for electrical node 2: 0.0kW (electrical_repository.py:1101)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Retrieving voltage drop calculations for cable route 1: skip=0, limit=100 (electrical_repository.py:1197)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Retrieved 1 voltage drop calculations for cable route 1 (electrical_repository.py:1216)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Retrieving non-compliant voltage drop calculations for project 1 (electrical_repository.py:1245)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Found 1 non-compliant voltage drop calculations for project 1 (electrical_repository.py:1265)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Calculating average voltage drop for project 1 (electrical_repository.py:1340)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Average voltage drop for project 1: 3.2% (electrical_repository.py:1353)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Counting compliant voltage drop calculations for project 1 (electrical_repository.py:1379)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Compliant voltage drop calculations in project 1: 8 (electrical_repository.py:1394)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes for project 1: skip=0, limit=100 (heat_tracing_repository.py:80)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieved 2 pipes for project 1 (heat_tracing_repository.py:99)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Searching for pipe with line tag: L-001 in project 1 (heat_tracing_repository.py:123)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found pipe: 'Test Pipe 001' (ID: 1) (heat_tracing_repository.py:138)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Searching for pipe with line tag: NON-EXISTENT in project 1 (heat_tracing_repository.py:123)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Pipe not found with line tag: NON-EXISTENT (heat_tracing_repository.py:140)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes without circuits for project 1 (heat_tracing_repository.py:162)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 1 pipes without circuits (heat_tracing_repository.py:178)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating heat loss calculation for pipe 1 (heat_tracing_repository.py:242)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Pipe 1 heat loss calculation updated successfully (heat_tracing_repository.py:264)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating heat loss calculation for pipe 999 (heat_tracing_repository.py:242)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Pipe 999 not found for heat loss update (heat_tracing_repository.py:248)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting pipes for project 1 (heat_tracing_repository.py:286)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total pipes in project 1: 0 (heat_tracing_repository.py:296)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting pipes for project 1 (heat_tracing_repository.py:286)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total pipes in project 1: 1 (heat_tracing_repository.py:296)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating heat loss calculation for pipe 1 (heat_tracing_repository.py:242)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Pipe 1 heat loss calculation updated successfully (heat_tracing_repository.py:264)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes with heat loss calculations for project 1 (heat_tracing_repository.py:199)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 1 pipes with heat loss calculations (heat_tracing_repository.py:217)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Searching for vessel with equipment tag: T-001 in project 1 (heat_tracing_repository.py:385)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found vessel: 'Test Vessel T-001' (ID: 1) (heat_tracing_repository.py:400)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving vessels without circuits for project 1 (heat_tracing_repository.py:426)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 1 vessels without circuits (heat_tracing_repository.py:442)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating heat loss calculation for vessel 1 (heat_tracing_repository.py:467)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Vessel 1 heat loss calculation updated successfully (heat_tracing_repository.py:489)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving HT circuits for feeder 1: skip=0, limit=100 (heat_tracing_repository.py:569)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieved 1 HT circuits for feeder 1 (heat_tracing_repository.py:588)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Searching for HT circuit for pipe 1 (heat_tracing_repository.py:611)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found HT circuit: 'HTC-001-A' (ID: 1) (heat_tracing_repository.py:620)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating load calculation for HT circuit 1 (heat_tracing_repository.py:729)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HT circuit 1 load calculation updated successfully (heat_tracing_repository.py:752)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating load calculation for HT circuit 1 (heat_tracing_repository.py:729)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HT circuit 1 load calculation updated successfully (heat_tracing_repository.py:752)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Calculating total load for feeder 1 (heat_tracing_repository.py:776)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total load for feeder 1: 1.25 kW (heat_tracing_repository.py:789)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving control circuits for switchboard 1: skip=0, limit=100 (heat_tracing_repository.py:833)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieved 1 control circuits for switchboard 1 (heat_tracing_repository.py:852)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving control circuits with limiting function for switchboard 1 (heat_tracing_repository.py:926)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 1 control circuits with limiting function (heat_tracing_repository.py:942)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HeatTracingRepository aggregate initialized (heat_tracing_repository.py:1016)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HeatTracingRepository aggregate initialized (heat_tracing_repository.py:1016)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Generating heat tracing summary for project 1 (heat_tracing_repository.py:1031)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting pipes for project 1 (heat_tracing_repository.py:286)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total pipes in project 1: 0 (heat_tracing_repository.py:296)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting vessels for project 1 (heat_tracing_repository.py:513)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total vessels in project 1: 0 (heat_tracing_repository.py:523)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes without circuits for project 1 (heat_tracing_repository.py:162)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 pipes without circuits (heat_tracing_repository.py:178)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving vessels without circuits for project 1 (heat_tracing_repository.py:426)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 vessels without circuits (heat_tracing_repository.py:442)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes with heat loss calculations for project 1 (heat_tracing_repository.py:199)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 pipes with heat loss calculations (heat_tracing_repository.py:217)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving vessels without circuits for project 1 (heat_tracing_repository.py:426)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 vessels without circuits (heat_tracing_repository.py:442)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Generated summary for project 1: {'project_id': 1, 'total_pipes': 0, 'total_vessels': 0, 'pipes_without_circuits': 0, 'vessels_without_circuits': 0, 'pipes_with_calculations': 0, 'vessels_with_calculations': 0} (heat_tracing_repository.py:1052)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HeatTracingRepository aggregate initialized (heat_tracing_repository.py:1016)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Checking design readiness for project 1 (heat_tracing_repository.py:1072)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Generating heat tracing summary for project 1 (heat_tracing_repository.py:1031)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting pipes for project 1 (heat_tracing_repository.py:286)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total pipes in project 1: 0 (heat_tracing_repository.py:296)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting vessels for project 1 (heat_tracing_repository.py:513)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total vessels in project 1: 0 (heat_tracing_repository.py:523)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes without circuits for project 1 (heat_tracing_repository.py:162)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 pipes without circuits (heat_tracing_repository.py:178)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving vessels without circuits for project 1 (heat_tracing_repository.py:426)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 vessels without circuits (heat_tracing_repository.py:442)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes with heat loss calculations for project 1 (heat_tracing_repository.py:199)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 pipes with heat loss calculations (heat_tracing_repository.py:217)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving vessels without circuits for project 1 (heat_tracing_repository.py:426)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 vessels without circuits (heat_tracing_repository.py:442)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Generated summary for project 1: {'project_id': 1, 'total_pipes': 0, 'total_vessels': 0, 'pipes_without_circuits': 0, 'vessels_without_circuits': 0, 'pipes_with_calculations': 0, 'vessels_with_calculations': 0} (heat_tracing_repository.py:1052)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Design readiness for project 1: 0.0% (heat_tracing_repository.py:1114)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Searching for project with code: HT-TEST-001 (project_repository.py:56)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Found project: 'Test Heat Tracing Project' (ID: 1) (project_repository.py:66)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Searching for project with code: NONEXISTENT-001 (project_repository.py:56)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Project not found with code: NONEXISTENT-001 (project_repository.py:63)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Searching for project with name: Test Heat Tracing Project (project_repository.py:89)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Found project: 'Test Heat Tracing Project' (ID: 1) (project_repository.py:96)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Searching for project with name: Nonexistent Project (project_repository.py:89)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Project not found with name: Nonexistent Project (project_repository.py:98)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Retrieving active projects: skip=0, limit=100 (project_repository.py:121)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Retrieved 12 active projects (project_repository.py:133)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Retrieving active projects: skip=2, limit=5 (project_repository.py:121)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Retrieved 5 active projects (project_repository.py:133)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Updating project 1 with data: ['name', 'description', 'max_ambient_temp_c'] (project_repository.py:157)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Project 1 updated successfully (project_repository.py:178)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Updating project 99999 with data: ['name'] (project_repository.py:157)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Project 99999 not found for update (project_repository.py:165)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:43:43 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Soft deleting project 1 (project_repository.py:201)
2025-06-01 23:43:44 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Project 1 soft deleted successfully (project_repository.py:223)
2025-06-01 23:43:44 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:43:44 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Soft deleting project 99999 (project_repository.py:201)
2025-06-01 23:43:44 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Project 99999 not found or already deleted (project_repository.py:226)
2025-06-01 23:43:44 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:43:44 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Soft deleting project 1 (project_repository.py:201)
2025-06-01 23:43:44 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Project 1 soft deleted successfully (project_repository.py:223)
2025-06-01 23:43:44 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Soft deleting project 1 (project_repository.py:201)
2025-06-01 23:43:44 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Project 1 not found or already deleted (project_repository.py:226)
2025-06-01 23:43:44 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:43:44 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Searching projects for term: 'Project 1' (project_repository.py:295)
2025-06-01 23:43:44 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Found 7 projects matching 'Project 1' (project_repository.py:318)
2025-06-01 23:43:44 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:43:44 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Searching projects for term: 'HT-TEST-001' (project_repository.py:295)
2025-06-01 23:43:44 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Found 1 projects matching 'HT-TEST-001' (project_repository.py:318)
2025-06-01 23:43:44 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:43:44 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Searching projects for term: 'description' (project_repository.py:295)
2025-06-01 23:43:44 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Found 15 projects matching 'description' (project_repository.py:318)
2025-06-01 23:43:44 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:43:44 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Searching projects for term: 'test project' (project_repository.py:295)
2025-06-01 23:43:44 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Found 15 projects matching 'test project' (project_repository.py:318)
2025-06-01 23:43:44 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Searching projects for term: 'TEST PROJECT' (project_repository.py:295)
2025-06-01 23:43:44 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Found 15 projects matching 'TEST PROJECT' (project_repository.py:318)
2025-06-01 23:43:44 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Searching projects for term: 'Test Project' (project_repository.py:295)
2025-06-01 23:43:44 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Found 15 projects matching 'Test Project' (project_repository.py:318)
2025-06-01 23:43:44 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:43:44 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Searching projects for term: 'Test Project' (project_repository.py:295)
2025-06-01 23:43:44 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Found 12 projects matching 'Test Project' (project_repository.py:318)
2025-06-01 23:43:44 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:43:44 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Searching projects for term: 'Project' (project_repository.py:295)
2025-06-01 23:43:44 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Found 5 projects matching 'Project' (project_repository.py:318)
2025-06-01 23:43:44 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Searching projects for term: 'Project' (project_repository.py:295)
2025-06-01 23:43:44 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Found 5 projects matching 'Project' (project_repository.py:318)
2025-06-01 23:43:44 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:43:44 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Counting active projects (project_repository.py:336)
2025-06-01 23:43:44 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Total active projects: 12 (project_repository.py:346)
2025-06-01 23:43:44 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:43:44 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Counting active projects (project_repository.py:336)
2025-06-01 23:43:44 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Total active projects: 0 (project_repository.py:346)
2025-06-01 23:43:44 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:43:44 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Retrieving project 1 with related data (project_repository.py:247)
2025-06-01 23:43:44 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Retrieved project with related data: 'Test Heat Tracing Project' (ID: 1) (project_repository.py:264)
2025-06-01 23:43:44 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:43:44 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Retrieving project 99999 with related data (project_repository.py:247)
2025-06-01 23:43:44 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Project 99999 not found (project_repository.py:268)
2025-06-01 23:43:44 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:43:44 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Retrieving electrical nodes for project 1: skip=0, limit=100 (electrical_repository.py:82)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Retrieved 1 electrical nodes for project 1 (electrical_repository.py:101)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Retrieving electrical nodes for project 1: skip=0, limit=100 (electrical_repository.py:82)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.electrical_repository - ERROR - Database error retrieving electrical nodes for project 1: Database error (electrical_repository.py:107)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Searching for electrical nodes with type: SWITCHBOARD_INCOMING in project 1 (electrical_repository.py:131)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Found 1 electrical nodes with type: SWITCHBOARD_INCOMING (electrical_repository.py:151)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Retrieving electrical nodes with capacity >= 50.0kVA for project 1 (electrical_repository.py:179)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Found 1 electrical nodes with sufficient capacity (electrical_repository.py:198)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Counting electrical nodes for project 1 (electrical_repository.py:265)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Total electrical nodes in project 1: 5 (electrical_repository.py:275)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Counting electrical nodes for project 1 (electrical_repository.py:265)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Total electrical nodes in project 1: None (electrical_repository.py:275)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Retrieving both cable routes for node 1 (electrical_repository.py:462)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Found 1 both cable routes for node 1 (electrical_repository.py:481)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Retrieving cable routes with installation method: CABLE_TRAY for project 1 (electrical_repository.py:509)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Found 1 cable routes with installation method: CABLE_TRAY (electrical_repository.py:527)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Retrieving cable routes with voltage drop > 3.0% for project 1 (electrical_repository.py:555)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Found 1 cable routes with high voltage drop (electrical_repository.py:578)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Retrieving load calculations for electrical node 2: skip=0, limit=100 (electrical_repository.py:992)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Retrieved 1 load calculations for electrical node 2 (electrical_repository.py:1011)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Retrieving load calculations with type: heat_tracing for project 1 (electrical_repository.py:1041)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Found 1 load calculations with type: heat_tracing (electrical_repository.py:1061)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Calculating total power for electrical node 2 (electrical_repository.py:1086)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Total power for electrical node 2: 5.5kW (electrical_repository.py:1101)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Calculating total power for electrical node 2 (electrical_repository.py:1086)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Total power for electrical node 2: 0.0kW (electrical_repository.py:1101)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Retrieving voltage drop calculations for cable route 1: skip=0, limit=100 (electrical_repository.py:1197)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Retrieved 1 voltage drop calculations for cable route 1 (electrical_repository.py:1216)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Retrieving non-compliant voltage drop calculations for project 1 (electrical_repository.py:1245)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Found 1 non-compliant voltage drop calculations for project 1 (electrical_repository.py:1265)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Calculating average voltage drop for project 1 (electrical_repository.py:1340)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Average voltage drop for project 1: 3.2% (electrical_repository.py:1353)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Counting compliant voltage drop calculations for project 1 (electrical_repository.py:1379)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - Compliant voltage drop calculations in project 1: 8 (electrical_repository.py:1394)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes for project 1: skip=0, limit=100 (heat_tracing_repository.py:80)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieved 2 pipes for project 1 (heat_tracing_repository.py:99)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Searching for pipe with line tag: L-001 in project 1 (heat_tracing_repository.py:123)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found pipe: 'Test Pipe 001' (ID: 1) (heat_tracing_repository.py:138)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Searching for pipe with line tag: NON-EXISTENT in project 1 (heat_tracing_repository.py:123)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Pipe not found with line tag: NON-EXISTENT (heat_tracing_repository.py:140)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes without circuits for project 1 (heat_tracing_repository.py:162)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 1 pipes without circuits (heat_tracing_repository.py:178)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating heat loss calculation for pipe 1 (heat_tracing_repository.py:242)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Pipe 1 heat loss calculation updated successfully (heat_tracing_repository.py:264)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating heat loss calculation for pipe 999 (heat_tracing_repository.py:242)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Pipe 999 not found for heat loss update (heat_tracing_repository.py:248)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting pipes for project 1 (heat_tracing_repository.py:286)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total pipes in project 1: 0 (heat_tracing_repository.py:296)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting pipes for project 1 (heat_tracing_repository.py:286)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total pipes in project 1: 1 (heat_tracing_repository.py:296)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating heat loss calculation for pipe 1 (heat_tracing_repository.py:242)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Pipe 1 heat loss calculation updated successfully (heat_tracing_repository.py:264)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes with heat loss calculations for project 1 (heat_tracing_repository.py:199)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 1 pipes with heat loss calculations (heat_tracing_repository.py:217)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Searching for vessel with equipment tag: T-001 in project 1 (heat_tracing_repository.py:385)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found vessel: 'Test Vessel T-001' (ID: 1) (heat_tracing_repository.py:400)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving vessels without circuits for project 1 (heat_tracing_repository.py:426)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 1 vessels without circuits (heat_tracing_repository.py:442)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating heat loss calculation for vessel 1 (heat_tracing_repository.py:467)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Vessel 1 heat loss calculation updated successfully (heat_tracing_repository.py:489)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving HT circuits for feeder 1: skip=0, limit=100 (heat_tracing_repository.py:569)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieved 1 HT circuits for feeder 1 (heat_tracing_repository.py:588)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Searching for HT circuit for pipe 1 (heat_tracing_repository.py:611)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found HT circuit: 'HTC-001-A' (ID: 1) (heat_tracing_repository.py:620)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating load calculation for HT circuit 1 (heat_tracing_repository.py:729)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HT circuit 1 load calculation updated successfully (heat_tracing_repository.py:752)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Updating load calculation for HT circuit 1 (heat_tracing_repository.py:729)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HT circuit 1 load calculation updated successfully (heat_tracing_repository.py:752)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Calculating total load for feeder 1 (heat_tracing_repository.py:776)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total load for feeder 1: 1.25 kW (heat_tracing_repository.py:789)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving control circuits for switchboard 1: skip=0, limit=100 (heat_tracing_repository.py:833)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieved 1 control circuits for switchboard 1 (heat_tracing_repository.py:852)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving control circuits with limiting function for switchboard 1 (heat_tracing_repository.py:926)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 1 control circuits with limiting function (heat_tracing_repository.py:942)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HeatTracingRepository aggregate initialized (heat_tracing_repository.py:1016)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HeatTracingRepository aggregate initialized (heat_tracing_repository.py:1016)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Generating heat tracing summary for project 1 (heat_tracing_repository.py:1031)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting pipes for project 1 (heat_tracing_repository.py:286)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total pipes in project 1: 0 (heat_tracing_repository.py:296)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting vessels for project 1 (heat_tracing_repository.py:513)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total vessels in project 1: 0 (heat_tracing_repository.py:523)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes without circuits for project 1 (heat_tracing_repository.py:162)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 pipes without circuits (heat_tracing_repository.py:178)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving vessels without circuits for project 1 (heat_tracing_repository.py:426)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 vessels without circuits (heat_tracing_repository.py:442)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes with heat loss calculations for project 1 (heat_tracing_repository.py:199)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 pipes with heat loss calculations (heat_tracing_repository.py:217)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving vessels without circuits for project 1 (heat_tracing_repository.py:426)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 vessels without circuits (heat_tracing_repository.py:442)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Generated summary for project 1: {'project_id': 1, 'total_pipes': 0, 'total_vessels': 0, 'pipes_without_circuits': 0, 'vessels_without_circuits': 0, 'pipes_with_calculations': 0, 'vessels_with_calculations': 0} (heat_tracing_repository.py:1052)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - PipeRepository initialized (heat_tracing_repository.py:61)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - VesselRepository initialized (heat_tracing_repository.py:321)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HTCircuitRepository initialized (heat_tracing_repository.py:550)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - ControlCircuitRepository initialized (heat_tracing_repository.py:814)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - HeatTracingRepository aggregate initialized (heat_tracing_repository.py:1016)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Checking design readiness for project 1 (heat_tracing_repository.py:1072)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Generating heat tracing summary for project 1 (heat_tracing_repository.py:1031)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting pipes for project 1 (heat_tracing_repository.py:286)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total pipes in project 1: 0 (heat_tracing_repository.py:296)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Counting vessels for project 1 (heat_tracing_repository.py:513)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Total vessels in project 1: 0 (heat_tracing_repository.py:523)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes without circuits for project 1 (heat_tracing_repository.py:162)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 pipes without circuits (heat_tracing_repository.py:178)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving vessels without circuits for project 1 (heat_tracing_repository.py:426)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 vessels without circuits (heat_tracing_repository.py:442)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving pipes with heat loss calculations for project 1 (heat_tracing_repository.py:199)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 pipes with heat loss calculations (heat_tracing_repository.py:217)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Retrieving vessels without circuits for project 1 (heat_tracing_repository.py:426)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Found 0 vessels without circuits (heat_tracing_repository.py:442)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Generated summary for project 1: {'project_id': 1, 'total_pipes': 0, 'total_vessels': 0, 'pipes_without_circuits': 0, 'vessels_without_circuits': 0, 'pipes_with_calculations': 0, 'vessels_with_calculations': 0} (heat_tracing_repository.py:1052)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.heat_tracing_repository - DEBUG - Design readiness for project 1: 0.0% (heat_tracing_repository.py:1114)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Searching for project with code: HT-TEST-001 (project_repository.py:56)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Found project: 'Test Heat Tracing Project' (ID: 1) (project_repository.py:66)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Searching for project with code: NONEXISTENT-001 (project_repository.py:56)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Project not found with code: NONEXISTENT-001 (project_repository.py:63)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Searching for project with name: Test Heat Tracing Project (project_repository.py:89)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Found project: 'Test Heat Tracing Project' (ID: 1) (project_repository.py:96)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Searching for project with name: Nonexistent Project (project_repository.py:89)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Project not found with name: Nonexistent Project (project_repository.py:98)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Retrieving active projects: skip=0, limit=100 (project_repository.py:121)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Retrieved 12 active projects (project_repository.py:133)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Retrieving active projects: skip=2, limit=5 (project_repository.py:121)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Retrieved 5 active projects (project_repository.py:133)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Updating project 1 with data: ['name', 'description', 'max_ambient_temp_c'] (project_repository.py:157)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Project 1 updated successfully (project_repository.py:178)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Updating project 99999 with data: ['name'] (project_repository.py:157)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Project 99999 not found for update (project_repository.py:165)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Soft deleting project 1 (project_repository.py:201)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Project 1 soft deleted successfully (project_repository.py:223)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Soft deleting project 99999 (project_repository.py:201)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Project 99999 not found or already deleted (project_repository.py:226)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Soft deleting project 1 (project_repository.py:201)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Project 1 soft deleted successfully (project_repository.py:223)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Soft deleting project 1 (project_repository.py:201)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Project 1 not found or already deleted (project_repository.py:226)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Searching projects for term: 'Project 1' (project_repository.py:295)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Found 7 projects matching 'Project 1' (project_repository.py:318)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Searching projects for term: 'HT-TEST-001' (project_repository.py:295)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Found 1 projects matching 'HT-TEST-001' (project_repository.py:318)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Searching projects for term: 'description' (project_repository.py:295)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Found 15 projects matching 'description' (project_repository.py:318)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Searching projects for term: 'test project' (project_repository.py:295)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Found 15 projects matching 'test project' (project_repository.py:318)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Searching projects for term: 'TEST PROJECT' (project_repository.py:295)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Found 15 projects matching 'TEST PROJECT' (project_repository.py:318)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Searching projects for term: 'Test Project' (project_repository.py:295)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Found 15 projects matching 'Test Project' (project_repository.py:318)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Searching projects for term: 'Test Project' (project_repository.py:295)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Found 12 projects matching 'Test Project' (project_repository.py:318)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Searching projects for term: 'Project' (project_repository.py:295)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Found 5 projects matching 'Project' (project_repository.py:318)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Searching projects for term: 'Project' (project_repository.py:295)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Found 5 projects matching 'Project' (project_repository.py:318)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Counting active projects (project_repository.py:336)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Total active projects: 12 (project_repository.py:346)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Counting active projects (project_repository.py:336)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Total active projects: 0 (project_repository.py:346)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Retrieving project 1 with related data (project_repository.py:247)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Retrieved project with related data: 'Test Heat Tracing Project' (ID: 1) (project_repository.py:264)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Retrieving project 99999 with related data (project_repository.py:247)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - Project 99999 not found (project_repository.py:268)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-01 23:44:20 - ultimate_electrical_designer.core.repositories.project_repository - DEBUG - ProjectRepository initialized (project_repository.py:40)
2025-06-02 07:35:46 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-02 07:35:46 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-02 07:35:46 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging event: CREATE, user_id=1, entity_type=EntityTypeEnum.PROJECT, entity_id=123 (activity_log_service.py:126)
2025-06-02 07:35:46 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Event logged successfully: CREATE, log_id=1 (activity_log_service.py:153)
2025-06-02 07:35:46 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-02 07:35:46 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging event: SYSTEM_START, user_id=None, entity_type=None, entity_id=None (activity_log_service.py:126)
2025-06-02 07:35:46 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Event logged successfully: SYSTEM_START, log_id=2 (activity_log_service.py:153)
2025-06-02 07:35:46 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-02 07:35:46 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging event: CREATE, user_id=999, entity_type=EntityTypeEnum.PROJECT, entity_id=123 (activity_log_service.py:126)
2025-06-02 07:35:46 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-02 07:35:46 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging user action: user_id=1, action=CREATE, entity_type=Project, entity_id=123 (activity_log_service.py:193)
2025-06-02 07:35:46 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging event: CREATE, user_id=1, entity_type=EntityTypeEnum.PROJECT, entity_id=123 (activity_log_service.py:126)
2025-06-02 07:35:46 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Event logged successfully: CREATE, log_id=1 (activity_log_service.py:153)
2025-06-02 07:35:46 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-02 07:35:46 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging user action: user_id=1, action=INVALID_ACTION, entity_type=Project, entity_id=123 (activity_log_service.py:193)
2025-06-02 07:35:46 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-02 07:35:46 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging user action: user_id=1, action=CREATE, entity_type=InvalidEntity, entity_id=123 (activity_log_service.py:193)
2025-06-02 07:35:46 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-02 07:35:46 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging authentication event: user_id=1, event_type=LOGIN, success=True (activity_log_service.py:248)
2025-06-02 07:35:46 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging event: LOGIN, user_id=1, entity_type=EntityTypeEnum.USER, entity_id=1 (activity_log_service.py:126)
2025-06-02 07:35:46 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Event logged successfully: LOGIN, log_id=1 (activity_log_service.py:153)
2025-06-02 07:35:46 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-02 07:35:46 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging authentication event: user_id=None, event_type=LOGIN, success=False (activity_log_service.py:248)
2025-06-02 07:35:46 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging event: LOGIN_FAILED, user_id=None, entity_type=EntityTypeEnum.USER, entity_id=None (activity_log_service.py:126)
2025-06-02 07:35:46 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Event logged successfully: LOGIN_FAILED, log_id=3 (activity_log_service.py:153)
2025-06-02 07:35:46 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-02 07:35:46 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging system event: event_type=SYSTEM_START (activity_log_service.py:291)
2025-06-02 07:35:46 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging event: SYSTEM_START, user_id=None, entity_type=None, entity_id=None (activity_log_service.py:126)
2025-06-02 07:35:46 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Event logged successfully: SYSTEM_START, log_id=1 (activity_log_service.py:153)
2025-06-02 07:35:46 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-02 07:35:46 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Retrieving activity log: 1 (activity_log_service.py:331)
2025-06-02 07:35:46 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-02 07:35:46 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Retrieving activity log: 999 (activity_log_service.py:331)
2025-06-02 07:35:46 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-02 07:35:46 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Retrieving activity logs for user 1: skip=0, limit=10 (activity_log_service.py:409)
2025-06-02 07:35:46 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-02 07:35:46 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Retrieving activity logs for user 999: skip=0, limit=100 (activity_log_service.py:409)
2025-06-02 07:35:46 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-02 07:35:46 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging security event: event_type=UNAUTHORIZED_ACCESS, user_id=1, severity=HIGH, threat_level=MEDIUM (activity_log_service.py:553)
2025-06-02 07:35:46 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging event: UNAUTHORIZED_ACCESS, user_id=1, entity_type=EntityTypeEnum.USER, entity_id=1 (activity_log_service.py:126)
2025-06-02 07:35:46 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Event logged successfully: UNAUTHORIZED_ACCESS, log_id=1 (activity_log_service.py:153)
2025-06-02 07:35:46 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-02 07:35:46 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging security event: event_type=UNAUTHORIZED_ACCESS, user_id=None, severity=INVALID_SEVERITY, threat_level=LOW (activity_log_service.py:553)
2025-06-02 07:35:46 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-02 07:35:46 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging security event: event_type=UNAUTHORIZED_ACCESS, user_id=None, severity=MEDIUM, threat_level=INVALID_LEVEL (activity_log_service.py:553)
2025-06-02 07:35:46 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-02 07:35:46 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Updating activity log: 1 (activity_log_service.py:791)
2025-06-02 07:35:46 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Activity log updated successfully: 1 (activity_log_service.py:814)
2025-06-02 07:35:46 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-02 07:35:46 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Updating activity log: 999 (activity_log_service.py:791)
2025-06-02 07:35:46 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-02 07:35:46 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Deleting activity logs older than 365 days (activity_log_service.py:841)
2025-06-02 07:35:46 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Deleted 10 old activity logs (activity_log_service.py:852)
2025-06-02 07:35:46 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-02 07:35:46 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Deleting activity logs older than 0 days (activity_log_service.py:841)
2025-06-02 07:35:46 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-02 07:35:46 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Generating audit report: type=user_activity, date_range=2025-05-03 07:35:46.930522 to 2025-06-02 07:35:46.930526 (activity_log_service.py:608)
2025-06-02 07:35:46 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Audit report generated successfully: f21516a7-28f1-4c92-a2de-11b891ec4592, total_events=1 (activity_log_service.py:665)
2025-06-02 07:35:46 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-02 07:35:46 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Generating activity summary for user 1 from 2025-05-03 07:35:46.934144 to 2025-06-02 07:35:46.934148 (activity_log_service.py:752)
2025-06-02 07:35:46 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-02 07:35:46 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Generating activity summary for user 1 from 2025-06-02 07:35:46.937790 to 2025-06-01 07:35:46.937791 (activity_log_service.py:752)
2025-06-02 07:35:46 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-02 07:35:46 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging event: CREATE, user_id=None, entity_type=None, entity_id=None (activity_log_service.py:126)
2025-06-02 07:35:46 - ultimate_electrical_designer.core.services.activity_log_service - ERROR - Failed to log event CREATE: Database error (activity_log_service.py:163)
Traceback (most recent call last):
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\activity_log_service.py", line 149, in log_event
    activity_log = self.activity_log_repo.create(log_dict)
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1167, in __call__
    return self._mock_call(*args, **kwargs)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1171, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1226, in _execute_mock_call
    raise effect
sqlalchemy.exc.SQLAlchemyError: Database error
2025-06-02 07:35:46 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-02 07:35:46 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging event: CREATE, user_id=None, entity_type=None, entity_id=None (activity_log_service.py:126)
2025-06-02 07:35:46 - ultimate_electrical_designer.core.services.activity_log_service - ERROR - Failed to log event CREATE: Repository error (activity_log_service.py:163)
Traceback (most recent call last):
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\activity_log_service.py", line 149, in log_event
    activity_log = self.activity_log_repo.create(log_dict)
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1167, in __call__
    return self._mock_call(*args, **kwargs)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1171, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1226, in _execute_mock_call
    raise effect
Exception: Repository error
2025-06-02 07:35:47 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - CableSegmentRepository initialized (electrical_repository.py:730)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.electrical_service - DEBUG - ElectricalService initialized (electrical_service.py:148)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.electrical_service - INFO - Starting cable sizing calculation for 5.0kW load (electrical_service.py:167)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.electrical_service - INFO - Cable sizing calculation completed: HEATING_CABLE_20W, voltage drop: 0.9% (electrical_service.py:204)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - CableSegmentRepository initialized (electrical_repository.py:730)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.electrical_service - DEBUG - ElectricalService initialized (electrical_service.py:148)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - CableSegmentRepository initialized (electrical_repository.py:730)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.electrical_service - DEBUG - ElectricalService initialized (electrical_service.py:148)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.electrical_service - INFO - Starting electrical standards validation for 1 standards (electrical_service.py:330)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.electrical_service - INFO - Standards validation completed: compliant=True, violations=0, warnings=1 (electrical_service.py:376)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - CableSegmentRepository initialized (electrical_repository.py:730)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.electrical_service - DEBUG - ElectricalService initialized (electrical_service.py:148)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.electrical_service - INFO - Calculating total load for electrical node 1 (electrical_service.py:457)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.electrical_service - INFO - Load calculation completed for node 1: 6.0kW diversified load (electrical_service.py:544)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - CableSegmentRepository initialized (electrical_repository.py:730)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.electrical_service - DEBUG - ElectricalService initialized (electrical_service.py:148)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.electrical_service - INFO - Calculating total load for electrical node 999 (electrical_service.py:457)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - CableSegmentRepository initialized (electrical_repository.py:730)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.electrical_service - DEBUG - ElectricalService initialized (electrical_service.py:148)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.electrical_service - INFO - Starting cable sizing calculation for 5.0kW load (electrical_service.py:167)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.electrical_service - ERROR - Cable sizing calculation failed: Calculation failed (electrical_service.py:213)
Traceback (most recent call last):
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\electrical_service.py", line 183, in perform_cable_sizing_calculation
    calc_result = self.calculation_service.calculate_cable_sizing(calc_input)
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1167, in __call__
    return self._mock_call(*args, **kwargs)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1171, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1226, in _execute_mock_call
    raise effect
Exception: Calculation failed
2025-06-02 07:35:47 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - CableSegmentRepository initialized (electrical_repository.py:730)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.electrical_service - DEBUG - ElectricalService initialized (electrical_service.py:148)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - CableSegmentRepository initialized (electrical_repository.py:730)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.electrical_service - DEBUG - ElectricalService initialized (electrical_service.py:148)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.heat_tracing_service - DEBUG - HeatTracingService initialized with repository: Mock (heat_tracing_service.py:122)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.heat_tracing_service - INFO - Attempting to create new pipe: 'Test Pipe 001' in project 1 (heat_tracing_service.py:145)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.heat_tracing_service - INFO - Pipe 'Test Pipe 001' (ID: 1) created successfully (heat_tracing_service.py:163)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.heat_tracing_service - DEBUG - HeatTracingService initialized with repository: Mock (heat_tracing_service.py:122)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.heat_tracing_service - DEBUG - HeatTracingService initialized with repository: Mock (heat_tracing_service.py:122)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.heat_tracing_service - INFO - Attempting to create new pipe: 'Test Pipe 001' in project 1 (heat_tracing_service.py:145)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.heat_tracing_service - WARNING - Duplicate pipe detected: Test Pipe 001 (heat_tracing_service.py:172)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.heat_tracing_service - DEBUG - HeatTracingService initialized with repository: Mock (heat_tracing_service.py:122)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.heat_tracing_service - DEBUG - Retrieving pipe details for ID: 1 (heat_tracing_service.py:202)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.heat_tracing_service - DEBUG - Pipe found: 'Test Pipe 001' (ID: 1) (heat_tracing_service.py:212)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.heat_tracing_service - DEBUG - HeatTracingService initialized with repository: Mock (heat_tracing_service.py:122)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.heat_tracing_service - DEBUG - Retrieving pipe details for ID: 999 (heat_tracing_service.py:202)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.heat_tracing_service - WARNING - Pipe not found: 999 (heat_tracing_service.py:207)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.heat_tracing_service - DEBUG - HeatTracingService initialized with repository: Mock (heat_tracing_service.py:122)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.heat_tracing_service - DEBUG - Retrieving pipe details for ID: 1 (heat_tracing_service.py:202)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.heat_tracing_service - WARNING - Pipe not found: 1 (heat_tracing_service.py:207)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.heat_tracing_service - DEBUG - HeatTracingService initialized with repository: Mock (heat_tracing_service.py:122)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.heat_tracing_service - INFO - Attempting to update pipe: 1 (heat_tracing_service.py:242)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.heat_tracing_service - INFO - Pipe 'Updated Pipe Name' (ID: 1) updated successfully (heat_tracing_service.py:270)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.heat_tracing_service - DEBUG - HeatTracingService initialized with repository: Mock (heat_tracing_service.py:122)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.heat_tracing_service - INFO - Attempting to update pipe: 999 (heat_tracing_service.py:242)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.heat_tracing_service - DEBUG - HeatTracingService initialized with repository: Mock (heat_tracing_service.py:122)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.heat_tracing_service - INFO - Attempting to delete pipe: 1 (heat_tracing_service.py:307)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.heat_tracing_service - INFO - Pipe 'Test Pipe 001' (ID: 1) deleted successfully (heat_tracing_service.py:327)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.heat_tracing_service - DEBUG - HeatTracingService initialized with repository: Mock (heat_tracing_service.py:122)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.heat_tracing_service - DEBUG - Retrieving pipes list for project 1: page=1, per_page=10 (heat_tracing_service.py:359)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.heat_tracing_service - DEBUG - HeatTracingService initialized with repository: Mock (heat_tracing_service.py:122)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.heat_tracing_service - INFO - Attempting to create new vessel: 'Test Vessel T-001' in project 1 (heat_tracing_service.py:419)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.heat_tracing_service - INFO - Vessel 'Test Vessel T-001' (ID: 1) created successfully (heat_tracing_service.py:437)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.heat_tracing_service - DEBUG - HeatTracingService initialized with repository: Mock (heat_tracing_service.py:122)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.heat_tracing_service - DEBUG - HeatTracingService initialized with repository: Mock (heat_tracing_service.py:122)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.heat_tracing_service - DEBUG - Retrieving vessel details for ID: 1 (heat_tracing_service.py:477)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.heat_tracing_service - DEBUG - Vessel found: 'Test Vessel T-001' (ID: 1) (heat_tracing_service.py:488)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.heat_tracing_service - DEBUG - HeatTracingService initialized with repository: Mock (heat_tracing_service.py:122)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.heat_tracing_service - INFO - Calculating heat loss for pipe 1 (heat_tracing_service.py:582)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.heat_tracing_service - DEBUG - HeatTracingService initialized with repository: Mock (heat_tracing_service.py:122)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.heat_tracing_service - INFO - Calculating heat loss for pipe 999 (heat_tracing_service.py:582)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.heat_tracing_service - DEBUG - HeatTracingService initialized with repository: Mock (heat_tracing_service.py:122)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.heat_tracing_service - INFO - Validating standards compliance (heat_tracing_service.py:644)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.heat_tracing_service - DEBUG - HeatTracingService initialized with repository: Mock (heat_tracing_service.py:122)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.heat_tracing_service - INFO - Executing heat tracing design workflow for project 1 (heat_tracing_service.py:691)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.heat_tracing_service - DEBUG - Processing 2 pipes (heat_tracing_service.py:707)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.heat_tracing_service - WARNING - Failed to design heat tracing for pipe 1: 24 validation errors for PipeReadSchema
created_at
  Input should be a valid datetime [type=datetime_type, input_value=<Mock name='mock.pipes.ge..._at' id='1721936414768'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/datetime_type
updated_at
  Input should be a valid datetime [type=datetime_type, input_value=<Mock name='mock.pipes.ge..._at' id='1721936424176'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/datetime_type
is_deleted
  Input should be a valid boolean [type=bool_type, input_value=<Mock name='mock.pipes.ge...ted' id='1721936422832'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/bool_type
deleted_at
  Input should be a valid datetime [type=datetime_type, input_value=<Mock name='mock.pipes.ge..._at' id='1721936423168'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/datetime_type
deleted_by_user_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock.pipes.ge..._id' id='1721936423504'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
nominal_diameter_mm
  Input should be a valid number [type=float_type, input_value=<Mock name='mock.pipes.ge..._mm' id='1721936423840'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/float_type
wall_thickness_mm
  Input should be a valid number [type=float_type, input_value=<Mock name='mock.pipes.ge..._mm' id='1721936418800'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/float_type
fluid_type
  Input should be a valid string [type=string_type, input_value=<Mock name='mock.pipes.ge...ype' id='1721936424512'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
specific_heat_capacity_jkgc
  Input should be a valid number [type=float_type, input_value=<Mock name='mock.pipes.ge...kgc' id='1721936412752'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/float_type
viscosity_cp
  Input should be a valid number [type=float_type, input_value=<Mock name='mock.pipes.ge..._cp' id='1721936417120'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/float_type
freezing_point_c
  Input should be a valid number [type=float_type, input_value=<Mock name='mock.pipes.ge...t_c' id='1721936419136'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/float_type
safety_margin_percent
  Input should be a valid number [type=float_type, input_value=<Mock name='mock.pipes.ge...ent' id='1721936419472'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/float_type
pid
  Input should be a valid string [type=string_type, input_value=<Mock name='mock.pipes.ge...pid' id='1721936417456'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
line_tag
  Input should be a valid string [type=string_type, input_value=<Mock name='mock.pipes.ge...tag' id='1721936418128'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
from_location
  Input should be a valid string [type=string_type, input_value=<Mock name='mock.pipes.ge...ion' id='1721936422496'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
to_location
  Input should be a valid string [type=string_type, input_value=<Mock name='mock.pipes.ge...ion' id='1721936428208'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
valve_count
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock.pipes.ge...unt' id='1721936887888'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
support_count
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock.pipes.ge...unt' id='1721936890576'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
project_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock.pipes.ge..._id' id='1721936888224'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
pipe_material_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock.pipes.ge..._id' id='1721936889904'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
insulation_material_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock.pipes.ge..._id' id='1721936890240'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
calculated_heat_loss_wm
  Input should be a valid number [type=float_type, input_value=<Mock name='mock.pipes.ge..._wm' id='1721936891248'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/float_type
required_heat_output_wm
  Input should be a valid number [type=float_type, input_value=<Mock name='mock.pipes.ge..._wm' id='1721936890912'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/float_type
imported_data_revision_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock.pipes.ge..._id' id='1721936894944'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type (heat_tracing_service.py:720)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.heat_tracing_service - WARNING - Failed to design heat tracing for pipe 2: 24 validation errors for PipeReadSchema
created_at
  Input should be a valid datetime [type=datetime_type, input_value=<Mock name='mock.pipes.ge..._at' id='1721936414768'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/datetime_type
updated_at
  Input should be a valid datetime [type=datetime_type, input_value=<Mock name='mock.pipes.ge..._at' id='1721936424176'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/datetime_type
is_deleted
  Input should be a valid boolean [type=bool_type, input_value=<Mock name='mock.pipes.ge...ted' id='1721936422832'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/bool_type
deleted_at
  Input should be a valid datetime [type=datetime_type, input_value=<Mock name='mock.pipes.ge..._at' id='1721936423168'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/datetime_type
deleted_by_user_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock.pipes.ge..._id' id='1721936423504'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
nominal_diameter_mm
  Input should be a valid number [type=float_type, input_value=<Mock name='mock.pipes.ge..._mm' id='1721936423840'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/float_type
wall_thickness_mm
  Input should be a valid number [type=float_type, input_value=<Mock name='mock.pipes.ge..._mm' id='1721936418800'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/float_type
fluid_type
  Input should be a valid string [type=string_type, input_value=<Mock name='mock.pipes.ge...ype' id='1721936424512'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
specific_heat_capacity_jkgc
  Input should be a valid number [type=float_type, input_value=<Mock name='mock.pipes.ge...kgc' id='1721936412752'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/float_type
viscosity_cp
  Input should be a valid number [type=float_type, input_value=<Mock name='mock.pipes.ge..._cp' id='1721936417120'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/float_type
freezing_point_c
  Input should be a valid number [type=float_type, input_value=<Mock name='mock.pipes.ge...t_c' id='1721936419136'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/float_type
safety_margin_percent
  Input should be a valid number [type=float_type, input_value=<Mock name='mock.pipes.ge...ent' id='1721936419472'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/float_type
pid
  Input should be a valid string [type=string_type, input_value=<Mock name='mock.pipes.ge...pid' id='1721936417456'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
line_tag
  Input should be a valid string [type=string_type, input_value=<Mock name='mock.pipes.ge...tag' id='1721936418128'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
from_location
  Input should be a valid string [type=string_type, input_value=<Mock name='mock.pipes.ge...ion' id='1721936422496'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
to_location
  Input should be a valid string [type=string_type, input_value=<Mock name='mock.pipes.ge...ion' id='1721936428208'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
valve_count
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock.pipes.ge...unt' id='1721936887888'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
support_count
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock.pipes.ge...unt' id='1721936890576'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
project_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock.pipes.ge..._id' id='1721936888224'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
pipe_material_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock.pipes.ge..._id' id='1721936889904'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
insulation_material_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock.pipes.ge..._id' id='1721936890240'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
calculated_heat_loss_wm
  Input should be a valid number [type=float_type, input_value=<Mock name='mock.pipes.ge..._wm' id='1721936891248'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/float_type
required_heat_output_wm
  Input should be a valid number [type=float_type, input_value=<Mock name='mock.pipes.ge..._wm' id='1721936890912'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/float_type
imported_data_revision_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock.pipes.ge..._id' id='1721936894944'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type (heat_tracing_service.py:720)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.heat_tracing_service - INFO - Heat tracing design workflow completed for project 1 (heat_tracing_service.py:749)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.project_service - DEBUG - ProjectService initialized with repository: Mock (project_service.py:53)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.project_service - INFO - Attempting to create new project: 'Test Heat Tracing Project' (HT-TEST-001) (project_service.py:72)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.project_service - INFO - Project 'Test Heat Tracing Project' (ID: 1) created successfully (project_service.py:90)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.project_service - DEBUG - ProjectService initialized with repository: Mock (project_service.py:53)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.project_service - INFO - Attempting to create new project: 'Test Project' (TEST-001) (project_service.py:72)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.project_service - ERROR - Unexpected error creating project 'Test Project': Input validation failed. (project_service.py:133)
Traceback (most recent call last):
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\project_service.py", line 78, in create_project
    self._validate_project_creation(project_data)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\project_service.py", line 443, in _validate_project_creation
    raise DataValidationError(
    ...<3 lines>...
    )
core.errors.exceptions.DataValidationError: Input validation failed.
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.project_service - DEBUG - ProjectService initialized with repository: Mock (project_service.py:53)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.project_service - INFO - Attempting to create new project: 'Test Heat Tracing Project' (HT-TEST-001) (project_service.py:72)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.project_service - WARNING - Duplicate project detected: Test Heat Tracing Project or HT-TEST-001 (project_service.py:99)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.project_service - DEBUG - ProjectService initialized with repository: Mock (project_service.py:53)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.project_service - DEBUG - Retrieving project details for ID: 1 (project_service.py:153)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.project_service - DEBUG - Project found: 'Test Heat Tracing Project' (ID: 1) (project_service.py:178)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.project_service - DEBUG - ProjectService initialized with repository: Mock (project_service.py:53)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.project_service - DEBUG - Retrieving project details for ID: HT-TEST-001 (project_service.py:153)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.project_service - DEBUG - Project found: 'Test Heat Tracing Project' (ID: 1) (project_service.py:178)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.project_service - DEBUG - ProjectService initialized with repository: Mock (project_service.py:53)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.project_service - DEBUG - Retrieving project details for ID: TEST-001 (project_service.py:153)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.project_service - WARNING - Project not found: TEST-001 (project_service.py:170)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.project_service - DEBUG - ProjectService initialized with repository: Mock (project_service.py:53)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.project_service - INFO - Attempting to update project: 1 (project_service.py:210)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.project_service - INFO - Project 'Updated Test Project' (ID: 1) updated successfully (project_service.py:234)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.project_service - DEBUG - ProjectService initialized with repository: Mock (project_service.py:53)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.project_service - INFO - Attempting to delete project: 1 (project_service.py:288)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.project_service - INFO - Project 'Test Heat Tracing Project' (ID: 1) deleted successfully (project_service.py:308)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.project_service - DEBUG - ProjectService initialized with repository: Mock (project_service.py:53)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.project_service - DEBUG - Retrieving projects list: page=1, per_page=10, include_deleted=False (project_service.py:342)
2025-06-02 07:35:47 - ultimate_electrical_designer.core.services.project_service - DEBUG - Retrieved 5 projects (total: 5) (project_service.py:375)
2025-06-02 07:38:34 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-02 07:38:34 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-02 07:38:34 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging event: CREATE, user_id=1, entity_type=EntityTypeEnum.PROJECT, entity_id=123 (activity_log_service.py:126)
2025-06-02 07:38:34 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Event logged successfully: CREATE, log_id=1 (activity_log_service.py:153)
2025-06-02 07:38:34 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-02 07:38:34 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging event: SYSTEM_START, user_id=None, entity_type=None, entity_id=None (activity_log_service.py:126)
2025-06-02 07:38:34 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Event logged successfully: SYSTEM_START, log_id=2 (activity_log_service.py:153)
2025-06-02 07:38:34 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-02 07:38:34 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging event: CREATE, user_id=999, entity_type=EntityTypeEnum.PROJECT, entity_id=123 (activity_log_service.py:126)
2025-06-02 07:38:34 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-02 07:38:34 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging user action: user_id=1, action=CREATE, entity_type=Project, entity_id=123 (activity_log_service.py:193)
2025-06-02 07:38:34 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging event: CREATE, user_id=1, entity_type=EntityTypeEnum.PROJECT, entity_id=123 (activity_log_service.py:126)
2025-06-02 07:38:34 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Event logged successfully: CREATE, log_id=1 (activity_log_service.py:153)
2025-06-02 07:38:34 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-02 07:38:34 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging user action: user_id=1, action=INVALID_ACTION, entity_type=Project, entity_id=123 (activity_log_service.py:193)
2025-06-02 07:38:34 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-02 07:38:34 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging user action: user_id=1, action=CREATE, entity_type=InvalidEntity, entity_id=123 (activity_log_service.py:193)
2025-06-02 07:38:34 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-02 07:38:34 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging authentication event: user_id=1, event_type=LOGIN, success=True (activity_log_service.py:248)
2025-06-02 07:38:34 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging event: LOGIN, user_id=1, entity_type=EntityTypeEnum.USER, entity_id=1 (activity_log_service.py:126)
2025-06-02 07:38:34 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Event logged successfully: LOGIN, log_id=1 (activity_log_service.py:153)
2025-06-02 07:38:34 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-02 07:38:34 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging authentication event: user_id=None, event_type=LOGIN, success=False (activity_log_service.py:248)
2025-06-02 07:38:34 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging event: LOGIN_FAILED, user_id=None, entity_type=EntityTypeEnum.USER, entity_id=None (activity_log_service.py:126)
2025-06-02 07:38:34 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Event logged successfully: LOGIN_FAILED, log_id=3 (activity_log_service.py:153)
2025-06-02 07:38:34 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-02 07:38:34 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging system event: event_type=SYSTEM_START (activity_log_service.py:291)
2025-06-02 07:38:34 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging event: SYSTEM_START, user_id=None, entity_type=None, entity_id=None (activity_log_service.py:126)
2025-06-02 07:38:34 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Event logged successfully: SYSTEM_START, log_id=1 (activity_log_service.py:153)
2025-06-02 07:38:34 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-02 07:38:34 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Retrieving activity log: 1 (activity_log_service.py:331)
2025-06-02 07:38:34 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-02 07:38:34 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Retrieving activity log: 999 (activity_log_service.py:331)
2025-06-02 07:38:34 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-02 07:38:34 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Retrieving activity logs for user 1: skip=0, limit=10 (activity_log_service.py:409)
2025-06-02 07:38:34 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-02 07:38:34 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Retrieving activity logs for user 999: skip=0, limit=100 (activity_log_service.py:409)
2025-06-02 07:38:34 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-02 07:38:34 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging security event: event_type=UNAUTHORIZED_ACCESS, user_id=1, severity=HIGH, threat_level=MEDIUM (activity_log_service.py:553)
2025-06-02 07:38:34 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging event: UNAUTHORIZED_ACCESS, user_id=1, entity_type=EntityTypeEnum.USER, entity_id=1 (activity_log_service.py:126)
2025-06-02 07:38:34 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Event logged successfully: UNAUTHORIZED_ACCESS, log_id=1 (activity_log_service.py:153)
2025-06-02 07:38:34 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-02 07:38:34 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging security event: event_type=UNAUTHORIZED_ACCESS, user_id=None, severity=INVALID_SEVERITY, threat_level=LOW (activity_log_service.py:553)
2025-06-02 07:38:34 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-02 07:38:34 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging security event: event_type=UNAUTHORIZED_ACCESS, user_id=None, severity=MEDIUM, threat_level=INVALID_LEVEL (activity_log_service.py:553)
2025-06-02 07:38:34 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-02 07:38:34 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Updating activity log: 1 (activity_log_service.py:791)
2025-06-02 07:38:34 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Activity log updated successfully: 1 (activity_log_service.py:814)
2025-06-02 07:38:34 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-02 07:38:34 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Updating activity log: 999 (activity_log_service.py:791)
2025-06-02 07:38:34 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-02 07:38:34 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Deleting activity logs older than 365 days (activity_log_service.py:841)
2025-06-02 07:38:34 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Deleted 10 old activity logs (activity_log_service.py:852)
2025-06-02 07:38:34 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-02 07:38:34 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Deleting activity logs older than 0 days (activity_log_service.py:841)
2025-06-02 07:38:34 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-02 07:38:34 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Generating audit report: type=user_activity, date_range=2025-05-03 07:38:34.616132 to 2025-06-02 07:38:34.616135 (activity_log_service.py:608)
2025-06-02 07:38:34 - ultimate_electrical_designer.core.services.activity_log_service - INFO - Audit report generated successfully: 289c8c95-f6e9-43b6-8df1-1d4a18bf0ea2, total_events=1 (activity_log_service.py:665)
2025-06-02 07:38:34 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-02 07:38:34 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Generating activity summary for user 1 from 2025-05-03 07:38:34.618718 to 2025-06-02 07:38:34.618721 (activity_log_service.py:752)
2025-06-02 07:38:34 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-02 07:38:34 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Generating activity summary for user 1 from 2025-06-02 07:38:34.621206 to 2025-06-01 07:38:34.621207 (activity_log_service.py:752)
2025-06-02 07:38:34 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-02 07:38:34 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging event: CREATE, user_id=None, entity_type=None, entity_id=None (activity_log_service.py:126)
2025-06-02 07:38:34 - ultimate_electrical_designer.core.services.activity_log_service - ERROR - Failed to log event CREATE: Database error (activity_log_service.py:163)
Traceback (most recent call last):
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\activity_log_service.py", line 149, in log_event
    activity_log = self.activity_log_repo.create(log_dict)
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1167, in __call__
    return self._mock_call(*args, **kwargs)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1171, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1226, in _execute_mock_call
    raise effect
sqlalchemy.exc.SQLAlchemyError: Database error
2025-06-02 07:38:34 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - ActivityLogService initialized (activity_log_service.py:95)
2025-06-02 07:38:34 - ultimate_electrical_designer.core.services.activity_log_service - DEBUG - Logging event: CREATE, user_id=None, entity_type=None, entity_id=None (activity_log_service.py:126)
2025-06-02 07:38:34 - ultimate_electrical_designer.core.services.activity_log_service - ERROR - Failed to log event CREATE: Repository error (activity_log_service.py:163)
Traceback (most recent call last):
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\activity_log_service.py", line 149, in log_event
    activity_log = self.activity_log_repo.create(log_dict)
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1167, in __call__
    return self._mock_call(*args, **kwargs)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1171, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1226, in _execute_mock_call
    raise effect
Exception: Repository error
2025-06-02 07:38:35 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - CableSegmentRepository initialized (electrical_repository.py:730)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.electrical_service - DEBUG - ElectricalService initialized (electrical_service.py:148)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.electrical_service - INFO - Starting cable sizing calculation for 5.0kW load (electrical_service.py:167)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.electrical_service - INFO - Cable sizing calculation completed: HEATING_CABLE_20W, voltage drop: 0.9% (electrical_service.py:204)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - CableSegmentRepository initialized (electrical_repository.py:730)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.electrical_service - DEBUG - ElectricalService initialized (electrical_service.py:148)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.electrical_service - INFO - Starting voltage drop calculation for 20.8A load over 100.0m (electrical_service.py:236)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.electrical_service - INFO - Voltage drop calculation completed: 0.9%, compliant: True (electrical_service.py:299)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - CableSegmentRepository initialized (electrical_repository.py:730)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.electrical_service - DEBUG - ElectricalService initialized (electrical_service.py:148)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.electrical_service - INFO - Starting electrical standards validation for 1 standards (electrical_service.py:330)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.electrical_service - INFO - Standards validation completed: compliant=True, violations=0, warnings=1 (electrical_service.py:376)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - CableSegmentRepository initialized (electrical_repository.py:730)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.electrical_service - DEBUG - ElectricalService initialized (electrical_service.py:148)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.electrical_service - INFO - Calculating total load for electrical node 1 (electrical_service.py:457)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.electrical_service - INFO - Load calculation completed for node 1: 6.0kW diversified load (electrical_service.py:544)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - CableSegmentRepository initialized (electrical_repository.py:730)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.electrical_service - DEBUG - ElectricalService initialized (electrical_service.py:148)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.electrical_service - INFO - Calculating total load for electrical node 999 (electrical_service.py:457)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - CableSegmentRepository initialized (electrical_repository.py:730)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.electrical_service - DEBUG - ElectricalService initialized (electrical_service.py:148)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.electrical_service - INFO - Starting cable sizing calculation for 5.0kW load (electrical_service.py:167)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.electrical_service - ERROR - Cable sizing calculation failed: Calculation failed (electrical_service.py:213)
Traceback (most recent call last):
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\electrical_service.py", line 183, in perform_cable_sizing_calculation
    calc_result = self.calculation_service.calculate_cable_sizing(calc_input)
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1167, in __call__
    return self._mock_call(*args, **kwargs)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1171, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1226, in _execute_mock_call
    raise effect
Exception: Calculation failed
2025-06-02 07:38:35 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - CableSegmentRepository initialized (electrical_repository.py:730)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.electrical_service - DEBUG - ElectricalService initialized (electrical_service.py:148)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.electrical_service - INFO - Starting voltage drop calculation for 20.8A load over 100.0m (electrical_service.py:236)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.electrical_service - ERROR - Voltage drop calculation failed: Voltage drop calculation failed (electrical_service.py:308)
Traceback (most recent call last):
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\electrical_service.py", line 242, in perform_voltage_drop_calculation
    voltage_drop_v = calculate_voltage_drop(
        current=inputs.load_current_a,
    ...<3 lines>...
        cable_reactance=inputs.cable_reactance_ohm_per_m,
    )
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1167, in __call__
    return self._mock_call(*args, **kwargs)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1171, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\unittest\mock.py", line 1226, in _execute_mock_call
    raise effect
Exception: Voltage drop calculation failed
2025-06-02 07:38:35 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - ElectricalNodeRepository initialized (electrical_repository.py:63)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - CableRouteRepository initialized (electrical_repository.py:392)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - CableSegmentRepository initialized (electrical_repository.py:730)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - LoadCalculationRepository initialized (electrical_repository.py:925)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.repositories.electrical_repository - DEBUG - VoltageDropCalculationRepository initialized (electrical_repository.py:1130)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.electrical_service - DEBUG - ElectricalService initialized (electrical_service.py:148)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.heat_tracing_service - DEBUG - HeatTracingService initialized with repository: Mock (heat_tracing_service.py:122)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.heat_tracing_service - INFO - Attempting to create new pipe: 'Test Pipe 001' in project 1 (heat_tracing_service.py:145)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.heat_tracing_service - INFO - Pipe 'Test Pipe 001' (ID: 1) created successfully (heat_tracing_service.py:163)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.heat_tracing_service - DEBUG - HeatTracingService initialized with repository: Mock (heat_tracing_service.py:122)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.heat_tracing_service - DEBUG - HeatTracingService initialized with repository: Mock (heat_tracing_service.py:122)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.heat_tracing_service - INFO - Attempting to create new pipe: 'Test Pipe 001' in project 1 (heat_tracing_service.py:145)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.heat_tracing_service - WARNING - Duplicate pipe detected: Test Pipe 001 (heat_tracing_service.py:172)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.heat_tracing_service - DEBUG - HeatTracingService initialized with repository: Mock (heat_tracing_service.py:122)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.heat_tracing_service - DEBUG - Retrieving pipe details for ID: 1 (heat_tracing_service.py:202)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.heat_tracing_service - DEBUG - Pipe found: 'Test Pipe 001' (ID: 1) (heat_tracing_service.py:212)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.heat_tracing_service - DEBUG - HeatTracingService initialized with repository: Mock (heat_tracing_service.py:122)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.heat_tracing_service - DEBUG - Retrieving pipe details for ID: 999 (heat_tracing_service.py:202)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.heat_tracing_service - WARNING - Pipe not found: 999 (heat_tracing_service.py:207)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.heat_tracing_service - DEBUG - HeatTracingService initialized with repository: Mock (heat_tracing_service.py:122)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.heat_tracing_service - DEBUG - Retrieving pipe details for ID: 1 (heat_tracing_service.py:202)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.heat_tracing_service - WARNING - Pipe not found: 1 (heat_tracing_service.py:207)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.heat_tracing_service - DEBUG - HeatTracingService initialized with repository: Mock (heat_tracing_service.py:122)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.heat_tracing_service - INFO - Attempting to update pipe: 1 (heat_tracing_service.py:242)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.heat_tracing_service - INFO - Pipe 'Updated Pipe Name' (ID: 1) updated successfully (heat_tracing_service.py:270)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.heat_tracing_service - DEBUG - HeatTracingService initialized with repository: Mock (heat_tracing_service.py:122)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.heat_tracing_service - INFO - Attempting to update pipe: 999 (heat_tracing_service.py:242)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.heat_tracing_service - DEBUG - HeatTracingService initialized with repository: Mock (heat_tracing_service.py:122)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.heat_tracing_service - INFO - Attempting to delete pipe: 1 (heat_tracing_service.py:307)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.heat_tracing_service - INFO - Pipe 'Test Pipe 001' (ID: 1) deleted successfully (heat_tracing_service.py:327)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.heat_tracing_service - DEBUG - HeatTracingService initialized with repository: Mock (heat_tracing_service.py:122)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.heat_tracing_service - DEBUG - Retrieving pipes list for project 1: page=1, per_page=10 (heat_tracing_service.py:359)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.heat_tracing_service - DEBUG - HeatTracingService initialized with repository: Mock (heat_tracing_service.py:122)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.heat_tracing_service - INFO - Attempting to create new vessel: 'Test Vessel T-001' in project 1 (heat_tracing_service.py:419)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.heat_tracing_service - INFO - Vessel 'Test Vessel T-001' (ID: 1) created successfully (heat_tracing_service.py:437)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.heat_tracing_service - DEBUG - HeatTracingService initialized with repository: Mock (heat_tracing_service.py:122)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.heat_tracing_service - DEBUG - HeatTracingService initialized with repository: Mock (heat_tracing_service.py:122)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.heat_tracing_service - DEBUG - Retrieving vessel details for ID: 1 (heat_tracing_service.py:477)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.heat_tracing_service - DEBUG - Vessel found: 'Test Vessel T-001' (ID: 1) (heat_tracing_service.py:488)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.heat_tracing_service - DEBUG - HeatTracingService initialized with repository: Mock (heat_tracing_service.py:122)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.heat_tracing_service - INFO - Calculating heat loss for pipe 1 (heat_tracing_service.py:582)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.heat_tracing_service - DEBUG - HeatTracingService initialized with repository: Mock (heat_tracing_service.py:122)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.heat_tracing_service - INFO - Calculating heat loss for pipe 999 (heat_tracing_service.py:582)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.heat_tracing_service - DEBUG - HeatTracingService initialized with repository: Mock (heat_tracing_service.py:122)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.heat_tracing_service - INFO - Validating standards compliance (heat_tracing_service.py:644)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.heat_tracing_service - DEBUG - HeatTracingService initialized with repository: Mock (heat_tracing_service.py:122)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.heat_tracing_service - INFO - Executing heat tracing design workflow for project 1 (heat_tracing_service.py:691)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.heat_tracing_service - DEBUG - Processing 2 pipes (heat_tracing_service.py:707)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.heat_tracing_service - WARNING - Failed to design heat tracing for pipe 1: 24 validation errors for PipeReadSchema
created_at
  Input should be a valid datetime [type=datetime_type, input_value=<Mock name='mock.pipes.ge..._at' id='2235780563696'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/datetime_type
updated_at
  Input should be a valid datetime [type=datetime_type, input_value=<Mock name='mock.pipes.ge..._at' id='2235780563360'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/datetime_type
is_deleted
  Input should be a valid boolean [type=bool_type, input_value=<Mock name='mock.pipes.ge...ted' id='2235780565376'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/bool_type
deleted_at
  Input should be a valid datetime [type=datetime_type, input_value=<Mock name='mock.pipes.ge..._at' id='2235780563024'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/datetime_type
deleted_by_user_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock.pipes.ge..._id' id='2235780569408'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
nominal_diameter_mm
  Input should be a valid number [type=float_type, input_value=<Mock name='mock.pipes.ge..._mm' id='2235780565040'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/float_type
wall_thickness_mm
  Input should be a valid number [type=float_type, input_value=<Mock name='mock.pipes.ge..._mm' id='2235780568400'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/float_type
fluid_type
  Input should be a valid string [type=string_type, input_value=<Mock name='mock.pipes.ge...ype' id='2235780571424'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
specific_heat_capacity_jkgc
  Input should be a valid number [type=float_type, input_value=<Mock name='mock.pipes.ge...kgc' id='2235780567728'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/float_type
viscosity_cp
  Input should be a valid number [type=float_type, input_value=<Mock name='mock.pipes.ge..._cp' id='2235780571088'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/float_type
freezing_point_c
  Input should be a valid number [type=float_type, input_value=<Mock name='mock.pipes.ge...t_c' id='2235780568064'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/float_type
safety_margin_percent
  Input should be a valid number [type=float_type, input_value=<Mock name='mock.pipes.ge...ent' id='2235780572768'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/float_type
pid
  Input should be a valid string [type=string_type, input_value=<Mock name='mock.pipes.ge...pid' id='2235780571760'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
line_tag
  Input should be a valid string [type=string_type, input_value=<Mock name='mock.pipes.ge...tag' id='2235780572432'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
from_location
  Input should be a valid string [type=string_type, input_value=<Mock name='mock.pipes.ge...ion' id='2235780572096'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
to_location
  Input should be a valid string [type=string_type, input_value=<Mock name='mock.pipes.ge...ion' id='2235780567392'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
valve_count
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock.pipes.ge...unt' id='2235780574448'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
support_count
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock.pipes.ge...unt' id='2235780578144'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
project_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock.pipes.ge..._id' id='2235780576464'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
pipe_material_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock.pipes.ge..._id' id='2235780573440'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
insulation_material_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock.pipes.ge..._id' id='2235780575792'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
calculated_heat_loss_wm
  Input should be a valid number [type=float_type, input_value=<Mock name='mock.pipes.ge..._wm' id='2235780577808'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/float_type
required_heat_output_wm
  Input should be a valid number [type=float_type, input_value=<Mock name='mock.pipes.ge..._wm' id='2235780578480'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/float_type
imported_data_revision_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock.pipes.ge..._id' id='2235780972624'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type (heat_tracing_service.py:720)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.heat_tracing_service - WARNING - Failed to design heat tracing for pipe 2: 24 validation errors for PipeReadSchema
created_at
  Input should be a valid datetime [type=datetime_type, input_value=<Mock name='mock.pipes.ge..._at' id='2235780563696'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/datetime_type
updated_at
  Input should be a valid datetime [type=datetime_type, input_value=<Mock name='mock.pipes.ge..._at' id='2235780563360'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/datetime_type
is_deleted
  Input should be a valid boolean [type=bool_type, input_value=<Mock name='mock.pipes.ge...ted' id='2235780565376'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/bool_type
deleted_at
  Input should be a valid datetime [type=datetime_type, input_value=<Mock name='mock.pipes.ge..._at' id='2235780563024'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/datetime_type
deleted_by_user_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock.pipes.ge..._id' id='2235780569408'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
nominal_diameter_mm
  Input should be a valid number [type=float_type, input_value=<Mock name='mock.pipes.ge..._mm' id='2235780565040'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/float_type
wall_thickness_mm
  Input should be a valid number [type=float_type, input_value=<Mock name='mock.pipes.ge..._mm' id='2235780568400'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/float_type
fluid_type
  Input should be a valid string [type=string_type, input_value=<Mock name='mock.pipes.ge...ype' id='2235780571424'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
specific_heat_capacity_jkgc
  Input should be a valid number [type=float_type, input_value=<Mock name='mock.pipes.ge...kgc' id='2235780567728'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/float_type
viscosity_cp
  Input should be a valid number [type=float_type, input_value=<Mock name='mock.pipes.ge..._cp' id='2235780571088'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/float_type
freezing_point_c
  Input should be a valid number [type=float_type, input_value=<Mock name='mock.pipes.ge...t_c' id='2235780568064'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/float_type
safety_margin_percent
  Input should be a valid number [type=float_type, input_value=<Mock name='mock.pipes.ge...ent' id='2235780572768'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/float_type
pid
  Input should be a valid string [type=string_type, input_value=<Mock name='mock.pipes.ge...pid' id='2235780571760'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
line_tag
  Input should be a valid string [type=string_type, input_value=<Mock name='mock.pipes.ge...tag' id='2235780572432'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
from_location
  Input should be a valid string [type=string_type, input_value=<Mock name='mock.pipes.ge...ion' id='2235780572096'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
to_location
  Input should be a valid string [type=string_type, input_value=<Mock name='mock.pipes.ge...ion' id='2235780567392'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
valve_count
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock.pipes.ge...unt' id='2235780574448'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
support_count
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock.pipes.ge...unt' id='2235780578144'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
project_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock.pipes.ge..._id' id='2235780576464'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
pipe_material_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock.pipes.ge..._id' id='2235780573440'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
insulation_material_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock.pipes.ge..._id' id='2235780575792'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
calculated_heat_loss_wm
  Input should be a valid number [type=float_type, input_value=<Mock name='mock.pipes.ge..._wm' id='2235780577808'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/float_type
required_heat_output_wm
  Input should be a valid number [type=float_type, input_value=<Mock name='mock.pipes.ge..._wm' id='2235780578480'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/float_type
imported_data_revision_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock.pipes.ge..._id' id='2235780972624'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type (heat_tracing_service.py:720)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.heat_tracing_service - INFO - Heat tracing design workflow completed for project 1 (heat_tracing_service.py:749)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.project_service - DEBUG - ProjectService initialized with repository: Mock (project_service.py:53)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.project_service - INFO - Attempting to create new project: 'Test Heat Tracing Project' (HT-TEST-001) (project_service.py:72)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.project_service - INFO - Project 'Test Heat Tracing Project' (ID: 1) created successfully (project_service.py:90)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.project_service - DEBUG - ProjectService initialized with repository: Mock (project_service.py:53)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.project_service - INFO - Attempting to create new project: 'Test Project' (TEST-001) (project_service.py:72)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.project_service - ERROR - Unexpected error creating project 'Test Project': Input validation failed. (project_service.py:133)
Traceback (most recent call last):
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\project_service.py", line 78, in create_project
    self._validate_project_creation(project_data)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\project_service.py", line 443, in _validate_project_creation
    raise DataValidationError(
    ...<3 lines>...
    )
core.errors.exceptions.DataValidationError: Input validation failed.
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.project_service - DEBUG - ProjectService initialized with repository: Mock (project_service.py:53)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.project_service - INFO - Attempting to create new project: 'Test Heat Tracing Project' (HT-TEST-001) (project_service.py:72)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.project_service - WARNING - Duplicate project detected: Test Heat Tracing Project or HT-TEST-001 (project_service.py:99)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.project_service - DEBUG - ProjectService initialized with repository: Mock (project_service.py:53)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.project_service - DEBUG - Retrieving project details for ID: 1 (project_service.py:153)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.project_service - DEBUG - Project found: 'Test Heat Tracing Project' (ID: 1) (project_service.py:178)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.project_service - DEBUG - ProjectService initialized with repository: Mock (project_service.py:53)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.project_service - DEBUG - Retrieving project details for ID: HT-TEST-001 (project_service.py:153)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.project_service - DEBUG - Project found: 'Test Heat Tracing Project' (ID: 1) (project_service.py:178)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.project_service - DEBUG - ProjectService initialized with repository: Mock (project_service.py:53)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.project_service - DEBUG - Retrieving project details for ID: TEST-001 (project_service.py:153)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.project_service - WARNING - Project not found: TEST-001 (project_service.py:170)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.project_service - DEBUG - ProjectService initialized with repository: Mock (project_service.py:53)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.project_service - INFO - Attempting to update project: 1 (project_service.py:210)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.project_service - INFO - Project 'Updated Test Project' (ID: 1) updated successfully (project_service.py:234)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.project_service - DEBUG - ProjectService initialized with repository: Mock (project_service.py:53)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.project_service - INFO - Attempting to delete project: 1 (project_service.py:288)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.project_service - INFO - Project 'Test Heat Tracing Project' (ID: 1) deleted successfully (project_service.py:308)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.project_service - DEBUG - ProjectService initialized with repository: Mock (project_service.py:53)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.project_service - DEBUG - Retrieving projects list: page=1, per_page=10, include_deleted=False (project_service.py:342)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.project_service - DEBUG - Retrieved 5 projects (total: 5) (project_service.py:375)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.switchboard_service - DEBUG - SwitchboardService initialized (switchboard_service.py:110)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.switchboard_service - INFO - Creating switchboard: Test Switchboard (switchboard_service.py:132)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.switchboard_service - INFO - Switchboard created successfully: 1 (switchboard_service.py:150)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.switchboard_service - ERROR - Failed to create switchboard: 10 validation errors for SwitchboardReadSchema
created_at
  Input should be a valid datetime [type=datetime_type, input_value=<Mock name='mock().create..._at' id='2235780982032'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/datetime_type
updated_at
  Input should be a valid datetime [type=datetime_type, input_value=<Mock name='mock().create..._at' id='2235780982704'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/datetime_type
is_deleted
  Input should be a valid boolean [type=bool_type, input_value=<Mock name='mock().create...ted' id='2235780981360'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/bool_type
deleted_at
  Input should be a valid datetime [type=datetime_type, input_value=<Mock name='mock().create..._at' id='2235780981696'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/datetime_type
deleted_by_user_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock().create..._id' id='2235780982368'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
location
  Input should be a valid string [type=string_type, input_value=<Mock name='mock().create...ion' id='2235780980688'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
voltage_level_v
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock().create...l_v' id='2235780981024'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
number_of_phases
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock().create...ses' id='2235780977328'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
type
  Input should be 'Main' or 'Sub-distribution' [type=enum, input_value=<Mock name='mock().create...ype' id='2235780976320'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/enum
project_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock().create..._id' id='2235780976656'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type (switchboard_service.py:158)
Traceback (most recent call last):
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\switchboard_service.py", line 151, in create_switchboard
    return SwitchboardReadSchema.model_validate(switchboard)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\site-packages\pydantic\main.py", line 703, in model_validate
    return cls.__pydantic_validator__.validate_python(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        obj, strict=strict, from_attributes=from_attributes, context=context, by_alias=by_alias, by_name=by_name
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
pydantic_core._pydantic_core.ValidationError: 10 validation errors for SwitchboardReadSchema
created_at
  Input should be a valid datetime [type=datetime_type, input_value=<Mock name='mock().create..._at' id='2235780982032'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/datetime_type
updated_at
  Input should be a valid datetime [type=datetime_type, input_value=<Mock name='mock().create..._at' id='2235780982704'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/datetime_type
is_deleted
  Input should be a valid boolean [type=bool_type, input_value=<Mock name='mock().create...ted' id='2235780981360'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/bool_type
deleted_at
  Input should be a valid datetime [type=datetime_type, input_value=<Mock name='mock().create..._at' id='2235780981696'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/datetime_type
deleted_by_user_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock().create..._id' id='2235780982368'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
location
  Input should be a valid string [type=string_type, input_value=<Mock name='mock().create...ion' id='2235780980688'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
voltage_level_v
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock().create...l_v' id='2235780981024'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
number_of_phases
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock().create...ses' id='2235780977328'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
type
  Input should be 'Main' or 'Sub-distribution' [type=enum, input_value=<Mock name='mock().create...ype' id='2235780976320'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/enum
project_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock().create..._id' id='2235780976656'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.switchboard_service - DEBUG - SwitchboardService initialized (switchboard_service.py:110)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.switchboard_service - DEBUG - SwitchboardService initialized (switchboard_service.py:110)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.switchboard_service - DEBUG - Retrieving switchboard: 1 (switchboard_service.py:177)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.switchboard_service - DEBUG - SwitchboardService initialized (switchboard_service.py:110)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.switchboard_service - DEBUG - Retrieving switchboard: 999 (switchboard_service.py:177)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.switchboard_service - DEBUG - SwitchboardService initialized (switchboard_service.py:110)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.switchboard_service - INFO - Updating switchboard: 1 (switchboard_service.py:205)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.switchboard_service - INFO - Switchboard updated successfully: 1 (switchboard_service.py:232)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.switchboard_service - ERROR - Failed to update switchboard 1: 10 validation errors for SwitchboardReadSchema
created_at
  Input should be a valid datetime [type=datetime_type, input_value=<Mock name='mock().update..._at' id='2235777177248'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/datetime_type
updated_at
  Input should be a valid datetime [type=datetime_type, input_value=<Mock name='mock().update..._at' id='2235777178592'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/datetime_type
is_deleted
  Input should be a valid boolean [type=bool_type, input_value=<Mock name='mock().update...ted' id='2235777172880'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/bool_type
deleted_at
  Input should be a valid datetime [type=datetime_type, input_value=<Mock name='mock().update..._at' id='2235777185984'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/datetime_type
deleted_by_user_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock().update..._id' id='2235777177584'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
location
  Input should be a valid string [type=string_type, input_value=<Mock name='mock().update...ion' id='2235777185312'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
voltage_level_v
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock().update...l_v' id='2235777179936'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
number_of_phases
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock().update...ses' id='2235777181280'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
type
  Input should be 'Main' or 'Sub-distribution' [type=enum, input_value=<Mock name='mock().update...ype' id='2235777179264'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/enum
project_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock().update..._id' id='2235777171872'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type (switchboard_service.py:240)
Traceback (most recent call last):
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\switchboard_service.py", line 233, in update_switchboard
    return SwitchboardReadSchema.model_validate(updated_switchboard)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\site-packages\pydantic\main.py", line 703, in model_validate
    return cls.__pydantic_validator__.validate_python(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        obj, strict=strict, from_attributes=from_attributes, context=context, by_alias=by_alias, by_name=by_name
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
pydantic_core._pydantic_core.ValidationError: 10 validation errors for SwitchboardReadSchema
created_at
  Input should be a valid datetime [type=datetime_type, input_value=<Mock name='mock().update..._at' id='2235777177248'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/datetime_type
updated_at
  Input should be a valid datetime [type=datetime_type, input_value=<Mock name='mock().update..._at' id='2235777178592'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/datetime_type
is_deleted
  Input should be a valid boolean [type=bool_type, input_value=<Mock name='mock().update...ted' id='2235777172880'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/bool_type
deleted_at
  Input should be a valid datetime [type=datetime_type, input_value=<Mock name='mock().update..._at' id='2235777185984'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/datetime_type
deleted_by_user_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock().update..._id' id='2235777177584'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
location
  Input should be a valid string [type=string_type, input_value=<Mock name='mock().update...ion' id='2235777185312'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
voltage_level_v
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock().update...l_v' id='2235777179936'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
number_of_phases
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock().update...ses' id='2235777181280'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
type
  Input should be 'Main' or 'Sub-distribution' [type=enum, input_value=<Mock name='mock().update...ype' id='2235777179264'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/enum
project_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock().update..._id' id='2235777171872'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.switchboard_service - DEBUG - SwitchboardService initialized (switchboard_service.py:110)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.switchboard_service - DEBUG - SwitchboardService initialized (switchboard_service.py:110)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.switchboard_service - INFO - Deleting switchboard: 1 (switchboard_service.py:264)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.switchboard_service - INFO - Switchboard deleted successfully: 1 (switchboard_service.py:272)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.switchboard_service - DEBUG - SwitchboardService initialized (switchboard_service.py:110)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.switchboard_service - INFO - Deleting switchboard: 999 (switchboard_service.py:264)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.switchboard_service - DEBUG - SwitchboardService initialized (switchboard_service.py:110)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.switchboard_service - INFO - Creating feeder: Test Feeder (switchboard_service.py:330)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.switchboard_service - INFO - Feeder created successfully: 1 (switchboard_service.py:347)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.switchboard_service - ERROR - Failed to create feeder: 6 validation errors for FeederReadSchema
created_at
  Input should be a valid datetime [type=datetime_type, input_value=<Mock name='mock().create..._at' id='2235803109424'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/datetime_type
updated_at
  Input should be a valid datetime [type=datetime_type, input_value=<Mock name='mock().create..._at' id='2235803109760'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/datetime_type
is_deleted
  Input should be a valid boolean [type=bool_type, input_value=<Mock name='mock().create...ted' id='2235803110096'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/bool_type
deleted_at
  Input should be a valid datetime [type=datetime_type, input_value=<Mock name='mock().create..._at' id='2235803110432'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/datetime_type
deleted_by_user_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock().create..._id' id='2235803110768'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
switchboard_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock().create..._id' id='2235803111104'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type (switchboard_service.py:355)
Traceback (most recent call last):
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\switchboard_service.py", line 348, in create_feeder
    return FeederReadSchema.model_validate(feeder)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\site-packages\pydantic\main.py", line 703, in model_validate
    return cls.__pydantic_validator__.validate_python(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        obj, strict=strict, from_attributes=from_attributes, context=context, by_alias=by_alias, by_name=by_name
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
pydantic_core._pydantic_core.ValidationError: 6 validation errors for FeederReadSchema
created_at
  Input should be a valid datetime [type=datetime_type, input_value=<Mock name='mock().create..._at' id='2235803109424'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/datetime_type
updated_at
  Input should be a valid datetime [type=datetime_type, input_value=<Mock name='mock().create..._at' id='2235803109760'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/datetime_type
is_deleted
  Input should be a valid boolean [type=bool_type, input_value=<Mock name='mock().create...ted' id='2235803110096'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/bool_type
deleted_at
  Input should be a valid datetime [type=datetime_type, input_value=<Mock name='mock().create..._at' id='2235803110432'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/datetime_type
deleted_by_user_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock().create..._id' id='2235803110768'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
switchboard_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock().create..._id' id='2235803111104'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.switchboard_service - DEBUG - SwitchboardService initialized (switchboard_service.py:110)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.switchboard_service - INFO - Creating feeder: Test Feeder (switchboard_service.py:330)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.switchboard_service - DEBUG - SwitchboardService initialized (switchboard_service.py:110)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.switchboard_service - INFO - Adding component 15 to switchboard 1 (switchboard_service.py:424)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.switchboard_service - DEBUG - Component 15 validated for switchboard installation (switchboard_service.py:448)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.switchboard_service - INFO - Component added to switchboard successfully: 1 (switchboard_service.py:460)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.switchboard_service - ERROR - Failed to add component to switchboard: 4 validation errors for SwitchboardComponentReadSchema
quantity
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock().create...ity' id='2235803114464'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
position
  Input should be a valid string [type=string_type, input_value=<Mock name='mock().create...ion' id='2235803114800'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
switchboard_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock().create..._id' id='2235803115136'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
component_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock().create..._id' id='2235803115472'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type (switchboard_service.py:470)
Traceback (most recent call last):
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\switchboard_service.py", line 463, in add_switchboard_component
    return SwitchboardComponentReadSchema.model_validate(switchboard_component)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\site-packages\pydantic\main.py", line 703, in model_validate
    return cls.__pydantic_validator__.validate_python(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        obj, strict=strict, from_attributes=from_attributes, context=context, by_alias=by_alias, by_name=by_name
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
pydantic_core._pydantic_core.ValidationError: 4 validation errors for SwitchboardComponentReadSchema
quantity
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock().create...ity' id='2235803114464'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
position
  Input should be a valid string [type=string_type, input_value=<Mock name='mock().create...ion' id='2235803114800'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
switchboard_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock().create..._id' id='2235803115136'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
component_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock().create..._id' id='2235803115472'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.switchboard_service - DEBUG - SwitchboardService initialized (switchboard_service.py:110)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.switchboard_service - INFO - Adding component 15 to switchboard 999 (switchboard_service.py:424)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.switchboard_service - DEBUG - SwitchboardService initialized (switchboard_service.py:110)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.switchboard_service - INFO - Adding component 999 to switchboard 1 (switchboard_service.py:424)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.switchboard_service - DEBUG - SwitchboardService initialized (switchboard_service.py:110)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.switchboard_service - DEBUG - Calculating load summary for switchboard: 1 (switchboard_service.py:495)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.switchboard_service - DEBUG - SwitchboardService initialized (switchboard_service.py:110)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.switchboard_service - DEBUG - Performing capacity analysis for switchboard: 1 (switchboard_service.py:562)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.user_service - DEBUG - UserService initialized (user_service.py:106)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.user_service - INFO - Creating user: John Smith (user_service.py:126)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.user_service - INFO - User created successfully: 1 (user_service.py:149)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.user_service - ERROR - Failed to create user: 1 validation error for UserReadSchema
is_active
  Input should be a valid boolean [type=bool_type, input_value=<Mock name='mock().create...ive' id='2235803120512'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/bool_type (user_service.py:157)
Traceback (most recent call last):
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\user_service.py", line 150, in create_user
    return UserReadSchema.model_validate(user)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\site-packages\pydantic\main.py", line 703, in model_validate
    return cls.__pydantic_validator__.validate_python(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        obj, strict=strict, from_attributes=from_attributes, context=context, by_alias=by_alias, by_name=by_name
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
pydantic_core._pydantic_core.ValidationError: 1 validation error for UserReadSchema
is_active
  Input should be a valid boolean [type=bool_type, input_value=<Mock name='mock().create...ive' id='2235803120512'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/bool_type
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.user_service - DEBUG - UserService initialized (user_service.py:106)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.user_service - INFO - Creating user: John Smith (user_service.py:126)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.user_service - DEBUG - UserService initialized (user_service.py:106)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.user_service - DEBUG - Retrieving user: 1 (user_service.py:176)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.user_service - DEBUG - UserService initialized (user_service.py:106)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.user_service - DEBUG - Retrieving user: 999 (user_service.py:176)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.user_service - DEBUG - UserService initialized (user_service.py:106)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.user_service - DEBUG - Retrieving user: 1 (user_service.py:176)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.user_service - DEBUG - UserService initialized (user_service.py:106)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.user_service - INFO - Login attempt for: <EMAIL> (user_service.py:407)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.user_service - DEBUG - Authenticating user: <EMAIL> (user_service.py:337)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.user_service - ERROR - Authentication <NAME_EMAIL>: hash must be unicode or bytes, not unittest.mock.Mock (user_service.py:357)
Traceback (most recent call last):
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\user_service.py", line 346, in authenticate_user
    if not user.password_hash or not pwd_context.verify(
                                     ~~~~~~~~~~~~~~~~~~^
        login_data.password, user.password_hash
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ):
    ^
  File "C:\Users\<USER>\Python\Python313\Lib\site-packages\passlib\context.py", line 2343, in verify
    record = self._get_or_identify_record(hash, scheme, category)
  File "C:\Users\<USER>\Python\Python313\Lib\site-packages\passlib\context.py", line 2031, in _get_or_identify_record
    return self._identify_record(hash, category)
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\site-packages\passlib\context.py", line 1122, in identify_record
    raise ExpectedStringError(hash, "hash")
TypeError: hash must be unicode or bytes, not unittest.mock.Mock
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.user_service - DEBUG - UserService initialized (user_service.py:106)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.user_service - INFO - Login attempt for: <EMAIL> (user_service.py:407)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.user_service - DEBUG - Authenticating user: <EMAIL> (user_service.py:337)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.user_service - DEBUG - UserService initialized (user_service.py:106)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.user_service - INFO - Login attempt for: <EMAIL> (user_service.py:407)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.user_service - DEBUG - Authenticating user: <EMAIL> (user_service.py:337)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.user_service - ERROR - Authentication <NAME_EMAIL>: hash must be unicode or bytes, not unittest.mock.Mock (user_service.py:357)
Traceback (most recent call last):
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\user_service.py", line 346, in authenticate_user
    if not user.password_hash or not pwd_context.verify(
                                     ~~~~~~~~~~~~~~~~~~^
        login_data.password, user.password_hash
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ):
    ^
  File "C:\Users\<USER>\Python\Python313\Lib\site-packages\passlib\context.py", line 2343, in verify
    record = self._get_or_identify_record(hash, scheme, category)
  File "C:\Users\<USER>\Python\Python313\Lib\site-packages\passlib\context.py", line 2031, in _get_or_identify_record
    return self._identify_record(hash, category)
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\site-packages\passlib\context.py", line 1122, in identify_record
    raise ExpectedStringError(hash, "hash")
TypeError: hash must be unicode or bytes, not unittest.mock.Mock
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.user_service - DEBUG - UserService initialized (user_service.py:106)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.user_service - INFO - Login attempt for: <EMAIL> (user_service.py:407)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.user_service - DEBUG - Authenticating user: <EMAIL> (user_service.py:337)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.user_service - DEBUG - UserService initialized (user_service.py:106)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.user_service - INFO - Password change request for user: 1 (user_service.py:490)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.user_service - ERROR - Password change error for user 1: hash must be unicode or bytes, not unittest.mock.Mock (user_service.py:528)
Traceback (most recent call last):
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\user_service.py", line 502, in change_password
    if not user.password_hash or not pwd_context.verify(
                                     ~~~~~~~~~~~~~~~~~~^
        password_data.current_password, user.password_hash
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ):
    ^
  File "C:\Users\<USER>\Python\Python313\Lib\site-packages\passlib\context.py", line 2343, in verify
    record = self._get_or_identify_record(hash, scheme, category)
  File "C:\Users\<USER>\Python\Python313\Lib\site-packages\passlib\context.py", line 2031, in _get_or_identify_record
    return self._identify_record(hash, category)
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\site-packages\passlib\context.py", line 1122, in identify_record
    raise ExpectedStringError(hash, "hash")
TypeError: hash must be unicode or bytes, not unittest.mock.Mock
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.user_service - DEBUG - UserService initialized (user_service.py:106)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.user_service - INFO - Password change request for user: 1 (user_service.py:490)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.user_service - ERROR - Password change error for user 1: hash must be unicode or bytes, not unittest.mock.Mock (user_service.py:528)
Traceback (most recent call last):
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\user_service.py", line 502, in change_password
    if not user.password_hash or not pwd_context.verify(
                                     ~~~~~~~~~~~~~~~~~~^
        password_data.current_password, user.password_hash
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ):
    ^
  File "C:\Users\<USER>\Python\Python313\Lib\site-packages\passlib\context.py", line 2343, in verify
    record = self._get_or_identify_record(hash, scheme, category)
  File "C:\Users\<USER>\Python\Python313\Lib\site-packages\passlib\context.py", line 2031, in _get_or_identify_record
    return self._identify_record(hash, category)
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\site-packages\passlib\context.py", line 1122, in identify_record
    raise ExpectedStringError(hash, "hash")
TypeError: hash must be unicode or bytes, not unittest.mock.Mock
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.user_service - DEBUG - UserService initialized (user_service.py:106)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.user_service - INFO - Updating user: 1 (user_service.py:228)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.user_service - INFO - User updated successfully: 1 (user_service.py:252)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.user_service - ERROR - Failed to update user 1: 2 validation errors for UserReadSchema
email
  Input should be a valid string [type=string_type, input_value=<Mock name='mock().update...ail' id='2235780572432'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
is_active
  Input should be a valid boolean [type=bool_type, input_value=<Mock name='mock().update...ive' id='2235780572096'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/bool_type (user_service.py:260)
Traceback (most recent call last):
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\user_service.py", line 253, in update_user
    return UserReadSchema.model_validate(updated_user)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\site-packages\pydantic\main.py", line 703, in model_validate
    return cls.__pydantic_validator__.validate_python(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        obj, strict=strict, from_attributes=from_attributes, context=context, by_alias=by_alias, by_name=by_name
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
pydantic_core._pydantic_core.ValidationError: 2 validation errors for UserReadSchema
email
  Input should be a valid string [type=string_type, input_value=<Mock name='mock().update...ail' id='2235780572432'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
is_active
  Input should be a valid boolean [type=bool_type, input_value=<Mock name='mock().update...ive' id='2235780572096'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/bool_type
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.user_service - DEBUG - UserService initialized (user_service.py:106)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.user_service - INFO - Deactivating user: 1 (user_service.py:279)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.user_service - INFO - User deactivated successfully: 1 (user_service.py:285)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.user_service - DEBUG - UserService initialized (user_service.py:106)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.user_service - DEBUG - Retrieving preferences for user: 1 (user_service.py:553)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.user_service - DEBUG - UserService initialized (user_service.py:106)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.user_service - DEBUG - Retrieving preferences for user: 1 (user_service.py:553)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.user_service - DEBUG - UserService initialized (user_service.py:106)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.user_service - INFO - Creating/updating preferences for user: 1 (user_service.py:584)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.user_service - INFO - Preferences updated successfully for user: 1 (user_service.py:603)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.user_service - ERROR - Failed to update preferences for user 1: 14 validation errors for UserPreferenceReadSchema
id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock().create....id' id='2235803083712'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
created_at
  Input should be a valid datetime [type=datetime_type, input_value=<Mock name='mock().create..._at' id='2235803081360'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/datetime_type
updated_at
  Input should be a valid datetime [type=datetime_type, input_value=<Mock name='mock().create..._at' id='2235803083376'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/datetime_type
is_deleted
  Input should be a valid boolean [type=bool_type, input_value=<Mock name='mock().create...ted' id='2235803081024'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/bool_type
deleted_at
  Input should be a valid datetime [type=datetime_type, input_value=<Mock name='mock().create..._at' id='2235803083040'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/datetime_type
deleted_by_user_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock().create..._id' id='2235803080688'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
ui_theme
  Input should be a valid string [type=string_type, input_value=<Mock name='mock().create...eme' id='2235803082704'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
default_min_ambient_temp_c
  Input should be a valid number [type=float_type, input_value=<Mock name='mock().create...p_c' id='2235803078000'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/float_type
default_max_ambient_temp_c
  Input should be a valid number [type=float_type, input_value=<Mock name='mock().create...p_c' id='2235803079344'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/float_type
default_desired_maintenance_temp_c
  Input should be a valid number [type=float_type, input_value=<Mock name='mock().create...p_c' id='2235803075984'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/float_type
default_safety_margin_percent
  Input should be a valid number [type=float_type, input_value=<Mock name='mock().create...ent' id='2235803076320'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/float_type
preferred_cable_manufacturers_json
  Input should be a valid string [type=string_type, input_value=<Mock name='mock().create...son' id='2235803079680'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
preferred_control_device_manufacturers_json
  Input should be a valid string [type=string_type, input_value=<Mock name='mock().create...son' id='2235803078336'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
user_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock().create..._id' id='2235803078672'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type (user_service.py:611)
Traceback (most recent call last):
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\user_service.py", line 604, in create_or_update_user_preferences
    return UserPreferenceReadSchema.model_validate(preferences)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Python\Python313\Lib\site-packages\pydantic\main.py", line 703, in model_validate
    return cls.__pydantic_validator__.validate_python(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        obj, strict=strict, from_attributes=from_attributes, context=context, by_alias=by_alias, by_name=by_name
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
pydantic_core._pydantic_core.ValidationError: 14 validation errors for UserPreferenceReadSchema
id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock().create....id' id='2235803083712'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
created_at
  Input should be a valid datetime [type=datetime_type, input_value=<Mock name='mock().create..._at' id='2235803081360'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/datetime_type
updated_at
  Input should be a valid datetime [type=datetime_type, input_value=<Mock name='mock().create..._at' id='2235803083376'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/datetime_type
is_deleted
  Input should be a valid boolean [type=bool_type, input_value=<Mock name='mock().create...ted' id='2235803081024'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/bool_type
deleted_at
  Input should be a valid datetime [type=datetime_type, input_value=<Mock name='mock().create..._at' id='2235803083040'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/datetime_type
deleted_by_user_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock().create..._id' id='2235803080688'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
ui_theme
  Input should be a valid string [type=string_type, input_value=<Mock name='mock().create...eme' id='2235803082704'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
default_min_ambient_temp_c
  Input should be a valid number [type=float_type, input_value=<Mock name='mock().create...p_c' id='2235803078000'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/float_type
default_max_ambient_temp_c
  Input should be a valid number [type=float_type, input_value=<Mock name='mock().create...p_c' id='2235803079344'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/float_type
default_desired_maintenance_temp_c
  Input should be a valid number [type=float_type, input_value=<Mock name='mock().create...p_c' id='2235803075984'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/float_type
default_safety_margin_percent
  Input should be a valid number [type=float_type, input_value=<Mock name='mock().create...ent' id='2235803076320'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/float_type
preferred_cable_manufacturers_json
  Input should be a valid string [type=string_type, input_value=<Mock name='mock().create...son' id='2235803079680'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
preferred_control_device_manufacturers_json
  Input should be a valid string [type=string_type, input_value=<Mock name='mock().create...son' id='2235803078336'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
user_id
  Input should be a valid integer [type=int_type, input_value=<Mock name='mock().create..._id' id='2235803078672'>, input_type=Mock]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.user_service - DEBUG - UserService initialized (user_service.py:106)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.user_service - DEBUG - Searching users with term: john (user_service.py:688)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.user_service - DEBUG - UserService initialized (user_service.py:106)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.user_service - DEBUG - Counting active users (user_service.py:700)
2025-06-02 07:38:35 - ultimate_electrical_designer.core.services.user_service - DEBUG - UserService initialized (user_service.py:106)
2025-06-02 07:38:36 - ultimate_electrical_designer.core.services.user_service - DEBUG - UserService initialized (user_service.py:106)

# backend/core/repositories/component_repository.py
"""
Repository for Component and ComponentCategory data access operations.

This module provides data access methods for component-related entities,
extending the base repository with component-specific functionality.
"""

import logging
from typing import List, Optional, Dict, Any
from sqlalchemy import select, and_, or_, func
from sqlalchemy.exc import SQLAlchemyError

from core.models.components import Component, ComponentCategory
from core.errors.exceptions import ComponentNotFoundError, DatabaseError
from .base_repository import BaseRepository

logger = logging.getLogger(__name__)


class ComponentRepository(BaseRepository[Component]):
    """Repository for Component data access operations."""

    def __init__(self, db_session):
        """Initialize the Component repository."""
        super().__init__(db_session, Component)
        logger.debug("ComponentRepository initialized")

    def get_by_name_and_category(
        self, name: str, category_id: int
    ) -> Optional[Component]:
        """
        Get component by name and category ID.

        Args:
            name: Component name to search for
            category_id: Category ID to filter by

        Returns:
            Optional[Component]: The found component or None

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(
            f"Searching for component with name: {name} in category: {category_id}"
        )

        try:
            stmt = select(self.model).where(
                and_(
                    self.model.name == name,
                    self.model.category_id == category_id,
                    self.model.is_deleted == False,
                )
            )
            result = self.db_session.scalar(stmt)

            if result:
                logger.debug(f"Found component: '{result.name}' (ID: {result.id})")
            else:
                logger.debug(
                    f"Component not found with name: {name} in category: {category_id}"
                )

            return result

        except SQLAlchemyError as e:
            logger.error(
                f"Database error searching for component {name} in category {category_id}: {e}"
            )
            self._handle_db_exception(e, "component")
            raise  # This will never be reached due to _handle_db_exception raising

    def get_by_category(
        self, category_id: int, skip: int = 0, limit: int = 100
    ) -> List[Component]:
        """
        Get components by category ID.

        Args:
            category_id: Category ID to filter by
            skip: Number of records to skip (for pagination)
            limit: Maximum number of records to return

        Returns:
            List[Component]: List of components in the category

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Retrieving components for category: {category_id}")

        try:
            stmt = (
                select(self.model)
                .where(
                    and_(
                        self.model.category_id == category_id,
                        self.model.is_deleted == False,
                    )
                )
                .offset(skip)
                .limit(limit)
                .order_by(self.model.name)
            )
            result = self.db_session.scalars(stmt).all()

            logger.debug(f"Found {len(result)} components in category {category_id}")
            return result

        except SQLAlchemyError as e:
            logger.error(
                f"Database error retrieving components for category {category_id}: {e}"
            )
            self._handle_db_exception(e, "component")
            raise

    def search_components(
        self,
        search_term: str,
        category_id: Optional[int] = None,
        manufacturer: Optional[str] = None,
        skip: int = 0,
        limit: int = 100,
    ) -> List[Component]:
        """
        Search components by various criteria.

        Args:
            search_term: Term to search in name, model, and manufacturer
            category_id: Optional category ID to filter by
            manufacturer: Optional manufacturer to filter by
            skip: Number of records to skip (for pagination)
            limit: Maximum number of records to return

        Returns:
            List[Component]: List of matching components

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Searching components with term: {search_term}")

        try:
            # Build search conditions
            conditions = [self.model.is_deleted == False]

            # Text search in name, model, and manufacturer
            if search_term:
                search_pattern = f"%{search_term.lower()}%"
                text_conditions = or_(
                    func.lower(self.model.name).like(search_pattern),
                    func.lower(self.model.model).like(search_pattern),
                    func.lower(self.model.manufacturer).like(search_pattern),
                )
                conditions.append(text_conditions)

            # Category filter
            if category_id is not None:
                conditions.append(self.model.category_id == category_id)

            # Manufacturer filter
            if manufacturer:
                conditions.append(
                    func.lower(self.model.manufacturer).like(
                        f"%{manufacturer.lower()}%"
                    )
                )

            stmt = (
                select(self.model)
                .where(and_(*conditions))
                .offset(skip)
                .limit(limit)
                .order_by(self.model.name)
            )
            result = self.db_session.scalars(stmt).all()

            logger.debug(f"Found {len(result)} components matching search criteria")
            return result

        except SQLAlchemyError as e:
            logger.error(f"Database error searching components: {e}")
            self._handle_db_exception(e, "component")
            raise

    def get_active_components(self, skip: int = 0, limit: int = 100) -> List[Component]:
        """
        Get all active (non-deleted) components.

        Args:
            skip: Number of records to skip (for pagination)
            limit: Maximum number of records to return

        Returns:
            List[Component]: List of active components

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug("Retrieving active components")

        try:
            stmt = (
                select(self.model)
                .where(self.model.is_deleted == False)
                .offset(skip)
                .limit(limit)
                .order_by(self.model.name)
            )
            result = self.db_session.scalars(stmt).all()

            logger.debug(f"Found {len(result)} active components")
            return result

        except SQLAlchemyError as e:
            logger.error(f"Database error retrieving active components: {e}")
            self._handle_db_exception(e, "component")
            raise

    def count_by_category(self, category_id: int) -> int:
        """
        Count components in a specific category.

        Args:
            category_id: Category ID to count components for

        Returns:
            int: Number of components in the category

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Counting components in category: {category_id}")

        try:
            stmt = select(func.count(self.model.id)).where(
                and_(
                    self.model.category_id == category_id,
                    self.model.is_deleted == False,
                )
            )
            result = self.db_session.scalar(stmt) or 0

            logger.debug(f"Found {result} components in category {category_id}")
            return result

        except SQLAlchemyError as e:
            logger.error(
                f"Database error counting components in category {category_id}: {e}"
            )
            self._handle_db_exception(e, "component")
            raise


class ComponentCategoryRepository(BaseRepository[ComponentCategory]):
    """Repository for ComponentCategory data access operations."""

    def __init__(self, db_session):
        """Initialize the ComponentCategory repository."""
        super().__init__(db_session, ComponentCategory)
        logger.debug("ComponentCategoryRepository initialized")

    def get_by_name(self, name: str) -> Optional[ComponentCategory]:
        """
        Get component category by name.

        Args:
            name: Category name to search for

        Returns:
            Optional[ComponentCategory]: The found category or None

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Searching for component category with name: {name}")

        try:
            stmt = select(self.model).where(
                and_(self.model.name == name, self.model.is_deleted == False)
            )
            result = self.db_session.scalar(stmt)

            if result:
                logger.debug(f"Found category: '{result.name}' (ID: {result.id})")
            else:
                logger.debug(f"Category not found with name: {name}")

            return result

        except SQLAlchemyError as e:
            logger.error(f"Database error searching for category {name}: {e}")
            self._handle_db_exception(e, "component_category")
            raise

    def get_root_categories(
        self, skip: int = 0, limit: int = 100
    ) -> List[ComponentCategory]:
        """
        Get root categories (categories without parent).

        Args:
            skip: Number of records to skip (for pagination)
            limit: Maximum number of records to return

        Returns:
            List[ComponentCategory]: List of root categories

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug("Retrieving root component categories")

        try:
            stmt = (
                select(self.model)
                .where(
                    and_(
                        self.model.parent_category_id.is_(None),
                        self.model.is_deleted == False,
                    )
                )
                .offset(skip)
                .limit(limit)
                .order_by(self.model.name)
            )
            result = self.db_session.scalars(stmt).all()

            logger.debug(f"Found {len(result)} root categories")
            return result

        except SQLAlchemyError as e:
            logger.error(f"Database error retrieving root categories: {e}")
            self._handle_db_exception(e, "component_category")
            raise

    def get_subcategories(
        self, parent_id: int, skip: int = 0, limit: int = 100
    ) -> List[ComponentCategory]:
        """
        Get subcategories of a parent category.

        Args:
            parent_id: Parent category ID
            skip: Number of records to skip (for pagination)
            limit: Maximum number of records to return

        Returns:
            List[ComponentCategory]: List of subcategories

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Retrieving subcategories for parent: {parent_id}")

        try:
            stmt = (
                select(self.model)
                .where(
                    and_(
                        self.model.parent_category_id == parent_id,
                        self.model.is_deleted == False,
                    )
                )
                .offset(skip)
                .limit(limit)
                .order_by(self.model.name)
            )
            result = self.db_session.scalars(stmt).all()

            logger.debug(f"Found {len(result)} subcategories for parent {parent_id}")
            return result

        except SQLAlchemyError as e:
            logger.error(
                f"Database error retrieving subcategories for parent {parent_id}: {e}"
            )
            self._handle_db_exception(e, "component_category")
            raise

{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.8.0", "globals": "12c6d4a756ac2be3fd7aff32b06775f7", "files": {"z_bfd9059a16e3fa4e_main_router_py": {"hash": "a20a227bbaf277470a05474075e8044b", "index": {"url": "z_bfd9059a16e3fa4e_main_router_py.html", "file": "backend\\api\\main_router.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 16, "n_excluded": 0, "n_missing": 16, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f96405ab037cc57a___init___py": {"hash": "b39448d5e6036789656f6c378e522772", "index": {"url": "z_f96405ab037cc57a___init___py.html", "file": "backend\\core\\calculations\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f96405ab037cc57a_calculation_service_py": {"hash": "01ec583ab3ae8565f0b0e2508c493f15", "index": {"url": "z_f96405ab037cc57a_calculation_service_py.html", "file": "backend\\core\\calculations\\calculation_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 120, "n_excluded": 0, "n_missing": 60, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_da9c56813bb6ca47___init___py": {"hash": "3fca9809d9d173199c7a8e2fdebf8d8f", "index": {"url": "z_da9c56813bb6ca47___init___py.html", "file": "backend\\core\\calculations\\electrical_sizing\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 3, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_da9c56813bb6ca47_cable_sizing_py": {"hash": "8bcdc60830c634955f5adca6022020db", "index": {"url": "z_da9c56813bb6ca47_cable_sizing_py.html", "file": "backend\\core\\calculations\\electrical_sizing\\cable_sizing.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 82, "n_excluded": 0, "n_missing": 71, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_da9c56813bb6ca47_voltage_drop_py": {"hash": "0e09178fbbbcf06ef59f31b9b7245582", "index": {"url": "z_da9c56813bb6ca47_voltage_drop_py.html", "file": "backend\\core\\calculations\\electrical_sizing\\voltage_drop.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 82, "n_excluded": 0, "n_missing": 72, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_aee7a23c30801740___init___py": {"hash": "3f7d3467d7d928e19f323a8d0b3e7810", "index": {"url": "z_aee7a23c30801740___init___py.html", "file": "backend\\core\\calculations\\heat_loss\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 4, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_aee7a23c30801740_insulation_properties_py": {"hash": "89a661e6096e6205d0bfb5106704891e", "index": {"url": "z_aee7a23c30801740_insulation_properties_py.html", "file": "backend\\core\\calculations\\heat_loss\\insulation_properties.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 76, "n_excluded": 0, "n_missing": 76, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_aee7a23c30801740_pipe_heat_loss_py": {"hash": "e9bffe333ada3e6713ad1fa29f5cee91", "index": {"url": "z_aee7a23c30801740_pipe_heat_loss_py.html", "file": "backend\\core\\calculations\\heat_loss\\pipe_heat_loss.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 99, "n_excluded": 0, "n_missing": 99, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_aee7a23c30801740_vessel_heat_loss_py": {"hash": "34e269e6e2aeae2fa424827a3558cece", "index": {"url": "z_aee7a23c30801740_vessel_heat_loss_py.html", "file": "backend\\core\\calculations\\heat_loss\\vessel_heat_loss.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 64, "n_excluded": 0, "n_missing": 64, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ab7802d47b18b7f9___init___py": {"hash": "11d927e3a8451f95e769a2832caf2bd3", "index": {"url": "z_ab7802d47b18b7f9___init___py.html", "file": "backend\\core\\database\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ab7802d47b18b7f9_engine_py": {"hash": "278570a1a0876d76209446e8e1865519", "index": {"url": "z_ab7802d47b18b7f9_engine_py.html", "file": "backend\\core\\database\\engine.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 83, "n_excluded": 0, "n_missing": 63, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ab7802d47b18b7f9_initialization_py": {"hash": "2cad55600bcc245a8582090d3a0d9135", "index": {"url": "z_ab7802d47b18b7f9_initialization_py.html", "file": "backend\\core\\database\\initialization.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 106, "n_excluded": 0, "n_missing": 85, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ab7802d47b18b7f9_session_py": {"hash": "c74319761716b45e4e45a73e21beb8dd", "index": {"url": "z_ab7802d47b18b7f9_session_py.html", "file": "backend\\core\\database\\session.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 78, "n_excluded": 0, "n_missing": 52, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_71ce108dda7d12d3_exceptions_py": {"hash": "b2552608c466993063d5431baa680897", "index": {"url": "z_71ce108dda7d12d3_exceptions_py.html", "file": "backend\\core\\errors\\exceptions.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 40, "n_excluded": 0, "n_missing": 3, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7ac94d1f4eebeb24___init___py": {"hash": "c446911d396cab9b69e7bf5a045de5d2", "index": {"url": "z_7ac94d1f4eebeb24___init___py.html", "file": "backend\\core\\models\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 1, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7ac94d1f4eebeb24_activity_log_py": {"hash": "8bfcda4cb2d2062d0bdb213acb72b850", "index": {"url": "z_7ac94d1f4eebeb24_activity_log_py.html", "file": "backend\\core\\models\\activity_log.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 15, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7ac94d1f4eebeb24_base_py": {"hash": "278a6b06dcde6dcf92bb7ab382d55b0c", "index": {"url": "z_7ac94d1f4eebeb24_base_py.html", "file": "backend\\core\\models\\base.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 57, "n_excluded": 0, "n_missing": 4, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7ac94d1f4eebeb24_components_py": {"hash": "436af6da30d4b33cfd65633769d0c07d", "index": {"url": "z_7ac94d1f4eebeb24_components_py.html", "file": "backend\\core\\models\\components.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 24, "n_excluded": 0, "n_missing": 2, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7ac94d1f4eebeb24_documents_py": {"hash": "bcdd5b0665a9e6d3b668f74a9901522c", "index": {"url": "z_7ac94d1f4eebeb24_documents_py.html", "file": "backend\\core\\models\\documents.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 42, "n_excluded": 0, "n_missing": 3, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7ac94d1f4eebeb24_electrical_py": {"hash": "4c838313fe6a6871f4cd50f1b1afe6b5", "index": {"url": "z_7ac94d1f4eebeb24_electrical_py.html", "file": "backend\\core\\models\\electrical.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 139, "n_excluded": 0, "n_missing": 5, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7ac94d1f4eebeb24_enums_py": {"hash": "9d201884a84dcf9d4ba3e22ce836bac5", "index": {"url": "z_7ac94d1f4eebeb24_enums_py.html", "file": "backend\\core\\models\\enums.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 75, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7ac94d1f4eebeb24_heat_tracing_py": {"hash": "0adfbc91ed05b9db7cc78eba9176b67d", "index": {"url": "z_7ac94d1f4eebeb24_heat_tracing_py.html", "file": "backend\\core\\models\\heat_tracing.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 134, "n_excluded": 0, "n_missing": 4, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7ac94d1f4eebeb24_project_py": {"hash": "697d7df8f7319283e6363cd7fc0ed491", "index": {"url": "z_7ac94d1f4eebeb24_project_py.html", "file": "backend\\core\\models\\project.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 74, "n_excluded": 0, "n_missing": 15, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7ac94d1f4eebeb24_switchboard_py": {"hash": "87d4b8d3d66df430b63e5fac426de53a", "index": {"url": "z_7ac94d1f4eebeb24_switchboard_py.html", "file": "backend\\core\\models\\switchboard.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 53, "n_excluded": 0, "n_missing": 4, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7ac94d1f4eebeb24_users_py": {"hash": "e404d13ae817ae383bc4a3c5e81b964c", "index": {"url": "z_7ac94d1f4eebeb24_users_py.html", "file": "backend\\core\\models\\users.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 30, "n_excluded": 0, "n_missing": 2, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_3d2460180cf335ae___init___py": {"hash": "1d46360c553c16a6218ec3c6cdc7dc7d", "index": {"url": "z_3d2460180cf335ae___init___py.html", "file": "backend\\core\\standards\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b97b1182853e7f12___init___py": {"hash": "77671b05c8cd4132cc17d2e787a6d551", "index": {"url": "z_b97b1182853e7f12___init___py.html", "file": "backend\\core\\standards\\iec_60079_30_1\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 3, "n_excluded": 0, "n_missing": 3, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b97b1182853e7f12_hazardous_area_compliance_py": {"hash": "48d226bb86623429f020a9dd7179c903", "index": {"url": "z_b97b1182853e7f12_hazardous_area_compliance_py.html", "file": "backend\\core\\standards\\iec_60079_30_1\\hazardous_area_compliance.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 69, "n_excluded": 0, "n_missing": 69, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b97b1182853e7f12_temperature_class_limits_py": {"hash": "28bf92f4956b82dec638397f17c7d165", "index": {"url": "z_b97b1182853e7f12_temperature_class_limits_py.html", "file": "backend\\core\\standards\\iec_60079_30_1\\temperature_class_limits.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 40, "n_excluded": 0, "n_missing": 40, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_3d2460180cf335ae_standards_manager_py": {"hash": "ac2d9e831b91deaa1666d1369e99575a", "index": {"url": "z_3d2460180cf335ae_standards_manager_py.html", "file": "backend\\core\\standards\\standards_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 189, "n_excluded": 0, "n_missing": 146, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e6723df25685444a___init___py": {"hash": "691c4b3aec0b89632b86259ad27cc2dc", "index": {"url": "z_e6723df25685444a___init___py.html", "file": "backend\\core\\standards\\tr_50410\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 2, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e6723df25685444a_heat_loss_factors_py": {"hash": "fb0c635d4916de6311fcb4d33b89ac46", "index": {"url": "z_e6723df25685444a_heat_loss_factors_py.html", "file": "backend\\core\\standards\\tr_50410\\heat_loss_factors.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 24, "n_excluded": 0, "n_missing": 24, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_599edd1a5c19dad3_project_routes_py": {"hash": "b11116a682e4006b5995a70c044a6ded", "index": {"url": "z_599edd1a5c19dad3_project_routes_py.html", "file": "backend\\api\\v1\\project_routes.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 68, "n_excluded": 0, "n_missing": 46, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_599edd1a5c19dad3_switchboard_routes_py": {"hash": "992c95d8ac460501532f4f56d2469de5", "index": {"url": "z_599edd1a5c19dad3_switchboard_routes_py.html", "file": "backend\\api\\v1\\switchboard_routes.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 157, "n_excluded": 0, "n_missing": 52, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_599edd1a5c19dad3_user_routes_py": {"hash": "faa88cbf4f7a3192e8cab27b6eb2a20a", "index": {"url": "z_599edd1a5c19dad3_user_routes_py.html", "file": "backend\\api\\v1\\user_routes.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 169, "n_excluded": 0, "n_missing": 49, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_71ce108dda7d12d3_error_factory_py": {"hash": "330f02ecb8714010ec5ecc09364cd90f", "index": {"url": "z_71ce108dda7d12d3_error_factory_py.html", "file": "backend\\core\\errors\\error_factory.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 12, "n_excluded": 0, "n_missing": 6, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_71ce108dda7d12d3_error_registry_py": {"hash": "060fb65393c6e8d12eeefdb7eb5678ae", "index": {"url": "z_71ce108dda7d12d3_error_registry_py.html", "file": "backend\\core\\errors\\error_registry.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 3, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_71ce108dda7d12d3_error_templates_py": {"hash": "84c07e9052121658b293c948bc326038", "index": {"url": "z_71ce108dda7d12d3_error_templates_py.html", "file": "backend\\core\\errors\\error_templates.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 1, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6805001cdfee4172_activity_log_repository_py": {"hash": "42d9e1b876b351f2ceceda149273a4d3", "index": {"url": "z_6805001cdfee4172_activity_log_repository_py.html", "file": "backend\\core\\repositories\\activity_log_repository.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 228, "n_excluded": 0, "n_missing": 66, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6805001cdfee4172_base_repository_py": {"hash": "28395012e3cd1f4974cd374d3d111f0e", "index": {"url": "z_6805001cdfee4172_base_repository_py.html", "file": "backend\\core\\repositories\\base_repository.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 67, "n_excluded": 0, "n_missing": 35, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6805001cdfee4172_component_repository_py": {"hash": "cbc7de1f8baca9e0565a186db28313ca", "index": {"url": "z_6805001cdfee4172_component_repository_py.html", "file": "backend\\core\\repositories\\component_repository.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 117, "n_excluded": 0, "n_missing": 97, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6805001cdfee4172_document_repository_py": {"hash": "21ea3647a9bfbb21d1cacd64b9447c8f", "index": {"url": "z_6805001cdfee4172_document_repository_py.html", "file": "backend\\core\\repositories\\document_repository.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 235, "n_excluded": 0, "n_missing": 174, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6805001cdfee4172_electrical_repository_py": {"hash": "af9c3471ffd5f90733ee56805ffd2ceb", "index": {"url": "z_6805001cdfee4172_electrical_repository_py.html", "file": "backend\\core\\repositories\\electrical_repository.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 374, "n_excluded": 0, "n_missing": 222, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6805001cdfee4172_heat_tracing_repository_py": {"hash": "f18e98c7238cebdcbbcfd4ec3d8ba7b0", "index": {"url": "z_6805001cdfee4172_heat_tracing_repository_py.html", "file": "backend\\core\\repositories\\heat_tracing_repository.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 322, "n_excluded": 0, "n_missing": 136, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6805001cdfee4172_project_repository_py": {"hash": "1f60b50a46d0bc42e3e447e311b3f195", "index": {"url": "z_6805001cdfee4172_project_repository_py.html", "file": "backend\\core\\repositories\\project_repository.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 120, "n_excluded": 0, "n_missing": 30, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6805001cdfee4172_switchboard_repository_py": {"hash": "ba9b18eef19040cb398b8a9bc771f2b7", "index": {"url": "z_6805001cdfee4172_switchboard_repository_py.html", "file": "backend\\core\\repositories\\switchboard_repository.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 185, "n_excluded": 0, "n_missing": 155, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6805001cdfee4172_user_repository_py": {"hash": "cca3ee819b0372b998107503c55c2c18", "index": {"url": "z_6805001cdfee4172_user_repository_py.html", "file": "backend\\core\\repositories\\user_repository.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 160, "n_excluded": 0, "n_missing": 135, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e64413ff4bf96b58_activity_log_schemas_py": {"hash": "a996d66534f143e7d702e704c00005de", "index": {"url": "z_e64413ff4bf96b58_activity_log_schemas_py.html", "file": "backend\\core\\schemas\\activity_log_schemas.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 183, "n_excluded": 0, "n_missing": 2, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e64413ff4bf96b58_base_py": {"hash": "72feb756a66b1df66617dbc0e59f5e7c", "index": {"url": "z_e64413ff4bf96b58_base_py.html", "file": "backend\\core\\schemas\\base.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 42, "n_excluded": 0, "n_missing": 3, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e64413ff4bf96b58_component_schemas_py": {"hash": "a9b98e851454a28e749c11ca25f9c68f", "index": {"url": "z_e64413ff4bf96b58_component_schemas_py.html", "file": "backend\\core\\schemas\\component_schemas.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 82, "n_excluded": 0, "n_missing": 2, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e64413ff4bf96b58_document_schemas_py": {"hash": "c8dbd058e50780989783a6bcc3a90183", "index": {"url": "z_e64413ff4bf96b58_document_schemas_py.html", "file": "backend\\core\\schemas\\document_schemas.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 187, "n_excluded": 0, "n_missing": 9, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e64413ff4bf96b58_electrical_schemas_py": {"hash": "03277ed950a00381a12dede48549d714", "index": {"url": "z_e64413ff4bf96b58_electrical_schemas_py.html", "file": "backend\\core\\schemas\\electrical_schemas.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 409, "n_excluded": 0, "n_missing": 9, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e64413ff4bf96b58_error_py": {"hash": "780b167af7cabb87077a6b52c7948045", "index": {"url": "z_e64413ff4bf96b58_error_py.html", "file": "backend\\core\\schemas\\error.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 18, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e64413ff4bf96b58_heat_tracing_schemas_py": {"hash": "50fb88b2faa3b3b584760356e38cbb96", "index": {"url": "z_e64413ff4bf96b58_heat_tracing_schemas_py.html", "file": "backend\\core\\schemas\\heat_tracing_schemas.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 330, "n_excluded": 0, "n_missing": 5, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e64413ff4bf96b58_project_schemas_py": {"hash": "dcd8ba267e1c888dd62126a32f3e9adc", "index": {"url": "z_e64413ff4bf96b58_project_schemas_py.html", "file": "backend\\core\\schemas\\project_schemas.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 103, "n_excluded": 0, "n_missing": 2, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e64413ff4bf96b58_switchboard_schemas_py": {"hash": "250615b7afebab3789fc0d9b4cc7b883", "index": {"url": "z_e64413ff4bf96b58_switchboard_schemas_py.html", "file": "backend\\core\\schemas\\switchboard_schemas.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 156, "n_excluded": 0, "n_missing": 3, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e64413ff4bf96b58_user_schemas_py": {"hash": "3ea85abbdbc9d534ab83b0e9d6310f86", "index": {"url": "z_e64413ff4bf96b58_user_schemas_py.html", "file": "backend\\core\\schemas\\user_schemas.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 132, "n_excluded": 0, "n_missing": 5, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ff90eff64d9c93aa_activity_log_service_py": {"hash": "86735667b2551c1057c3bfead30f9dd6", "index": {"url": "z_ff90eff64d9c93aa_activity_log_service_py.html", "file": "backend\\core\\services\\activity_log_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 228, "n_excluded": 0, "n_missing": 60, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ff90eff64d9c93aa_component_service_py": {"hash": "867dedd141881ed92e32e125e1142da9", "index": {"url": "z_ff90eff64d9c93aa_component_service_py.html", "file": "backend\\core\\services\\component_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 217, "n_excluded": 0, "n_missing": 96, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ff90eff64d9c93aa_document_service_py": {"hash": "d1852ccfb00deea4c315e5127bf854ff", "index": {"url": "z_ff90eff64d9c93aa_document_service_py.html", "file": "backend\\core\\services\\document_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 308, "n_excluded": 0, "n_missing": 253, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ff90eff64d9c93aa_electrical_service_py": {"hash": "b20feb4cd720d0a6da5254c03c2a120f", "index": {"url": "z_ff90eff64d9c93aa_electrical_service_py.html", "file": "backend\\core\\services\\electrical_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 193, "n_excluded": 0, "n_missing": 82, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ff90eff64d9c93aa_heat_tracing_service_py": {"hash": "e49e86fba89720f8e3600d8d67e2513c", "index": {"url": "z_ff90eff64d9c93aa_heat_tracing_service_py.html", "file": "backend\\core\\services\\heat_tracing_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 326, "n_excluded": 0, "n_missing": 138, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ff90eff64d9c93aa_project_service_py": {"hash": "fef322fd10044614b42e2378dd57e9c1", "index": {"url": "z_ff90eff64d9c93aa_project_service_py.html", "file": "backend\\core\\services\\project_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 158, "n_excluded": 0, "n_missing": 56, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ff90eff64d9c93aa_switchboard_service_py": {"hash": "f0dda63e19c0f23a7c28d3cf3a84df86", "index": {"url": "z_ff90eff64d9c93aa_switchboard_service_py.html", "file": "backend\\core\\services\\switchboard_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 186, "n_excluded": 0, "n_missing": 43, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ff90eff64d9c93aa_user_service_py": {"hash": "831c948d1c7fa4504a7753faeb7c3d4e", "index": {"url": "z_ff90eff64d9c93aa_user_service_py.html", "file": "backend\\core\\services\\user_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 222, "n_excluded": 0, "n_missing": 99, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_599edd1a5c19dad3_activity_log_routes_py": {"hash": "3c58b7f6bd3b253cf48ccbd6764ef689", "index": {"url": "z_599edd1a5c19dad3_activity_log_routes_py.html", "file": "backend\\api\\v1\\activity_log_routes.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 165, "n_excluded": 0, "n_missing": 46, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_599edd1a5c19dad3_document_routes_py": {"hash": "d6421d24a35642cb8bf5c3bc68063f65", "index": {"url": "z_599edd1a5c19dad3_document_routes_py.html", "file": "backend\\api\\v1\\document_routes.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 173, "n_excluded": 0, "n_missing": 124, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_599edd1a5c19dad3_heat_tracing_routes_py": {"hash": "c08fb803797a065a40234e1f5d9f2054", "index": {"url": "z_599edd1a5c19dad3_heat_tracing_routes_py.html", "file": "backend\\api\\v1\\heat_tracing_routes.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 209, "n_excluded": 0, "n_missing": 170, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}
# backend/core/calculations/electrical_sizing/cable_sizing.py
"""
Cable Sizing Calculations.

This module implements cable selection and sizing calculations
for heat tracing applications.
"""

import logging
from typing import Dict, Any, Optional

from core.errors.exceptions import CalculationError, NotFoundError

logger = logging.getLogger(__name__)

# Cable database - simplified for demonstration
CABLE_DATABASE = {
    "self_regulating_10w": {
        "type": "self_regulating_10w",
        "power_per_meter": 10.0,  # W/m at 10°C
        "max_temperature": 65,  # °C
        "resistance_per_meter": 0.5,  # Ohm/m
        "max_length": 200,  # m
        "applications": ["general", "low_temperature"],
        "cost_factor": 1.0,
    },
    "self_regulating_20w": {
        "type": "self_regulating_20w",
        "power_per_meter": 20.0,
        "max_temperature": 85,
        "resistance_per_meter": 0.3,
        "max_length": 150,
        "applications": ["general", "medium_temperature"],
        "cost_factor": 1.3,
    },
    "constant_wattage_15w": {
        "type": "constant_wattage_15w",
        "power_per_meter": 15.0,
        "max_temperature": 150,
        "resistance_per_meter": 0.4,
        "max_length": 300,
        "applications": ["high_temperature", "precise_control"],
        "cost_factor": 1.8,
    },
    "mineral_insulated_25w": {
        "type": "mineral_insulated_25w",
        "power_per_meter": 25.0,
        "max_temperature": 250,
        "resistance_per_meter": 0.2,
        "max_length": 100,
        "applications": ["high_temperature", "hazardous_area"],
        "cost_factor": 3.0,
    },
}


def select_optimal_cable(
    required_power: float,
    cable_length: float,
    ambient_temp: float,
    installation_method: str,
    preferred_type: Optional[str] = None,
) -> Optional[Dict[str, Any]]:
    """
    Select optimal cable for given requirements.

    Args:
        required_power: Required heating power (W)
        cable_length: Cable length (m)
        ambient_temp: Ambient temperature (°C)
        installation_method: Installation method
        preferred_type: Preferred cable type (optional)

    Returns:
        Dict with selected cable properties or None if no suitable cable

    Raises:
        CalculationError: If calculation fails
    """
    logger.debug(f"Selecting cable for {required_power}W over {cable_length}m")

    try:
        required_power_per_meter = required_power / cable_length

        # If preferred type specified, check if it's suitable
        if preferred_type and preferred_type in CABLE_DATABASE:
            cable = CABLE_DATABASE[preferred_type]
            if _is_cable_suitable(
                cable, required_power_per_meter, cable_length, ambient_temp
            ):
                logger.debug(f"Preferred cable {preferred_type} is suitable")
                return cable.copy()

        # Find suitable cables
        suitable_cables = []

        for cable_type, cable_props in CABLE_DATABASE.items():
            if _is_cable_suitable(
                cable_props, required_power_per_meter, cable_length, ambient_temp
            ):
                cable_info = cable_props.copy()
                cable_info["suitability_score"] = _calculate_suitability_score(
                    cable_props, required_power_per_meter, cable_length
                )
                suitable_cables.append(cable_info)

        if not suitable_cables:
            logger.warning("No suitable cable found for requirements")
            return None

        # Sort by suitability score (higher is better)
        suitable_cables.sort(key=lambda x: x["suitability_score"], reverse=True)

        selected_cable = suitable_cables[0]
        logger.debug(f"Selected cable: {selected_cable['type']}")

        return selected_cable

    except Exception as e:
        logger.error(f"Cable selection failed: {e}", exc_info=True)
        raise CalculationError(f"Cable selection failed: {str(e)}")


def calculate_cable_parameters(
    cable_type: str, required_power: float, cable_length: float, supply_voltage: float
) -> Dict[str, Any]:
    """
    Calculate cable parameters for selected cable.

    Args:
        cable_type: Selected cable type
        required_power: Required power (W)
        cable_length: Cable length (m)
        supply_voltage: Supply voltage (V)

    Returns:
        Dict with calculated cable parameters

    Raises:
        NotFoundError: If cable type not found
        CalculationError: If calculation fails
    """
    logger.debug(f"Calculating parameters for {cable_type}")

    try:
        if cable_type not in CABLE_DATABASE:
            raise NotFoundError(f"Cable type '{cable_type}' not found in database")

        cable = CABLE_DATABASE[cable_type]

        # Calculate actual power per meter needed
        required_power_per_meter = required_power / cable_length

        # Calculate total cable length (may need multiple parallel runs)
        if required_power_per_meter <= cable["power_per_meter"]:
            # Single run sufficient
            total_length = cable_length
            parallel_runs = 1
        else:
            # Multiple parallel runs needed
            parallel_runs = int(required_power_per_meter / cable["power_per_meter"]) + 1
            total_length = cable_length * parallel_runs

        # Calculate current draw
        total_power = cable["power_per_meter"] * total_length
        current_draw = total_power / supply_voltage

        # Calculate power density (simplified)
        power_density = cable["power_per_meter"] * 10  # W/m² (rough approximation)

        result = {
            "cable_type": cable_type,
            "power_per_meter": cable["power_per_meter"],
            "total_length": total_length,
            "parallel_runs": parallel_runs,
            "current_draw": current_draw,
            "total_power": total_power,
            "power_density": power_density,
            "cable_properties": cable,
        }

        logger.debug(
            f"Cable parameters calculated: {current_draw:.1f}A, {total_length}m"
        )
        return result

    except (NotFoundError, CalculationError):
        raise
    except Exception as e:
        logger.error(f"Cable parameter calculation failed: {e}", exc_info=True)
        raise CalculationError(f"Cable parameter calculation failed: {str(e)}")


def _is_cable_suitable(
    cable: Dict[str, Any],
    required_power_per_meter: float,
    cable_length: float,
    ambient_temp: float,
) -> bool:
    """Check if cable is suitable for requirements."""

    # Check power capability (allow for multiple parallel runs)
    max_parallel_runs = 5  # Practical limit
    max_achievable_power = cable["power_per_meter"] * max_parallel_runs

    if required_power_per_meter > max_achievable_power:
        return False

    # Check length limits
    if cable_length > cable["max_length"]:
        return False

    # Check temperature limits
    if ambient_temp > cable["max_temperature"]:
        return False

    return True


def _calculate_suitability_score(
    cable: Dict[str, Any], required_power_per_meter: float, cable_length: float
) -> float:
    """Calculate suitability score for cable selection."""

    score = 100.0  # Base score

    # Power matching score (prefer cables close to required power)
    power_ratio = required_power_per_meter / cable["power_per_meter"]
    if power_ratio <= 1.0:
        # Prefer cables that closely match power requirement
        score += (1.0 - abs(1.0 - power_ratio)) * 50
    else:
        # Penalize cables requiring multiple runs
        score -= (power_ratio - 1.0) * 30

    # Length utilization score
    length_ratio = cable_length / cable["max_length"]
    if length_ratio <= 0.8:
        score += 20  # Good length utilization
    elif length_ratio > 0.95:
        score -= 30  # Close to length limit

    # Cost factor (lower cost is better)
    score -= cable["cost_factor"] * 10

    return max(0.0, score)


def get_available_cables() -> Dict[str, Dict[str, Any]]:
    """Get all available cable types."""
    return CABLE_DATABASE.copy()


def get_cable_properties(cable_type: str) -> Dict[str, Any]:
    """
    Get properties for specific cable type.

    Args:
        cable_type: Cable type identifier

    Returns:
        Dict with cable properties

    Raises:
        NotFoundError: If cable type not found
    """
    if cable_type not in CABLE_DATABASE:
        raise NotFoundError(f"Cable type '{cable_type}' not found")

    return CABLE_DATABASE[cable_type].copy()

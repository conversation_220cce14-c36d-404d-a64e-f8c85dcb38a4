# backend/tests/test_user_schemas.py
"""
Tests for User Schemas

This module tests the validation and serialization of user-related schemas.
"""

import pytest

# Mark all tests in this file
pytestmark = [pytest.mark.unit, pytest.mark.schema, pytest.mark.user]
import sys
import os
from pydantic import ValidationError

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.schemas.user_schemas import (
    UserCreateSchema,
    UserUpdateSchema,
    UserPreferenceCreateSchema,
    UserPreferenceUpdateSchema,
    LoginRequestSchema,
    PasswordChangeRequestSchema,
    PasswordResetConfirmSchema,
)


class TestUserSchemas:
    """Test user schema validation."""

    def test_user_create_schema_valid(self):
        """Test valid user creation data."""
        data = {
            "name": "<PERSON>",
            "email": "<EMAIL>",
            "password": "SecurePass123",
            "is_active": True,
        }

        schema = UserCreateSchema(**data)
        assert schema.name == "<PERSON>"
        assert schema.email == "<EMAIL>"
        assert schema.is_active is True

    def test_user_create_schema_password_validation(self):
        """Test user creation with weak password."""
        data = {
            "name": "John Smith",
            "email": "<EMAIL>",
            "password": "weak",  # Too short, no uppercase, no digit
            "is_active": True,
        }

        with pytest.raises(ValidationError) as exc_info:
            UserCreateSchema(**data)

        error_message = str(exc_info.value)
        assert "String should have at least 8 characters" in error_message

    def test_user_create_schema_password_no_uppercase(self):
        """Test user creation with password missing uppercase."""
        data = {
            "name": "John Smith",
            "email": "<EMAIL>",
            "password": "lowercase123",  # No uppercase
            "is_active": True,
        }

        with pytest.raises(ValidationError) as exc_info:
            UserCreateSchema(**data)

        error_message = str(exc_info.value)
        assert "uppercase letter" in error_message

    def test_user_create_schema_password_no_digit(self):
        """Test user creation with password missing digit."""
        data = {
            "name": "John Smith",
            "email": "<EMAIL>",
            "password": "NoDigitHere",  # No digit
            "is_active": True,
        }

        with pytest.raises(ValidationError) as exc_info:
            UserCreateSchema(**data)

        error_message = str(exc_info.value)
        assert "digit" in error_message

    def test_user_create_schema_empty_name(self):
        """Test user creation with empty name."""
        data = {
            "name": "   ",  # Empty after strip
            "email": "<EMAIL>",
            "password": "SecurePass123",
            "is_active": True,
        }

        with pytest.raises(ValidationError) as exc_info:
            UserCreateSchema(**data)

        assert "User name cannot be empty" in str(exc_info.value)

    def test_user_create_schema_invalid_email(self):
        """Test user creation with invalid email."""
        data = {
            "name": "John Smith",
            "email": "invalid-email",  # Invalid email format
            "password": "SecurePass123",
            "is_active": True,
        }

        with pytest.raises(ValidationError) as exc_info:
            UserCreateSchema(**data)

        assert "email" in str(exc_info.value).lower()

    def test_user_update_schema_partial(self):
        """Test user update with partial data."""
        data = {
            "name": "Updated Name",
            "is_active": False,
        }

        schema = UserUpdateSchema(**data)
        assert schema.name == "Updated Name"
        assert schema.is_active is False
        assert schema.email is None  # Not provided

    def test_login_request_schema_valid(self):
        """Test valid login request data."""
        data = {
            "email": "<EMAIL>",
            "password": "SecurePass123",
        }

        schema = LoginRequestSchema(**data)
        assert schema.email == "<EMAIL>"
        assert schema.password == "SecurePass123"

    def test_login_request_schema_invalid_email(self):
        """Test login request with invalid email."""
        data = {
            "email": "invalid-email",
            "password": "SecurePass123",
        }

        with pytest.raises(ValidationError) as exc_info:
            LoginRequestSchema(**data)

        assert "email" in str(exc_info.value).lower()

    def test_password_change_request_schema_valid(self):
        """Test valid password change request."""
        data = {
            "current_password": "OldPass123",
            "new_password": "NewSecurePass456",
        }

        schema = PasswordChangeRequestSchema(**data)
        assert schema.current_password == "OldPass123"
        assert schema.new_password == "NewSecurePass456"

    def test_password_change_request_schema_weak_new_password(self):
        """Test password change with weak new password."""
        data = {
            "current_password": "OldPass123",
            "new_password": "weak",  # Too weak
        }

        with pytest.raises(ValidationError) as exc_info:
            PasswordChangeRequestSchema(**data)

        error_message = str(exc_info.value)
        assert "String should have at least 8 characters" in error_message

    def test_password_reset_confirm_schema_valid(self):
        """Test valid password reset confirmation."""
        data = {
            "token": "reset_token_here",
            "new_password": "NewSecurePass456",
        }

        schema = PasswordResetConfirmSchema(**data)
        assert schema.token == "reset_token_here"
        assert schema.new_password == "NewSecurePass456"


class TestUserPreferenceSchemas:
    """Test user preference schema validation."""

    def test_user_preference_create_schema_valid(self):
        """Test valid user preference creation data."""
        data = {
            "user_id": 1,
            "ui_theme": "dark",
            "default_min_ambient_temp_c": -10.0,
            "default_max_ambient_temp_c": 40.0,
            "default_desired_maintenance_temp_c": 65.0,
            "default_safety_margin_percent": 20.0,
        }

        schema = UserPreferenceCreateSchema(**data)
        assert schema.user_id == 1
        assert schema.ui_theme == "dark"
        assert schema.default_min_ambient_temp_c == -10.0

    def test_user_preference_create_schema_invalid_theme(self):
        """Test user preference creation with invalid theme."""
        data = {
            "user_id": 1,
            "ui_theme": "invalid_theme",  # Not in allowed themes
        }

        with pytest.raises(ValidationError) as exc_info:
            UserPreferenceCreateSchema(**data)

        error_message = str(exc_info.value)
        assert "UI theme must be one of" in error_message

    def test_user_preference_create_schema_invalid_temperature_range(self):
        """Test user preference creation with invalid temperature range."""
        data = {
            "user_id": 1,
            "default_min_ambient_temp_c": 40.0,  # Higher than max
            "default_max_ambient_temp_c": 30.0,  # Lower than min
        }

        with pytest.raises(ValidationError) as exc_info:
            UserPreferenceCreateSchema(**data)

        error_message = str(exc_info.value)
        assert (
            "Maximum ambient temperature must be higher than minimum" in error_message
        )

    def test_user_preference_create_schema_temperature_boundaries(self):
        """Test user preference creation with temperature boundaries."""
        data = {
            "user_id": 1,
            "default_min_ambient_temp_c": -50.0,  # Minimum allowed
            "default_max_ambient_temp_c": 80.0,  # Maximum allowed
            "default_desired_maintenance_temp_c": 200.0,  # Maximum allowed
        }

        schema = UserPreferenceCreateSchema(**data)
        assert schema.default_min_ambient_temp_c == -50.0
        assert schema.default_max_ambient_temp_c == 80.0
        assert schema.default_desired_maintenance_temp_c == 200.0

    def test_user_preference_create_schema_invalid_safety_margin(self):
        """Test user preference creation with invalid safety margin."""
        data = {
            "user_id": 1,
            "default_safety_margin_percent": 150.0,  # Exceeds maximum
        }

        with pytest.raises(ValidationError) as exc_info:
            UserPreferenceCreateSchema(**data)

        assert "safety_margin_percent" in str(exc_info.value)

    def test_user_preference_update_schema_partial(self):
        """Test user preference update with partial data."""
        data = {
            "ui_theme": "light",
            "default_safety_margin_percent": 15.0,
        }

        schema = UserPreferenceUpdateSchema(**data)
        assert schema.ui_theme == "light"
        assert schema.default_safety_margin_percent == 15.0
        assert schema.default_min_ambient_temp_c is None  # Not provided


class TestUserSchemaEdgeCases:
    """Test edge cases and boundary conditions."""

    def test_user_name_whitespace_handling(self):
        """Test user name whitespace handling."""
        data = {
            "name": "  John Smith  ",  # Whitespace should be stripped
            "email": "<EMAIL>",
            "password": "SecurePass123",
            "is_active": True,
        }

        schema = UserCreateSchema(**data)
        assert schema.name == "John Smith"

    def test_user_preference_theme_case_sensitivity(self):
        """Test user preference theme validation is case sensitive."""
        data = {
            "user_id": 1,
            "ui_theme": "DARK",  # Wrong case
        }

        with pytest.raises(ValidationError) as exc_info:
            UserPreferenceCreateSchema(**data)

        error_message = str(exc_info.value)
        assert "UI theme must be one of" in error_message

    def test_user_preference_equal_temperatures(self):
        """Test user preference with equal min and max temperatures."""
        data = {
            "user_id": 1,
            "default_min_ambient_temp_c": 20.0,
            "default_max_ambient_temp_c": 20.0,  # Equal to min
        }

        with pytest.raises(ValidationError) as exc_info:
            UserPreferenceCreateSchema(**data)

        error_message = str(exc_info.value)
        assert (
            "Maximum ambient temperature must be higher than minimum" in error_message
        )

    def test_user_optional_email(self):
        """Test user creation with optional email."""
        data = {
            "name": "John Smith",
            "password": "SecurePass123",
            "is_active": True,
            # email is optional
        }

        schema = UserCreateSchema(**data)
        assert schema.name == "John Smith"
        assert schema.email is None

    def test_password_minimum_length(self):
        """Test password with exactly minimum length."""
        data = {
            "name": "John Smith",
            "email": "<EMAIL>",
            "password": "Pass123A",  # Exactly 8 characters with requirements
            "is_active": True,
        }

        schema = UserCreateSchema(**data)
        assert len(schema.password) == 8

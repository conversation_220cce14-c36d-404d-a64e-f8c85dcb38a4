<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_718ce007.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">58%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button current">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.0">coverage.py v7.8.0</a>,
            created at 2025-06-01 22:35 +0300
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">function<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_bfd9059a16e3fa4e_main_router_py.html">backend\api\main_router.py</a></td>
                <td class="name left"><a href="z_bfd9059a16e3fa4e_main_router_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_activity_log_routes_py.html#t73">backend\api\v1\activity_log_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_activity_log_routes_py.html#t73"><data value='handle_activity_log_exceptions'>handle_activity_log_exceptions</data></a></td>
                <td>14</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="9 14">64%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_activity_log_routes_py.html#t127">backend\api\v1\activity_log_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_activity_log_routes_py.html#t127"><data value='create_activity_log'>create_activity_log</data></a></td>
                <td>8</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="6 8">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_activity_log_routes_py.html#t157">backend\api\v1\activity_log_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_activity_log_routes_py.html#t157"><data value='list_activity_logs'>list_activity_logs</data></a></td>
                <td>10</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="8 10">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_activity_log_routes_py.html#t222">backend\api\v1\activity_log_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_activity_log_routes_py.html#t222"><data value='update_activity_log'>update_activity_log</data></a></td>
                <td>8</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="6 8">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_activity_log_routes_py.html#t252">backend\api\v1\activity_log_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_activity_log_routes_py.html#t252"><data value='get_user_activity_logs'>get_user_activity_logs</data></a></td>
                <td>7</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="5 7">71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_activity_log_routes_py.html#t283">backend\api\v1\activity_log_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_activity_log_routes_py.html#t283"><data value='get_user_activity_summary'>get_user_activity_summary</data></a></td>
                <td>7</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="5 7">71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_activity_log_routes_py.html#t317">backend\api\v1\activity_log_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_activity_log_routes_py.html#t317"><data value='get_entity_activity_logs'>get_entity_activity_logs</data></a></td>
                <td>7</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="5 7">71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_activity_log_routes_py.html#t354">backend\api\v1\activity_log_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_activity_log_routes_py.html#t354"><data value='get_security_events'>get_security_events</data></a></td>
                <td>7</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="5 7">71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_activity_log_routes_py.html#t387">backend\api\v1\activity_log_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_activity_log_routes_py.html#t387"><data value='log_security_event'>log_security_event</data></a></td>
                <td>15</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="8 15">53%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_activity_log_routes_py.html#t443">backend\api\v1\activity_log_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_activity_log_routes_py.html#t443"><data value='generate_audit_report'>generate_audit_report</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_activity_log_routes_py.html#t483">backend\api\v1\activity_log_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_activity_log_routes_py.html#t483"><data value='get_recent_activity'>get_recent_activity</data></a></td>
                <td>7</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="5 7">71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_activity_log_routes_py.html#t508">backend\api\v1\activity_log_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_activity_log_routes_py.html#t508"><data value='search_activity_logs'>search_activity_logs</data></a></td>
                <td>7</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="5 7">71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_activity_log_routes_py.html#t537">backend\api\v1\activity_log_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_activity_log_routes_py.html#t537"><data value='delete_old_activity_logs'>delete_old_activity_logs</data></a></td>
                <td>8</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="6 8">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_activity_log_routes_py.html#t572">backend\api\v1\activity_log_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_activity_log_routes_py.html#t572"><data value='get_activity_log'>get_activity_log</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_activity_log_routes_py.html">backend\api\v1\activity_log_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_activity_log_routes_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>45</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="39 45">87%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_document_routes_py.html#t102">backend\api\v1\document_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_document_routes_py.html#t102"><data value='handle_document_exceptions'>handle_document_exceptions</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_document_routes_py.html#t150">backend\api\v1\document_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_document_routes_py.html#t150"><data value='get_document_service'>get_document_service</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_document_routes_py.html#t189">backend\api\v1\document_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_document_routes_py.html#t189"><data value='create_imported_data_revision'>create_imported_data_revision</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_document_routes_py.html#t209">backend\api\v1\document_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_document_routes_py.html#t209"><data value='get_imported_data_revision'>get_imported_data_revision</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_document_routes_py.html#t227">backend\api\v1\document_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_document_routes_py.html#t227"><data value='list_imported_data_revisions'>list_imported_data_revisions</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_document_routes_py.html#t258">backend\api\v1\document_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_document_routes_py.html#t258"><data value='update_imported_data_revision'>update_imported_data_revision</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_document_routes_py.html#t279">backend\api\v1\document_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_document_routes_py.html#t279"><data value='delete_imported_data_revision'>delete_imported_data_revision</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_document_routes_py.html#t303">backend\api\v1\document_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_document_routes_py.html#t303"><data value='create_exported_document'>create_exported_document</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_document_routes_py.html#t323">backend\api\v1\document_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_document_routes_py.html#t323"><data value='get_exported_document'>get_exported_document</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_document_routes_py.html#t341">backend\api\v1\document_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_document_routes_py.html#t341"><data value='list_exported_documents'>list_exported_documents</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_document_routes_py.html#t378">backend\api\v1\document_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_document_routes_py.html#t378"><data value='create_calculation_standard'>create_calculation_standard</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_document_routes_py.html#t396">backend\api\v1\document_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_document_routes_py.html#t396"><data value='get_calculation_standard'>get_calculation_standard</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_document_routes_py.html#t414">backend\api\v1\document_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_document_routes_py.html#t414"><data value='get_calculation_standard_by_code'>get_calculation_standard_by_code</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_document_routes_py.html#t432">backend\api\v1\document_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_document_routes_py.html#t432"><data value='list_calculation_standards'>list_calculation_standards</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_document_routes_py.html#t462">backend\api\v1\document_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_document_routes_py.html#t462"><data value='update_calculation_standard'>update_calculation_standard</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_document_routes_py.html#t481">backend\api\v1\document_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_document_routes_py.html#t481"><data value='delete_calculation_standard'>delete_calculation_standard</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_document_routes_py.html#t505">backend\api\v1\document_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_document_routes_py.html#t505"><data value='upload_file'>upload_file</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_document_routes_py.html#t545">backend\api\v1\document_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_document_routes_py.html#t545"><data value='download_document'>download_document</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_document_routes_py.html">backend\api\v1\document_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_document_routes_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>61</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="49 61">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_heat_tracing_routes_py.html#t92">backend\api\v1\heat_tracing_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_heat_tracing_routes_py.html#t92"><data value='get_heat_tracing_service'>get_heat_tracing_service</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_heat_tracing_routes_py.html#t137">backend\api\v1\heat_tracing_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_heat_tracing_routes_py.html#t137"><data value='create_pipe'>create_pipe</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_heat_tracing_routes_py.html#t182">backend\api\v1\heat_tracing_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_heat_tracing_routes_py.html#t182"><data value='get_pipe'>get_pipe</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_heat_tracing_routes_py.html#t226">backend\api\v1\heat_tracing_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_heat_tracing_routes_py.html#t226"><data value='update_pipe'>update_pipe</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_heat_tracing_routes_py.html#t267">backend\api\v1\heat_tracing_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_heat_tracing_routes_py.html#t267"><data value='delete_pipe'>delete_pipe</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_heat_tracing_routes_py.html#t305">backend\api\v1\heat_tracing_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_heat_tracing_routes_py.html#t305"><data value='list_pipes'>list_pipes</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_heat_tracing_routes_py.html#t362">backend\api\v1\heat_tracing_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_heat_tracing_routes_py.html#t362"><data value='create_vessel'>create_vessel</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_heat_tracing_routes_py.html#t409">backend\api\v1\heat_tracing_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_heat_tracing_routes_py.html#t409"><data value='get_vessel'>get_vessel</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_heat_tracing_routes_py.html#t445">backend\api\v1\heat_tracing_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_heat_tracing_routes_py.html#t445"><data value='list_vessels'>list_vessels</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_heat_tracing_routes_py.html#t505">backend\api\v1\heat_tracing_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_heat_tracing_routes_py.html#t505"><data value='calculate_pipe_heat_loss'>calculate_pipe_heat_loss</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_heat_tracing_routes_py.html#t561">backend\api\v1\heat_tracing_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_heat_tracing_routes_py.html#t561"><data value='validate_standards_compliance'>validate_standards_compliance</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_heat_tracing_routes_py.html#t613">backend\api\v1\heat_tracing_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_heat_tracing_routes_py.html#t613"><data value='execute_design_workflow'>execute_design_workflow</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_heat_tracing_routes_py.html#t676">backend\api\v1\heat_tracing_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_heat_tracing_routes_py.html#t676"><data value='get_project_summary'>get_project_summary</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_heat_tracing_routes_py.html#t716">backend\api\v1\heat_tracing_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_heat_tracing_routes_py.html#t716"><data value='get_design_readiness'>get_design_readiness</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_heat_tracing_routes_py.html">backend\api\v1\heat_tracing_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_heat_tracing_routes_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>47</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="39 47">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_project_routes_py.html#t40">backend\api\v1\project_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_project_routes_py.html#t40"><data value='get_project_service'>get_project_service</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_project_routes_py.html#t66">backend\api\v1\project_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_project_routes_py.html#t66"><data value='create_project'>create_project</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_project_routes_py.html#t100">backend\api\v1\project_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_project_routes_py.html#t100"><data value='get_project'>get_project</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_project_routes_py.html#t132">backend\api\v1\project_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_project_routes_py.html#t132"><data value='update_project'>update_project</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_project_routes_py.html#t160">backend\api\v1\project_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_project_routes_py.html#t160"><data value='delete_project'>delete_project</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_project_routes_py.html#t187">backend\api\v1\project_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_project_routes_py.html#t187"><data value='list_projects'>list_projects</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_project_routes_py.html">backend\api\v1\project_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_project_routes_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>22</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="22 22">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_switchboard_routes_py.html#t100">backend\api\v1\switchboard_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_switchboard_routes_py.html#t100"><data value='handle_switchboard_exceptions'>handle_switchboard_exceptions</data></a></td>
                <td>14</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="8 14">57%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_switchboard_routes_py.html#t154">backend\api\v1\switchboard_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_switchboard_routes_py.html#t154"><data value='create_switchboard'>create_switchboard</data></a></td>
                <td>8</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="6 8">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_switchboard_routes_py.html#t178">backend\api\v1\switchboard_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_switchboard_routes_py.html#t178"><data value='get_switchboard'>get_switchboard</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_switchboard_routes_py.html#t201">backend\api\v1\switchboard_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_switchboard_routes_py.html#t201"><data value='list_switchboards'>list_switchboards</data></a></td>
                <td>13</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="10 13">77%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_switchboard_routes_py.html#t256">backend\api\v1\switchboard_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_switchboard_routes_py.html#t256"><data value='update_switchboard'>update_switchboard</data></a></td>
                <td>8</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="6 8">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_switchboard_routes_py.html#t283">backend\api\v1\switchboard_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_switchboard_routes_py.html#t283"><data value='delete_switchboard'>delete_switchboard</data></a></td>
                <td>7</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="5 7">71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_switchboard_routes_py.html#t315">backend\api\v1\switchboard_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_switchboard_routes_py.html#t315"><data value='create_feeder'>create_feeder</data></a></td>
                <td>8</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="6 8">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_switchboard_routes_py.html#t339">backend\api\v1\switchboard_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_switchboard_routes_py.html#t339"><data value='get_feeder'>get_feeder</data></a></td>
                <td>7</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="5 7">71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_switchboard_routes_py.html#t362">backend\api\v1\switchboard_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_switchboard_routes_py.html#t362"><data value='list_feeders'>list_feeders</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_switchboard_routes_py.html#t409">backend\api\v1\switchboard_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_switchboard_routes_py.html#t409"><data value='add_switchboard_component'>add_switchboard_component</data></a></td>
                <td>8</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="6 8">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_switchboard_routes_py.html#t435">backend\api\v1\switchboard_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_switchboard_routes_py.html#t435"><data value='list_switchboard_components'>list_switchboard_components</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_switchboard_routes_py.html#t486">backend\api\v1\switchboard_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_switchboard_routes_py.html#t486"><data value='get_switchboard_load_summary'>get_switchboard_load_summary</data></a></td>
                <td>7</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="5 7">71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_switchboard_routes_py.html#t509">backend\api\v1\switchboard_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_switchboard_routes_py.html#t509"><data value='get_switchboard_capacity_analysis'>get_switchboard_capacity_analysis</data></a></td>
                <td>7</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="5 7">71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_switchboard_routes_py.html">backend\api\v1\switchboard_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_switchboard_routes_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>45</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="36 45">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_user_routes_py.html#t86">backend\api\v1\user_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_user_routes_py.html#t86"><data value='handle_user_exceptions'>handle_user_exceptions</data></a></td>
                <td>18</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="8 18">44%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_user_routes_py.html#t151">backend\api\v1\user_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_user_routes_py.html#t151"><data value='login'>login</data></a></td>
                <td>8</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="6 8">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_user_routes_py.html#t176">backend\api\v1\user_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_user_routes_py.html#t176"><data value='logout'>logout</data></a></td>
                <td>8</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="6 8">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_user_routes_py.html#t200">backend\api\v1\user_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_user_routes_py.html#t200"><data value='change_password'>change_password</data></a></td>
                <td>10</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="7 10">70%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_user_routes_py.html#t231">backend\api\v1\user_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_user_routes_py.html#t231"><data value='request_password_reset'>request_password_reset</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_user_routes_py.html#t258">backend\api\v1\user_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_user_routes_py.html#t258"><data value='confirm_password_reset'>confirm_password_reset</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_user_routes_py.html#t291">backend\api\v1\user_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_user_routes_py.html#t291"><data value='create_user'>create_user</data></a></td>
                <td>8</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="6 8">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_user_routes_py.html#t315">backend\api\v1\user_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_user_routes_py.html#t315"><data value='get_user'>get_user</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_user_routes_py.html#t338">backend\api\v1\user_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_user_routes_py.html#t338"><data value='list_users'>list_users</data></a></td>
                <td>12</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="11 12">92%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_user_routes_py.html#t381">backend\api\v1\user_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_user_routes_py.html#t381"><data value='update_user'>update_user</data></a></td>
                <td>8</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="6 8">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_user_routes_py.html#t406">backend\api\v1\user_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_user_routes_py.html#t406"><data value='deactivate_user'>deactivate_user</data></a></td>
                <td>7</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="5 7">71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_user_routes_py.html#t434">backend\api\v1\user_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_user_routes_py.html#t434"><data value='get_user_preferences'>get_user_preferences</data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_user_routes_py.html#t463">backend\api\v1\user_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_user_routes_py.html#t463"><data value='update_user_preferences'>update_user_preferences</data></a></td>
                <td>8</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="6 8">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_user_routes_py.html#t490">backend\api\v1\user_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_user_routes_py.html#t490"><data value='delete_user_preferences'>delete_user_preferences</data></a></td>
                <td>7</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="5 7">71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_user_routes_py.html">backend\api\v1\user_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_user_routes_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>47</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="38 47">81%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f96405ab037cc57a___init___py.html">backend\core\calculations\__init__.py</a></td>
                <td class="name left"><a href="z_f96405ab037cc57a___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f96405ab037cc57a_calculation_service_py.html#t40">backend\core\calculations\calculation_service.py</a></td>
                <td class="name left"><a href="z_f96405ab037cc57a_calculation_service_py.html#t40"><data value='post_init__'>CalculationResult.__post_init__</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f96405ab037cc57a_calculation_service_py.html#t103">backend\core\calculations\calculation_service.py</a></td>
                <td class="name left"><a href="z_f96405ab037cc57a_calculation_service_py.html#t103"><data value='init__'>CalculationService.__init__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f96405ab037cc57a_calculation_service_py.html#t107">backend\core\calculations\calculation_service.py</a></td>
                <td class="name left"><a href="z_f96405ab037cc57a_calculation_service_py.html#t107"><data value='calculate_heat_loss'>CalculationService.calculate_heat_loss</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f96405ab037cc57a_calculation_service_py.html#t196">backend\core\calculations\calculation_service.py</a></td>
                <td class="name left"><a href="z_f96405ab037cc57a_calculation_service_py.html#t196"><data value='calculate_cable_sizing'>CalculationService.calculate_cable_sizing</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f96405ab037cc57a_calculation_service_py.html#t286">backend\core\calculations\calculation_service.py</a></td>
                <td class="name left"><a href="z_f96405ab037cc57a_calculation_service_py.html#t286"><data value='validate_heat_loss_inputs'>CalculationService._validate_heat_loss_inputs</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f96405ab037cc57a_calculation_service_py.html#t305">backend\core\calculations\calculation_service.py</a></td>
                <td class="name left"><a href="z_f96405ab037cc57a_calculation_service_py.html#t305"><data value='validate_cable_sizing_inputs'>CalculationService._validate_cable_sizing_inputs</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f96405ab037cc57a_calculation_service_py.html#t319">backend\core\calculations\calculation_service.py</a></td>
                <td class="name left"><a href="z_f96405ab037cc57a_calculation_service_py.html#t319"><data value='calculate_surface_temperature'>CalculationService._calculate_surface_temperature</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f96405ab037cc57a_calculation_service_py.html">backend\core\calculations\calculation_service.py</a></td>
                <td class="name left"><a href="z_f96405ab037cc57a_calculation_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>55</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="55 55">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_da9c56813bb6ca47___init___py.html">backend\core\calculations\electrical_sizing\__init__.py</a></td>
                <td class="name left"><a href="z_da9c56813bb6ca47___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_da9c56813bb6ca47_cable_sizing_py.html#t57">backend\core\calculations\electrical_sizing\cable_sizing.py</a></td>
                <td class="name left"><a href="z_da9c56813bb6ca47_cable_sizing_py.html#t57"><data value='select_optimal_cable'>select_optimal_cable</data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_da9c56813bb6ca47_cable_sizing_py.html#t120">backend\core\calculations\electrical_sizing\cable_sizing.py</a></td>
                <td class="name left"><a href="z_da9c56813bb6ca47_cable_sizing_py.html#t120"><data value='calculate_cable_parameters'>calculate_cable_parameters</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_da9c56813bb6ca47_cable_sizing_py.html#t191">backend\core\calculations\electrical_sizing\cable_sizing.py</a></td>
                <td class="name left"><a href="z_da9c56813bb6ca47_cable_sizing_py.html#t191"><data value='is_cable_suitable'>_is_cable_suitable</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_da9c56813bb6ca47_cable_sizing_py.html#t217">backend\core\calculations\electrical_sizing\cable_sizing.py</a></td>
                <td class="name left"><a href="z_da9c56813bb6ca47_cable_sizing_py.html#t217"><data value='calculate_suitability_score'>_calculate_suitability_score</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_da9c56813bb6ca47_cable_sizing_py.html#t248">backend\core\calculations\electrical_sizing\cable_sizing.py</a></td>
                <td class="name left"><a href="z_da9c56813bb6ca47_cable_sizing_py.html#t248"><data value='get_available_cables'>get_available_cables</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_da9c56813bb6ca47_cable_sizing_py.html#t253">backend\core\calculations\electrical_sizing\cable_sizing.py</a></td>
                <td class="name left"><a href="z_da9c56813bb6ca47_cable_sizing_py.html#t253"><data value='get_cable_properties'>get_cable_properties</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_da9c56813bb6ca47_cable_sizing_py.html">backend\core\calculations\electrical_sizing\cable_sizing.py</a></td>
                <td class="name left"><a href="z_da9c56813bb6ca47_cable_sizing_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_da9c56813bb6ca47_voltage_drop_py.html#t18">backend\core\calculations\electrical_sizing\voltage_drop.py</a></td>
                <td class="name left"><a href="z_da9c56813bb6ca47_voltage_drop_py.html#t18"><data value='calculate_voltage_drop'>calculate_voltage_drop</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_da9c56813bb6ca47_voltage_drop_py.html#t85">backend\core\calculations\electrical_sizing\voltage_drop.py</a></td>
                <td class="name left"><a href="z_da9c56813bb6ca47_voltage_drop_py.html#t85"><data value='calculate_voltage_drop_percentage'>calculate_voltage_drop_percentage</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_da9c56813bb6ca47_voltage_drop_py.html#t119">backend\core\calculations\electrical_sizing\voltage_drop.py</a></td>
                <td class="name left"><a href="z_da9c56813bb6ca47_voltage_drop_py.html#t119"><data value='calculate_maximum_cable_length'>calculate_maximum_cable_length</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_da9c56813bb6ca47_voltage_drop_py.html#t176">backend\core\calculations\electrical_sizing\voltage_drop.py</a></td>
                <td class="name left"><a href="z_da9c56813bb6ca47_voltage_drop_py.html#t176"><data value='calculate_required_cable_size'>calculate_required_cable_size</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_da9c56813bb6ca47_voltage_drop_py.html#t242">backend\core\calculations\electrical_sizing\voltage_drop.py</a></td>
                <td class="name left"><a href="z_da9c56813bb6ca47_voltage_drop_py.html#t242"><data value='validate_voltage_drop_compliance'>validate_voltage_drop_compliance</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_da9c56813bb6ca47_voltage_drop_py.html">backend\core\calculations\electrical_sizing\voltage_drop.py</a></td>
                <td class="name left"><a href="z_da9c56813bb6ca47_voltage_drop_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee7a23c30801740___init___py.html">backend\core\calculations\heat_loss\__init__.py</a></td>
                <td class="name left"><a href="z_aee7a23c30801740___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee7a23c30801740_insulation_properties_py.html#t108">backend\core\calculations\heat_loss\insulation_properties.py</a></td>
                <td class="name left"><a href="z_aee7a23c30801740_insulation_properties_py.html#t108"><data value='get_insulation_properties'>get_insulation_properties</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee7a23c30801740_insulation_properties_py.html#t137">backend\core\calculations\heat_loss\insulation_properties.py</a></td>
                <td class="name left"><a href="z_aee7a23c30801740_insulation_properties_py.html#t137"><data value='get_temperature_corrected_conductivity'>get_temperature_corrected_conductivity</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee7a23c30801740_insulation_properties_py.html#t179">backend\core\calculations\heat_loss\insulation_properties.py</a></td>
                <td class="name left"><a href="z_aee7a23c30801740_insulation_properties_py.html#t179"><data value='validate_insulation_temperature'>validate_insulation_temperature</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee7a23c30801740_insulation_properties_py.html#t204">backend\core\calculations\heat_loss\insulation_properties.py</a></td>
                <td class="name left"><a href="z_aee7a23c30801740_insulation_properties_py.html#t204"><data value='get_recommended_insulation'>get_recommended_insulation</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee7a23c30801740_insulation_properties_py.html#t251">backend\core\calculations\heat_loss\insulation_properties.py</a></td>
                <td class="name left"><a href="z_aee7a23c30801740_insulation_properties_py.html#t251"><data value='calculate_insulation_thickness'>calculate_insulation_thickness</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee7a23c30801740_insulation_properties_py.html#t311">backend\core\calculations\heat_loss\insulation_properties.py</a></td>
                <td class="name left"><a href="z_aee7a23c30801740_insulation_properties_py.html#t311"><data value='get_all_insulation_types'>get_all_insulation_types</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee7a23c30801740_insulation_properties_py.html#t316">backend\core\calculations\heat_loss\insulation_properties.py</a></td>
                <td class="name left"><a href="z_aee7a23c30801740_insulation_properties_py.html#t316"><data value='get_insulation_summary'>get_insulation_summary</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee7a23c30801740_insulation_properties_py.html">backend\core\calculations\heat_loss\insulation_properties.py</a></td>
                <td class="name left"><a href="z_aee7a23c30801740_insulation_properties_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee7a23c30801740_pipe_heat_loss_py.html#t24">backend\core\calculations\heat_loss\pipe_heat_loss.py</a></td>
                <td class="name left"><a href="z_aee7a23c30801740_pipe_heat_loss_py.html#t24"><data value='calculate_pipe_heat_loss'>calculate_pipe_heat_loss</data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee7a23c30801740_pipe_heat_loss_py.html#t110">backend\core\calculations\heat_loss\pipe_heat_loss.py</a></td>
                <td class="name left"><a href="z_aee7a23c30801740_pipe_heat_loss_py.html#t110"><data value='validate_pipe_inputs'>_validate_pipe_inputs</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee7a23c30801740_pipe_heat_loss_py.html#t134">backend\core\calculations\heat_loss\pipe_heat_loss.py</a></td>
                <td class="name left"><a href="z_aee7a23c30801740_pipe_heat_loss_py.html#t134"><data value='calculate_surface_temperature_iterative'>_calculate_surface_temperature_iterative</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee7a23c30801740_pipe_heat_loss_py.html#t179">backend\core\calculations\heat_loss\pipe_heat_loss.py</a></td>
                <td class="name left"><a href="z_aee7a23c30801740_pipe_heat_loss_py.html#t179"><data value='calculate_convection_coefficient'>_calculate_convection_coefficient</data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee7a23c30801740_pipe_heat_loss_py.html#t231">backend\core\calculations\heat_loss\pipe_heat_loss.py</a></td>
                <td class="name left"><a href="z_aee7a23c30801740_pipe_heat_loss_py.html#t231"><data value='calculate_radiation_coefficient'>_calculate_radiation_coefficient</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee7a23c30801740_pipe_heat_loss_py.html#t239">backend\core\calculations\heat_loss\pipe_heat_loss.py</a></td>
                <td class="name left"><a href="z_aee7a23c30801740_pipe_heat_loss_py.html#t239"><data value='calculate_pipe_heat_loss_simplified'>calculate_pipe_heat_loss_simplified</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee7a23c30801740_pipe_heat_loss_py.html">backend\core\calculations\heat_loss\pipe_heat_loss.py</a></td>
                <td class="name left"><a href="z_aee7a23c30801740_pipe_heat_loss_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee7a23c30801740_vessel_heat_loss_py.html#t17">backend\core\calculations\heat_loss\vessel_heat_loss.py</a></td>
                <td class="name left"><a href="z_aee7a23c30801740_vessel_heat_loss_py.html#t17"><data value='calculate_vessel_heat_loss'>calculate_vessel_heat_loss</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee7a23c30801740_vessel_heat_loss_py.html#t81">backend\core\calculations\heat_loss\vessel_heat_loss.py</a></td>
                <td class="name left"><a href="z_aee7a23c30801740_vessel_heat_loss_py.html#t81"><data value='validate_vessel_inputs'>_validate_vessel_inputs</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee7a23c30801740_vessel_heat_loss_py.html#t108">backend\core\calculations\heat_loss\vessel_heat_loss.py</a></td>
                <td class="name left"><a href="z_aee7a23c30801740_vessel_heat_loss_py.html#t108"><data value='calculate_cylinder_heat_loss'>_calculate_cylinder_heat_loss</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee7a23c30801740_vessel_heat_loss_py.html#t168">backend\core\calculations\heat_loss\vessel_heat_loss.py</a></td>
                <td class="name left"><a href="z_aee7a23c30801740_vessel_heat_loss_py.html#t168"><data value='calculate_sphere_heat_loss'>_calculate_sphere_heat_loss</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee7a23c30801740_vessel_heat_loss_py.html">backend\core\calculations\heat_loss\vessel_heat_loss.py</a></td>
                <td class="name left"><a href="z_aee7a23c30801740_vessel_heat_loss_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ab7802d47b18b7f9___init___py.html">backend\core\database\__init__.py</a></td>
                <td class="name left"><a href="z_ab7802d47b18b7f9___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ab7802d47b18b7f9_engine_py.html#t34">backend\core\database\engine.py</a></td>
                <td class="name left"><a href="z_ab7802d47b18b7f9_engine_py.html#t34"><data value='create_engine'>create_engine</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ab7802d47b18b7f9_engine_py.html#t79">backend\core\database\engine.py</a></td>
                <td class="name left"><a href="z_ab7802d47b18b7f9_engine_py.html#t79"><data value='create_engine_with_fallback'>_create_engine_with_fallback</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ab7802d47b18b7f9_engine_py.html#t108">backend\core\database\engine.py</a></td>
                <td class="name left"><a href="z_ab7802d47b18b7f9_engine_py.html#t108"><data value='try_create_engine'>_try_create_engine</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ab7802d47b18b7f9_engine_py.html#t160">backend\core\database\engine.py</a></td>
                <td class="name left"><a href="z_ab7802d47b18b7f9_engine_py.html#t160"><data value='get_db_type_from_url'>_get_db_type_from_url</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ab7802d47b18b7f9_engine_py.html#t175">backend\core\database\engine.py</a></td>
                <td class="name left"><a href="z_ab7802d47b18b7f9_engine_py.html#t175"><data value='get_engine'>get_engine</data></a></td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ab7802d47b18b7f9_engine_py.html#t193">backend\core\database\engine.py</a></td>
                <td class="name left"><a href="z_ab7802d47b18b7f9_engine_py.html#t193"><data value='close_engine'>close_engine</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ab7802d47b18b7f9_engine_py.html">backend\core\database\engine.py</a></td>
                <td class="name left"><a href="z_ab7802d47b18b7f9_engine_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>23</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="18 23">78%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ab7802d47b18b7f9_initialization_py.html#t34">backend\core\database\initialization.py</a></td>
                <td class="name left"><a href="z_ab7802d47b18b7f9_initialization_py.html#t34"><data value='initialize_database'>initialize_database</data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ab7802d47b18b7f9_initialization_py.html#t88">backend\core\database\initialization.py</a></td>
                <td class="name left"><a href="z_ab7802d47b18b7f9_initialization_py.html#t88"><data value='run_alembic_migrations'>run_alembic_migrations</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ab7802d47b18b7f9_initialization_py.html#t120">backend\core\database\initialization.py</a></td>
                <td class="name left"><a href="z_ab7802d47b18b7f9_initialization_py.html#t120"><data value='create_initial_migration'>create_initial_migration</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ab7802d47b18b7f9_initialization_py.html#t148">backend\core\database\initialization.py</a></td>
                <td class="name left"><a href="z_ab7802d47b18b7f9_initialization_py.html#t148"><data value='check_migration_status'>check_migration_status</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ab7802d47b18b7f9_initialization_py.html#t172">backend\core\database\initialization.py</a></td>
                <td class="name left"><a href="z_ab7802d47b18b7f9_initialization_py.html#t172"><data value='get_alembic_config_path'>_get_alembic_config_path</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ab7802d47b18b7f9_initialization_py.html#t181">backend\core\database\initialization.py</a></td>
                <td class="name left"><a href="z_ab7802d47b18b7f9_initialization_py.html#t181"><data value='verify_database_connection'>_verify_database_connection</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ab7802d47b18b7f9_initialization_py.html#t203">backend\core\database\initialization.py</a></td>
                <td class="name left"><a href="z_ab7802d47b18b7f9_initialization_py.html#t203"><data value='reset_database'>reset_database</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ab7802d47b18b7f9_initialization_py.html">backend\core\database\initialization.py</a></td>
                <td class="name left"><a href="z_ab7802d47b18b7f9_initialization_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>26</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="21 26">81%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ab7802d47b18b7f9_session_py.html#t34">backend\core\database\session.py</a></td>
                <td class="name left"><a href="z_ab7802d47b18b7f9_session_py.html#t34"><data value='get_session_factory'>get_session_factory</data></a></td>
                <td>6</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="3 6">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ab7802d47b18b7f9_session_py.html#t61">backend\core\database\session.py</a></td>
                <td class="name left"><a href="z_ab7802d47b18b7f9_session_py.html#t61"><data value='get_scoped_session'>get_scoped_session</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ab7802d47b18b7f9_session_py.html#t82">backend\core\database\session.py</a></td>
                <td class="name left"><a href="z_ab7802d47b18b7f9_session_py.html#t82"><data value='get_db_session'>get_db_session</data></a></td>
                <td>17</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="1 17">6%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ab7802d47b18b7f9_session_py.html#t118">backend\core\database\session.py</a></td>
                <td class="name left"><a href="z_ab7802d47b18b7f9_session_py.html#t118"><data value='get_db'>get_db</data></a></td>
                <td>2</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="1 2">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ab7802d47b18b7f9_session_py.html#t136">backend\core\database\session.py</a></td>
                <td class="name left"><a href="z_ab7802d47b18b7f9_session_py.html#t136"><data value='create_tables'>create_tables</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ab7802d47b18b7f9_session_py.html#t161">backend\core\database\session.py</a></td>
                <td class="name left"><a href="z_ab7802d47b18b7f9_session_py.html#t161"><data value='drop_tables'>drop_tables</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ab7802d47b18b7f9_session_py.html#t185">backend\core\database\session.py</a></td>
                <td class="name left"><a href="z_ab7802d47b18b7f9_session_py.html#t185"><data value='reset_session_factory'>reset_session_factory</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ab7802d47b18b7f9_session_py.html">backend\core\database\session.py</a></td>
                <td class="name left"><a href="z_ab7802d47b18b7f9_session_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>25</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="21 25">84%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_71ce108dda7d12d3_error_factory_py.html#t8">backend\core\errors\error_factory.py</a></td>
                <td class="name left"><a href="z_71ce108dda7d12d3_error_factory_py.html#t8"><data value='create_exception'>ErrorFactory.create_exception</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_71ce108dda7d12d3_error_factory_py.html">backend\core\errors\error_factory.py</a></td>
                <td class="name left"><a href="z_71ce108dda7d12d3_error_factory_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_71ce108dda7d12d3_error_registry_py.html">backend\core\errors\error_registry.py</a></td>
                <td class="name left"><a href="z_71ce108dda7d12d3_error_registry_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_71ce108dda7d12d3_error_templates_py.html">backend\core\errors\error_templates.py</a></td>
                <td class="name left"><a href="z_71ce108dda7d12d3_error_templates_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_71ce108dda7d12d3_exceptions_py.html#t8">backend\core\errors\exceptions.py</a></td>
                <td class="name left"><a href="z_71ce108dda7d12d3_exceptions_py.html#t8"><data value='init__'>BaseApplicationException.__init__</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_71ce108dda7d12d3_exceptions_py.html#t30">backend\core\errors\exceptions.py</a></td>
                <td class="name left"><a href="z_71ce108dda7d12d3_exceptions_py.html#t30"><data value='init__'>ProjectNotFoundError.__init__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_71ce108dda7d12d3_exceptions_py.html#t41">backend\core\errors\exceptions.py</a></td>
                <td class="name left"><a href="z_71ce108dda7d12d3_exceptions_py.html#t41"><data value='init__'>DataValidationError.__init__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_71ce108dda7d12d3_exceptions_py.html#t52">backend\core\errors\exceptions.py</a></td>
                <td class="name left"><a href="z_71ce108dda7d12d3_exceptions_py.html#t52"><data value='init__'>InvalidInputError.__init__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_71ce108dda7d12d3_exceptions_py.html#t63">backend\core\errors\exceptions.py</a></td>
                <td class="name left"><a href="z_71ce108dda7d12d3_exceptions_py.html#t63"><data value='init__'>DuplicateEntryError.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_71ce108dda7d12d3_exceptions_py.html#t79">backend\core\errors\exceptions.py</a></td>
                <td class="name left"><a href="z_71ce108dda7d12d3_exceptions_py.html#t79"><data value='init__'>DatabaseError.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_71ce108dda7d12d3_exceptions_py.html#t95">backend\core\errors\exceptions.py</a></td>
                <td class="name left"><a href="z_71ce108dda7d12d3_exceptions_py.html#t95"><data value='init__'>ComponentNotFoundError.__init__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_71ce108dda7d12d3_exceptions_py.html#t106">backend\core\errors\exceptions.py</a></td>
                <td class="name left"><a href="z_71ce108dda7d12d3_exceptions_py.html#t106"><data value='init__'>CalculationError.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_71ce108dda7d12d3_exceptions_py.html#t119">backend\core\errors\exceptions.py</a></td>
                <td class="name left"><a href="z_71ce108dda7d12d3_exceptions_py.html#t119"><data value='init__'>StandardComplianceError.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_71ce108dda7d12d3_exceptions_py.html#t126">backend\core\errors\exceptions.py</a></td>
                <td class="name left"><a href="z_71ce108dda7d12d3_exceptions_py.html#t126"><data value='init__'>BusinessLogicError.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_71ce108dda7d12d3_exceptions_py.html">backend\core\errors\exceptions.py</a></td>
                <td class="name left"><a href="z_71ce108dda7d12d3_exceptions_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>23</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="23 23">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24___init___py.html">backend\core\models\__init__.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_activity_log_py.html#t28">backend\core\models\activity_log.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_activity_log_py.html#t28"><data value='repr__'>ActivityLog.__repr__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_activity_log_py.html">backend\core\models\activity_log.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_activity_log_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_base_py.html#t16">backend\core\models\base.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_base_py.html#t16"><data value='id'>CommonColumns.id</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_base_py.html#t20">backend\core\models\base.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_base_py.html#t20"><data value='name'>CommonColumns.name</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_base_py.html#t24">backend\core\models\base.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_base_py.html#t24"><data value='notes'>CommonColumns.notes</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_base_py.html#t28">backend\core\models\base.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_base_py.html#t28"><data value='created_at'>CommonColumns.created_at</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_base_py.html#t32">backend\core\models\base.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_base_py.html#t32"><data value='updated_at'>CommonColumns.updated_at</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_base_py.html#t41">backend\core\models\base.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_base_py.html#t41"><data value='is_deleted'>SoftDeleteColumns.is_deleted</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_base_py.html#t45">backend\core\models\base.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_base_py.html#t45"><data value='deleted_at'>SoftDeleteColumns.deleted_at</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_base_py.html#t49">backend\core\models\base.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_base_py.html#t49"><data value='deleted_by_user_id'>SoftDeleteColumns.deleted_by_user_id</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_base_py.html#t53">backend\core\models\base.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_base_py.html#t53"><data value='deleted_by_user'>SoftDeleteColumns.deleted_by_user</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_base_py.html#t66">backend\core\models\base.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_base_py.html#t66"><data value='init__'>EnumType.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_base_py.html#t70">backend\core\models\base.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_base_py.html#t70"><data value='process_bind_param'>EnumType.process_bind_param</data></a></td>
                <td>5</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="4 5">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_base_py.html#t79">backend\core\models\base.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_base_py.html#t79"><data value='process_result_value'>EnumType.process_result_value</data></a></td>
                <td>8</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="5 8">62%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_base_py.html#t89">backend\core\models\base.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_base_py.html#t89"><data value='copy'>EnumType.copy</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_base_py.html">backend\core\models\base.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_base_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>32</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="32 32">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_components_py.html#t39">backend\core\models\components.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_components_py.html#t39"><data value='repr__'>ComponentCategory.__repr__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_components_py.html#t65">backend\core\models\components.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_components_py.html#t65"><data value='repr__'>Component.__repr__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_components_py.html">backend\core\models\components.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_components_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>22</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="22 22">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_documents_py.html#t43">backend\core\models\documents.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_documents_py.html#t43"><data value='repr__'>ImportedDataRevision.__repr__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_documents_py.html#t81">backend\core\models\documents.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_documents_py.html#t81"><data value='repr__'>ExportedDocument.__repr__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_documents_py.html#t95">backend\core\models\documents.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_documents_py.html#t95"><data value='repr__'>CalculationStandard.__repr__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_documents_py.html">backend\core\models\documents.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_documents_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>39</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="39 39">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_electrical_py.html#t81">backend\core\models\electrical.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_electrical_py.html#t81"><data value='repr__'>ElectricalNode.__repr__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_electrical_py.html#t140">backend\core\models\electrical.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_electrical_py.html#t140"><data value='repr__'>CableRoute.__repr__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_electrical_py.html#t197">backend\core\models\electrical.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_electrical_py.html#t197"><data value='repr__'>CableSegment.__repr__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_electrical_py.html#t294">backend\core\models\electrical.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_electrical_py.html#t294"><data value='repr__'>LoadCalculation.__repr__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_electrical_py.html#t379">backend\core\models\electrical.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_electrical_py.html#t379"><data value='repr__'>VoltageDropCalculation.__repr__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_electrical_py.html">backend\core\models\electrical.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_electrical_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>134</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="134 134">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_enums_py.html">backend\core\models\enums.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_enums_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>75</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="75 75">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_heat_tracing_py.html#t76">backend\core\models\heat_tracing.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_heat_tracing_py.html#t76"><data value='repr__'>Pipe.__repr__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_heat_tracing_py.html#t129">backend\core\models\heat_tracing.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_heat_tracing_py.html#t129"><data value='repr__'>Vessel.__repr__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_heat_tracing_py.html#t172">backend\core\models\heat_tracing.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_heat_tracing_py.html#t172"><data value='repr__'>ControlCircuit.__repr__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_heat_tracing_py.html#t233">backend\core\models\heat_tracing.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_heat_tracing_py.html#t233"><data value='repr__'>HTCircuit.__repr__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_heat_tracing_py.html#t264">backend\core\models\heat_tracing.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_heat_tracing_py.html#t264"><data value='repr__'>ControlCircuitComponent.__repr__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_heat_tracing_py.html#t290">backend\core\models\heat_tracing.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_heat_tracing_py.html#t290"><data value='repr__'>HTCircuitComponent.__repr__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_heat_tracing_py.html">backend\core\models\heat_tracing.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_heat_tracing_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>128</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="128 128">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_project_py.html#t75">backend\core\models\project.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_project_py.html#t75"><data value='repr__'>Project.__repr__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_project_py.html#t90">backend\core\models\project.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_project_py.html#t90"><data value='validate_project_data'>validate_project_data</data></a></td>
                <td>32</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="17 32">53%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_project_py.html#t224">backend\core\models\project.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_project_py.html#t224"><data value='before_insert_listener'>before_insert_listener</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_project_py.html#t230">backend\core\models\project.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_project_py.html#t230"><data value='before_update_listener'>before_update_listener</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_project_py.html">backend\core\models\project.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_project_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>39</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="39 39">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_switchboard_py.html#t39">backend\core\models\switchboard.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_switchboard_py.html#t39"><data value='repr__'>Switchboard.__repr__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_switchboard_py.html#t66">backend\core\models\switchboard.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_switchboard_py.html#t66"><data value='repr__'>Feeder.__repr__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_switchboard_py.html#t95">backend\core\models\switchboard.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_switchboard_py.html#t95"><data value='repr__'>SwitchboardComponent.__repr__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_switchboard_py.html#t119">backend\core\models\switchboard.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_switchboard_py.html#t119"><data value='repr__'>FeederComponent.__repr__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_switchboard_py.html">backend\core\models\switchboard.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_switchboard_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>49</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="49 49">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_users_py.html#t52">backend\core\models\users.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_users_py.html#t52"><data value='repr__'>User.__repr__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_users_py.html#t86">backend\core\models\users.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_users_py.html#t86"><data value='repr__'>UserPreference.__repr__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_users_py.html">backend\core\models\users.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_users_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>28</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="27 28">96%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_activity_log_repository_py.html#t53">backend\core\repositories\activity_log_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_activity_log_repository_py.html#t53"><data value='init__'>ActivityLogRepository.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_activity_log_repository_py.html#t63">backend\core\repositories\activity_log_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_activity_log_repository_py.html#t63"><data value='get_by_user_id'>ActivityLogRepository.get_by_user_id</data></a></td>
                <td>10</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="9 10">90%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_activity_log_repository_py.html#t104">backend\core\repositories\activity_log_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_activity_log_repository_py.html#t104"><data value='get_by_entity'>ActivityLogRepository.get_by_entity</data></a></td>
                <td>10</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="6 10">60%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_activity_log_repository_py.html#t153">backend\core\repositories\activity_log_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_activity_log_repository_py.html#t153"><data value='get_by_event_type'>ActivityLogRepository.get_by_event_type</data></a></td>
                <td>10</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="6 10">60%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_activity_log_repository_py.html#t196">backend\core\repositories\activity_log_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_activity_log_repository_py.html#t196"><data value='get_by_date_range'>ActivityLogRepository.get_by_date_range</data></a></td>
                <td>10</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="6 10">60%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_activity_log_repository_py.html#t249">backend\core\repositories\activity_log_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_activity_log_repository_py.html#t249"><data value='search_by_details'>ActivityLogRepository.search_by_details</data></a></td>
                <td>11</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="7 11">64%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_activity_log_repository_py.html#t291">backend\core\repositories\activity_log_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_activity_log_repository_py.html#t291"><data value='filter_activity_logs'>ActivityLogRepository.filter_activity_logs</data></a></td>
                <td>29</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="25 29">86%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_activity_log_repository_py.html#t354">backend\core\repositories\activity_log_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_activity_log_repository_py.html#t354"><data value='count_filtered_activity_logs'>ActivityLogRepository.count_filtered_activity_logs</data></a></td>
                <td>28</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="18 28">64%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_activity_log_repository_py.html#t410">backend\core\repositories\activity_log_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_activity_log_repository_py.html#t410"><data value='get_security_events'>ActivityLogRepository.get_security_events</data></a></td>
                <td>11</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="7 11">64%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_activity_log_repository_py.html#t467">backend\core\repositories\activity_log_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_activity_log_repository_py.html#t467"><data value='get_user_activity_summary'>ActivityLogRepository.get_user_activity_summary</data></a></td>
                <td>17</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="13 17">76%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_activity_log_repository_py.html#t555">backend\core\repositories\activity_log_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_activity_log_repository_py.html#t555"><data value='get_daily_activity_counts'>ActivityLogRepository.get_daily_activity_counts</data></a></td>
                <td>11</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="7 11">64%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_activity_log_repository_py.html#t607">backend\core\repositories\activity_log_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_activity_log_repository_py.html#t607"><data value='get_recent_activity'>ActivityLogRepository.get_recent_activity</data></a></td>
                <td>10</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="6 10">60%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_activity_log_repository_py.html#t634">backend\core\repositories\activity_log_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_activity_log_repository_py.html#t634"><data value='delete_old_logs'>ActivityLogRepository.delete_old_logs</data></a></td>
                <td>16</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="10 16">62%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_activity_log_repository_py.html#t674">backend\core\repositories\activity_log_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_activity_log_repository_py.html#t674"><data value='get_unique_users_count'>ActivityLogRepository.get_unique_users_count</data></a></td>
                <td>10</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="6 10">60%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_activity_log_repository_py.html#t710">backend\core\repositories\activity_log_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_activity_log_repository_py.html#t710"><data value='get_unique_entities_count'>ActivityLogRepository.get_unique_entities_count</data></a></td>
                <td>10</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="6 10">60%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_activity_log_repository_py.html">backend\core\repositories\activity_log_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_activity_log_repository_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>33</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="28 33">85%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_base_repository_py.html#t30">backend\core\repositories\base_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_base_repository_py.html#t30"><data value='init__'>BaseRepository.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_base_repository_py.html#t34">backend\core\repositories\base_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_base_repository_py.html#t34"><data value='handle_db_exception'>BaseRepository._handle_db_exception</data></a></td>
                <td>5</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="3 5">60%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_base_repository_py.html#t53">backend\core\repositories\base_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_base_repository_py.html#t53"><data value='get_by_id'>BaseRepository.get_by_id</data></a></td>
                <td>6</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="3 6">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_base_repository_py.html#t61">backend\core\repositories\base_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_base_repository_py.html#t61"><data value='get_all'>BaseRepository.get_all</data></a></td>
                <td>6</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="3 6">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_base_repository_py.html#t69">backend\core\repositories\base_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_base_repository_py.html#t69"><data value='create'>BaseRepository.create</data></a></td>
                <td>7</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="4 7">57%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_base_repository_py.html#t82">backend\core\repositories\base_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_base_repository_py.html#t82"><data value='update'>BaseRepository.update</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_base_repository_py.html#t112">backend\core\repositories\base_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_base_repository_py.html#t112"><data value='delete'>BaseRepository.delete</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_base_repository_py.html">backend\core\repositories\base_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_base_repository_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>21</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="17 21">81%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_component_repository_py.html#t24">backend\core\repositories\component_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_component_repository_py.html#t24"><data value='init__'>ComponentRepository.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_component_repository_py.html#t29">backend\core\repositories\component_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_component_repository_py.html#t29"><data value='get_by_name_and_category'>ComponentRepository.get_by_name_and_category</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_component_repository_py.html#t67">backend\core\repositories\component_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_component_repository_py.html#t67"><data value='get_by_category'>ComponentRepository.get_by_category</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_component_repository_py.html#t107">backend\core\repositories\component_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_component_repository_py.html#t107"><data value='search_components'>ComponentRepository.search_components</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_component_repository_py.html#t172">backend\core\repositories\component_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_component_repository_py.html#t172"><data value='get_active_components'>ComponentRepository.get_active_components</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_component_repository_py.html#t206">backend\core\repositories\component_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_component_repository_py.html#t206"><data value='count_by_category'>ComponentRepository.count_by_category</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_component_repository_py.html#t242">backend\core\repositories\component_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_component_repository_py.html#t242"><data value='init__'>ComponentCategoryRepository.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_component_repository_py.html#t247">backend\core\repositories\component_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_component_repository_py.html#t247"><data value='get_by_name'>ComponentCategoryRepository.get_by_name</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_component_repository_py.html#t283">backend\core\repositories\component_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_component_repository_py.html#t283"><data value='get_root_categories'>ComponentCategoryRepository.get_root_categories</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_component_repository_py.html#t322">backend\core\repositories\component_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_component_repository_py.html#t322"><data value='get_subcategories'>ComponentCategoryRepository.get_subcategories</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_component_repository_py.html">backend\core\repositories\component_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_component_repository_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>20</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="20 20">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_document_repository_py.html#t38">backend\core\repositories\document_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_document_repository_py.html#t38"><data value='init__'>ImportedDataRevisionRepository.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_document_repository_py.html#t48">backend\core\repositories\document_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_document_repository_py.html#t48"><data value='get_by_project_id'>ImportedDataRevisionRepository.get_by_project_id</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_document_repository_py.html#t94">backend\core\repositories\document_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_document_repository_py.html#t94"><data value='get_active_revision_by_filename'>ImportedDataRevisionRepository.get_active_revision_by_filename</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_document_repository_py.html#t141">backend\core\repositories\document_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_document_repository_py.html#t141"><data value='get_revisions_by_filename'>ImportedDataRevisionRepository.get_revisions_by_filename</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_document_repository_py.html#t183">backend\core\repositories\document_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_document_repository_py.html#t183"><data value='deactivate_other_revisions'>ImportedDataRevisionRepository.deactivate_other_revisions</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_document_repository_py.html#t234">backend\core\repositories\document_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_document_repository_py.html#t234"><data value='get_with_related_data'>ImportedDataRevisionRepository.get_with_related_data</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_document_repository_py.html#t278">backend\core\repositories\document_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_document_repository_py.html#t278"><data value='count_by_project_id'>ImportedDataRevisionRepository.count_by_project_id</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_document_repository_py.html#t321">backend\core\repositories\document_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_document_repository_py.html#t321"><data value='init__'>ExportedDocumentRepository.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_document_repository_py.html#t331">backend\core\repositories\document_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_document_repository_py.html#t331"><data value='get_by_project_id'>ExportedDocumentRepository.get_by_project_id</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_document_repository_py.html#t377">backend\core\repositories\document_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_document_repository_py.html#t377"><data value='get_latest_by_document_type'>ExportedDocumentRepository.get_latest_by_document_type</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_document_repository_py.html#t424">backend\core\repositories\document_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_document_repository_py.html#t424"><data value='get_by_document_type'>ExportedDocumentRepository.get_by_document_type</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_document_repository_py.html#t466">backend\core\repositories\document_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_document_repository_py.html#t466"><data value='mark_others_as_not_latest'>ExportedDocumentRepository.mark_others_as_not_latest</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_document_repository_py.html#t519">backend\core\repositories\document_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_document_repository_py.html#t519"><data value='get_with_related_data'>ExportedDocumentRepository.get_with_related_data</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_document_repository_py.html#t559">backend\core\repositories\document_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_document_repository_py.html#t559"><data value='count_by_project_id'>ExportedDocumentRepository.count_by_project_id</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_document_repository_py.html#t602">backend\core\repositories\document_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_document_repository_py.html#t602"><data value='init__'>CalculationStandardRepository.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_document_repository_py.html#t612">backend\core\repositories\document_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_document_repository_py.html#t612"><data value='get_by_standard_code'>CalculationStandardRepository.get_by_standard_code</data></a></td>
                <td>12</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="8 12">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_document_repository_py.html#t650">backend\core\repositories\document_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_document_repository_py.html#t650"><data value='get_by_name'>CalculationStandardRepository.get_by_name</data></a></td>
                <td>12</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="8 12">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_document_repository_py.html#t688">backend\core\repositories\document_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_document_repository_py.html#t688"><data value='search_by_name_or_code'>CalculationStandardRepository.search_by_name_or_code</data></a></td>
                <td>11</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="7 11">64%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_document_repository_py.html#t732">backend\core\repositories\document_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_document_repository_py.html#t732"><data value='get_all_active'>CalculationStandardRepository.get_all_active</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_document_repository_py.html#t768">backend\core\repositories\document_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_document_repository_py.html#t768"><data value='count_active'>CalculationStandardRepository.count_active</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_document_repository_py.html">backend\core\repositories\document_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_document_repository_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>32</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="32 32">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t55">backend\core\repositories\electrical_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t55"><data value='init__'>ElectricalNodeRepository.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t65">backend\core\repositories\electrical_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t65"><data value='get_by_project_id'>ElectricalNodeRepository.get_by_project_id</data></a></td>
                <td>10</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="9 10">90%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t113">backend\core\repositories\electrical_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t113"><data value='get_by_node_type'>ElectricalNodeRepository.get_by_node_type</data></a></td>
                <td>10</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="6 10">60%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t163">backend\core\repositories\electrical_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t163"><data value='get_nodes_with_capacity'>ElectricalNodeRepository.get_nodes_with_capacity</data></a></td>
                <td>10</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="6 10">60%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t210">backend\core\repositories\electrical_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t210"><data value='get_nodes_without_routes'>ElectricalNodeRepository.get_nodes_without_routes</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t252">backend\core\repositories\electrical_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t252"><data value='count_by_project'>ElectricalNodeRepository.count_by_project</data></a></td>
                <td>10</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="6 10">60%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t285">backend\core\repositories\electrical_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t285"><data value='update'>ElectricalNodeRepository.update</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t328">backend\core\repositories\electrical_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t328"><data value='soft_delete'>ElectricalNodeRepository.soft_delete</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t384">backend\core\repositories\electrical_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t384"><data value='init__'>CableRouteRepository.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t394">backend\core\repositories\electrical_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t394"><data value='get_by_project_id'>CableRouteRepository.get_by_project_id</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t442">backend\core\repositories\electrical_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t442"><data value='get_routes_by_node'>CableRouteRepository.get_routes_by_node</data></a></td>
                <td>17</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="11 17">65%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t493">backend\core\repositories\electrical_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t493"><data value='get_routes_by_installation_method'>CableRouteRepository.get_routes_by_installation_method</data></a></td>
                <td>10</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="6 10">60%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t539">backend\core\repositories\electrical_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t539"><data value='get_routes_with_high_voltage_drop'>CableRouteRepository.get_routes_with_high_voltage_drop</data></a></td>
                <td>10</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="6 10">60%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t588">backend\core\repositories\electrical_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t588"><data value='count_by_project'>CableRouteRepository.count_by_project</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t621">backend\core\repositories\electrical_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t621"><data value='update'>CableRouteRepository.update</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t666">backend\core\repositories\electrical_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t666"><data value='soft_delete'>CableRouteRepository.soft_delete</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t722">backend\core\repositories\electrical_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t722"><data value='init__'>CableSegmentRepository.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t732">backend\core\repositories\electrical_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t732"><data value='get_by_cable_route_id'>CableSegmentRepository.get_by_cable_route_id</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t780">backend\core\repositories\electrical_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t780"><data value='get_by_project_id'>CableSegmentRepository.get_by_project_id</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t828">backend\core\repositories\electrical_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t828"><data value='get_segments_by_installation_method'>CableSegmentRepository.get_segments_by_installation_method</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t874">backend\core\repositories\electrical_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t874"><data value='count_by_cable_route'>CableSegmentRepository.count_by_cable_route</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t917">backend\core\repositories\electrical_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t917"><data value='init__'>LoadCalculationRepository.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t927">backend\core\repositories\electrical_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t927"><data value='get_by_project_id'>LoadCalculationRepository.get_by_project_id</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t975">backend\core\repositories\electrical_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t975"><data value='get_by_electrical_node_id'>LoadCalculationRepository.get_by_electrical_node_id</data></a></td>
                <td>10</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="6 10">60%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t1023">backend\core\repositories\electrical_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t1023"><data value='get_by_load_type'>LoadCalculationRepository.get_by_load_type</data></a></td>
                <td>10</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="6 10">60%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t1073">backend\core\repositories\electrical_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t1073"><data value='get_total_power_by_node'>LoadCalculationRepository.get_total_power_by_node</data></a></td>
                <td>11</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="7 11">64%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t1122">backend\core\repositories\electrical_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t1122"><data value='init__'>VoltageDropCalculationRepository.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t1132">backend\core\repositories\electrical_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t1132"><data value='get_by_project_id'>VoltageDropCalculationRepository.get_by_project_id</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t1180">backend\core\repositories\electrical_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t1180"><data value='get_by_cable_route_id'>VoltageDropCalculationRepository.get_by_cable_route_id</data></a></td>
                <td>10</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="6 10">60%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t1228">backend\core\repositories\electrical_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t1228"><data value='get_non_compliant_calculations'>VoltageDropCalculationRepository.get_non_compliant_calculations</data></a></td>
                <td>10</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="6 10">60%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t1277">backend\core\repositories\electrical_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t1277"><data value='get_calculations_by_method'>VoltageDropCalculationRepository.get_calculations_by_method</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t1327">backend\core\repositories\electrical_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t1327"><data value='get_average_voltage_drop_by_project'>VoltageDropCalculationRepository.get_average_voltage_drop_by_project</data></a></td>
                <td>11</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="7 11">64%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t1365">backend\core\repositories\electrical_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t1365"><data value='count_by_compliance_status'>VoltageDropCalculationRepository.count_by_compliance_status</data></a></td>
                <td>11</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="7 11">64%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html">backend\core\repositories\electrical_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>54</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="47 54">87%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html#t53">backend\core\repositories\heat_tracing_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html#t53"><data value='init__'>PipeRepository.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html#t63">backend\core\repositories\heat_tracing_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html#t63"><data value='get_by_project_id'>PipeRepository.get_by_project_id</data></a></td>
                <td>10</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="6 10">60%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html#t109">backend\core\repositories\heat_tracing_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html#t109"><data value='get_by_line_tag'>PipeRepository.get_by_line_tag</data></a></td>
                <td>12</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="8 12">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html#t149">backend\core\repositories\heat_tracing_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html#t149"><data value='get_pipes_without_circuits'>PipeRepository.get_pipes_without_circuits</data></a></td>
                <td>10</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="6 10">60%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html#t186">backend\core\repositories\heat_tracing_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html#t186"><data value='get_with_heat_loss_calculations'>PipeRepository.get_with_heat_loss_calculations</data></a></td>
                <td>10</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="6 10">60%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html#t225">backend\core\repositories\heat_tracing_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html#t225"><data value='update_heat_loss_calculation'>PipeRepository.update_heat_loss_calculation</data></a></td>
                <td>14</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="11 14">79%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html#t273">backend\core\repositories\heat_tracing_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html#t273"><data value='count_by_project'>PipeRepository.count_by_project</data></a></td>
                <td>10</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="6 10">60%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html#t313">backend\core\repositories\heat_tracing_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html#t313"><data value='init__'>VesselRepository.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html#t323">backend\core\repositories\heat_tracing_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html#t323"><data value='get_by_project_id'>VesselRepository.get_by_project_id</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html#t369">backend\core\repositories\heat_tracing_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html#t369"><data value='get_by_equipment_tag'>VesselRepository.get_by_equipment_tag</data></a></td>
                <td>12</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="7 12">58%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html#t413">backend\core\repositories\heat_tracing_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html#t413"><data value='get_vessels_without_circuits'>VesselRepository.get_vessels_without_circuits</data></a></td>
                <td>10</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="6 10">60%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html#t450">backend\core\repositories\heat_tracing_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html#t450"><data value='update_heat_loss_calculation'>VesselRepository.update_heat_loss_calculation</data></a></td>
                <td>14</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="9 14">64%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html#t500">backend\core\repositories\heat_tracing_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html#t500"><data value='count_by_project'>VesselRepository.count_by_project</data></a></td>
                <td>10</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="6 10">60%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html#t542">backend\core\repositories\heat_tracing_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html#t542"><data value='init__'>HTCircuitRepository.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html#t552">backend\core\repositories\heat_tracing_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html#t552"><data value='get_by_feeder_id'>HTCircuitRepository.get_by_feeder_id</data></a></td>
                <td>10</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="6 10">60%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html#t598">backend\core\repositories\heat_tracing_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html#t598"><data value='get_by_pipe_id'>HTCircuitRepository.get_by_pipe_id</data></a></td>
                <td>12</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="7 12">58%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html#t633">backend\core\repositories\heat_tracing_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html#t633"><data value='get_by_vessel_id'>HTCircuitRepository.get_by_vessel_id</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html#t668">backend\core\repositories\heat_tracing_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html#t668"><data value='get_with_calculations'>HTCircuitRepository.get_with_calculations</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html#t707">backend\core\repositories\heat_tracing_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html#t707"><data value='update_load_calculation'>HTCircuitRepository.update_load_calculation</data></a></td>
                <td>14</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="9 14">64%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html#t763">backend\core\repositories\heat_tracing_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html#t763"><data value='get_total_feeder_load'>HTCircuitRepository.get_total_feeder_load</data></a></td>
                <td>11</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="7 11">64%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html#t806">backend\core\repositories\heat_tracing_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html#t806"><data value='init__'>ControlCircuitRepository.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html#t816">backend\core\repositories\heat_tracing_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html#t816"><data value='get_by_switchboard_id'>ControlCircuitRepository.get_by_switchboard_id</data></a></td>
                <td>10</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="6 10">60%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html#t864">backend\core\repositories\heat_tracing_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html#t864"><data value='get_by_type'>ControlCircuitRepository.get_by_type</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html#t911">backend\core\repositories\heat_tracing_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html#t911"><data value='get_with_limiting_function'>ControlCircuitRepository.get_with_limiting_function</data></a></td>
                <td>13</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="9 13">69%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html#t954">backend\core\repositories\heat_tracing_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html#t954"><data value='count_by_switchboard'>ControlCircuitRepository.count_by_switchboard</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html#t1004">backend\core\repositories\heat_tracing_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html#t1004"><data value='init__'>HeatTracingRepository.__init__</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html#t1018">backend\core\repositories\heat_tracing_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html#t1018"><data value='get_project_summary'>HeatTracingRepository.get_project_summary</data></a></td>
                <td>8</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="5 8">62%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html#t1059">backend\core\repositories\heat_tracing_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html#t1059"><data value='get_design_readiness'>HeatTracingRepository.get_design_readiness</data></a></td>
                <td>17</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="10 17">59%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html">backend\core\repositories\heat_tracing_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>49</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="42 49">86%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_project_repository_py.html#t32">backend\core\repositories\project_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_project_repository_py.html#t32"><data value='init__'>ProjectRepository.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_project_repository_py.html#t42">backend\core\repositories\project_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_project_repository_py.html#t42"><data value='get_by_code'>ProjectRepository.get_by_code</data></a></td>
                <td>15</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="11 15">73%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_project_repository_py.html#t76">backend\core\repositories\project_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_project_repository_py.html#t76"><data value='get_by_name'>ProjectRepository.get_by_name</data></a></td>
                <td>12</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="8 12">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_project_repository_py.html#t107">backend\core\repositories\project_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_project_repository_py.html#t107"><data value='get_active_projects'>ProjectRepository.get_active_projects</data></a></td>
                <td>10</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="6 10">60%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_project_repository_py.html#t141">backend\core\repositories\project_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_project_repository_py.html#t141"><data value='update_project'>ProjectRepository.update_project</data></a></td>
                <td>14</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="11 14">79%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_project_repository_py.html#t185">backend\core\repositories\project_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_project_repository_py.html#t185"><data value='soft_delete_project'>ProjectRepository.soft_delete_project</data></a></td>
                <td>15</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="11 15">73%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_project_repository_py.html#t234">backend\core\repositories\project_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_project_repository_py.html#t234"><data value='get_project_with_related_data'>ProjectRepository.get_project_with_related_data</data></a></td>
                <td>11</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="8 11">73%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_project_repository_py.html#t278">backend\core\repositories\project_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_project_repository_py.html#t278"><data value='search_projects'>ProjectRepository.search_projects</data></a></td>
                <td>11</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="7 11">64%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_project_repository_py.html#t326">backend\core\repositories\project_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_project_repository_py.html#t326"><data value='count_active_projects'>ProjectRepository.count_active_projects</data></a></td>
                <td>11</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="7 11">64%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_project_repository_py.html">backend\core\repositories\project_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_project_repository_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>19</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="19 19">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_switchboard_repository_py.html#t53">backend\core\repositories\switchboard_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_switchboard_repository_py.html#t53"><data value='init__'>SwitchboardRepository.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_switchboard_repository_py.html#t63">backend\core\repositories\switchboard_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_switchboard_repository_py.html#t63"><data value='get_by_project_id'>SwitchboardRepository.get_by_project_id</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_switchboard_repository_py.html#t111">backend\core\repositories\switchboard_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_switchboard_repository_py.html#t111"><data value='get_by_voltage_level'>SwitchboardRepository.get_by_voltage_level</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_switchboard_repository_py.html#t161">backend\core\repositories\switchboard_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_switchboard_repository_py.html#t161"><data value='get_by_type'>SwitchboardRepository.get_by_type</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_switchboard_repository_py.html#t211">backend\core\repositories\switchboard_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_switchboard_repository_py.html#t211"><data value='count_by_project'>SwitchboardRepository.count_by_project</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_switchboard_repository_py.html#t244">backend\core\repositories\switchboard_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_switchboard_repository_py.html#t244"><data value='update'>SwitchboardRepository.update</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_switchboard_repository_py.html#t289">backend\core\repositories\switchboard_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_switchboard_repository_py.html#t289"><data value='soft_delete'>SwitchboardRepository.soft_delete</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_switchboard_repository_py.html#t353">backend\core\repositories\switchboard_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_switchboard_repository_py.html#t353"><data value='init__'>FeederRepository.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_switchboard_repository_py.html#t363">backend\core\repositories\switchboard_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_switchboard_repository_py.html#t363"><data value='get_by_switchboard_id'>FeederRepository.get_by_switchboard_id</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_switchboard_repository_py.html#t411">backend\core\repositories\switchboard_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_switchboard_repository_py.html#t411"><data value='count_by_switchboard'>FeederRepository.count_by_switchboard</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_switchboard_repository_py.html#t454">backend\core\repositories\switchboard_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_switchboard_repository_py.html#t454"><data value='init__'>SwitchboardComponentRepository.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_switchboard_repository_py.html#t464">backend\core\repositories\switchboard_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_switchboard_repository_py.html#t464"><data value='get_by_switchboard_id'>SwitchboardComponentRepository.get_by_switchboard_id</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_switchboard_repository_py.html#t507">backend\core\repositories\switchboard_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_switchboard_repository_py.html#t507"><data value='get_by_component_id'>SwitchboardComponentRepository.get_by_component_id</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_switchboard_repository_py.html#t550">backend\core\repositories\switchboard_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_switchboard_repository_py.html#t550"><data value='count_by_switchboard'>SwitchboardComponentRepository.count_by_switchboard</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_switchboard_repository_py.html#t590">backend\core\repositories\switchboard_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_switchboard_repository_py.html#t590"><data value='init__'>FeederComponentRepository.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_switchboard_repository_py.html#t600">backend\core\repositories\switchboard_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_switchboard_repository_py.html#t600"><data value='get_by_feeder_id'>FeederComponentRepository.get_by_feeder_id</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_switchboard_repository_py.html#t641">backend\core\repositories\switchboard_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_switchboard_repository_py.html#t641"><data value='count_by_feeder'>FeederComponentRepository.count_by_feeder</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_switchboard_repository_py.html">backend\core\repositories\switchboard_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_switchboard_repository_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>37</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="30 37">81%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_user_repository_py.html#t43">backend\core\repositories\user_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_user_repository_py.html#t43"><data value='init__'>UserRepository.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_user_repository_py.html#t53">backend\core\repositories\user_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_user_repository_py.html#t53"><data value='get_by_email'>UserRepository.get_by_email</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_user_repository_py.html#t89">backend\core\repositories\user_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_user_repository_py.html#t89"><data value='get_by_name'>UserRepository.get_by_name</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_user_repository_py.html#t125">backend\core\repositories\user_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_user_repository_py.html#t125"><data value='get_active_users'>UserRepository.get_active_users</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_user_repository_py.html#t159">backend\core\repositories\user_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_user_repository_py.html#t159"><data value='search_users'>UserRepository.search_users</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_user_repository_py.html#t207">backend\core\repositories\user_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_user_repository_py.html#t207"><data value='count_active_users'>UserRepository.count_active_users</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_user_repository_py.html#t231">backend\core\repositories\user_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_user_repository_py.html#t231"><data value='update_password'>UserRepository.update_password</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_user_repository_py.html#t268">backend\core\repositories\user_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_user_repository_py.html#t268"><data value='deactivate_user'>UserRepository.deactivate_user</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_user_repository_py.html#t313">backend\core\repositories\user_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_user_repository_py.html#t313"><data value='init__'>UserPreferenceRepository.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_user_repository_py.html#t323">backend\core\repositories\user_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_user_repository_py.html#t323"><data value='get_by_user_id'>UserPreferenceRepository.get_by_user_id</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_user_repository_py.html#t361">backend\core\repositories\user_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_user_repository_py.html#t361"><data value='create_or_update_preferences'>UserPreferenceRepository.create_or_update_preferences</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_user_repository_py.html#t412">backend\core\repositories\user_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_user_repository_py.html#t412"><data value='soft_delete_preferences'>UserPreferenceRepository.soft_delete_preferences</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_user_repository_py.html">backend\core\repositories\user_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_user_repository_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>30</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="23 30">77%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_activity_log_schemas_py.html#t120">backend\core\schemas\activity_log_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_activity_log_schemas_py.html#t120"><data value='validate_details'>ActivityLogBaseSchema.validate_details</data></a></td>
                <td>8</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="7 8">88%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_activity_log_schemas_py.html#t137">backend\core\schemas\activity_log_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_activity_log_schemas_py.html#t137"><data value='validate_entity_consistency'>ActivityLogBaseSchema.validate_entity_consistency</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_activity_log_schemas_py.html#t257">backend\core\schemas\activity_log_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_activity_log_schemas_py.html#t257"><data value='validate_date_range'>ActivityLogFilterSchema.validate_date_range</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_activity_log_schemas_py.html#t313">backend\core\schemas\activity_log_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_activity_log_schemas_py.html#t313"><data value='validate_report_type'>AuditReportRequestSchema.validate_report_type</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_activity_log_schemas_py.html#t327">backend\core\schemas\activity_log_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_activity_log_schemas_py.html#t327"><data value='validate_date_range'>AuditReportRequestSchema.validate_date_range</data></a></td>
                <td>7</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="6 7">86%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_activity_log_schemas_py.html#t428">backend\core\schemas\activity_log_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_activity_log_schemas_py.html#t428"><data value='validate_severity'>SecurityEventSchema.validate_severity</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_activity_log_schemas_py.html#t439">backend\core\schemas\activity_log_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_activity_log_schemas_py.html#t439"><data value='validate_threat_level'>SecurityEventSchema.validate_threat_level</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_activity_log_schemas_py.html#t505">backend\core\schemas\activity_log_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_activity_log_schemas_py.html#t505"><data value='get_event_category'>EventCategoryMappingSchema.get_event_category</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_activity_log_schemas_py.html">backend\core\schemas\activity_log_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_activity_log_schemas_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>146</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="146 146">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_base_py.html#t63">backend\core\schemas\base.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_base_py.html#t63"><data value='create_response'>PaginatedResponseSchema.create_response</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_base_py.html">backend\core\schemas\base.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_base_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>39</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="39 39">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_component_schemas_py.html#t27">backend\core\schemas\component_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_component_schemas_py.html#t27"><data value='validate_name'>ComponentCategoryBaseSchema.validate_name</data></a></td>
                <td>6</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="5 6">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_component_schemas_py.html#t125">backend\core\schemas\component_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_component_schemas_py.html#t125"><data value='validate_name'>ComponentBaseSchema.validate_name</data></a></td>
                <td>6</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="5 6">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_component_schemas_py.html#t140">backend\core\schemas\component_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_component_schemas_py.html#t140"><data value='validate_specific_data'>ComponentBaseSchema.validate_specific_data</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_component_schemas_py.html">backend\core\schemas\component_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_component_schemas_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>63</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="63 63">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_document_schemas_py.html#t77">backend\core\schemas\document_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_document_schemas_py.html#t77"><data value='validate_filename'>ImportedDataRevisionBaseSchema.validate_filename</data></a></td>
                <td>6</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="5 6">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_document_schemas_py.html#t93">backend\core\schemas\document_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_document_schemas_py.html#t93"><data value='validate_revision_identifier'>ImportedDataRevisionBaseSchema.validate_revision_identifier</data></a></td>
                <td>7</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="6 7">86%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_document_schemas_py.html#t222">backend\core\schemas\document_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_document_schemas_py.html#t222"><data value='validate_filename'>ExportedDocumentBaseSchema.validate_filename</data></a></td>
                <td>6</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="5 6">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_document_schemas_py.html#t344">backend\core\schemas\document_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_document_schemas_py.html#t344"><data value='validate_name'>CalculationStandardBaseSchema.validate_name</data></a></td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_document_schemas_py.html#t352">backend\core\schemas\document_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_document_schemas_py.html#t352"><data value='validate_standard_code'>CalculationStandardBaseSchema.validate_standard_code</data></a></td>
                <td>6</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="5 6">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_document_schemas_py.html#t369">backend\core\schemas\document_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_document_schemas_py.html#t369"><data value='validate_parameters_json'>CalculationStandardBaseSchema.validate_parameters_json</data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_document_schemas_py.html#t477">backend\core\schemas\document_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_document_schemas_py.html#t477"><data value='validate_content_type'>FileUploadSchema.validate_content_type</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_document_schemas_py.html">backend\core\schemas\document_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_document_schemas_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>146</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="146 146">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t212">backend\core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t212"><data value='validate_name'>ElectricalNodeBaseSchema.validate_name</data></a></td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t366">backend\core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t366"><data value='validate_name'>CableRouteBaseSchema.validate_name</data></a></td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t373">backend\core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t373"><data value='validate_temperature_range'>CableRouteBaseSchema.validate_temperature_range</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t395">backend\core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t395"><data value='validate_different_nodes'>CableRouteCreateSchema.validate_different_nodes</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t542">backend\core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t542"><data value='validate_name'>CableSegmentBaseSchema.validate_name</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t722">backend\core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t722"><data value='validate_name'>LoadCalculationBaseSchema.validate_name</data></a></td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t729">backend\core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t729"><data value='validate_electrical_parameters'>LoadCalculationBaseSchema.validate_electrical_parameters</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t935">backend\core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t935"><data value='validate_name'>VoltageDropCalculationBaseSchema.validate_name</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t1134">backend\core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t1134"><data value='validate_node_or_route_ids'>ElectricalDesignInputSchema.validate_node_or_route_ids</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html">backend\core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>379</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="379 379">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_error_py.html">backend\core\schemas\error.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_error_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>18</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="18 18">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t55">backend\core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t55"><data value='validate_fluid_temp'>HeatLossCalculationInputSchema.validate_fluid_temp</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t155">backend\core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t155"><data value='validate_name'>PipeBaseSchema.validate_name</data></a></td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t163">backend\core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t163"><data value='validate_outer_diameter'>PipeBaseSchema.validate_outer_diameter</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t355">backend\core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t355"><data value='validate_name'>VesselBaseSchema.validate_name</data></a></td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t363">backend\core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t363"><data value='validate_dimensions_json'>VesselBaseSchema.validate_dimensions_json</data></a></td>
                <td>11</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="10 11">91%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t538">backend\core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t538"><data value='validate_name'>ControlCircuitBaseSchema.validate_name</data></a></td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t546">backend\core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t546"><data value='validate_limiting_setpoint'>ControlCircuitBaseSchema.validate_limiting_setpoint</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t682">backend\core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t682"><data value='validate_name'>HTCircuitBaseSchema.validate_name</data></a></td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t703">backend\core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t703"><data value='validate_pipe_or_vessel'>HTCircuitCreateSchema.validate_pipe_or_vessel</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t873">backend\core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t873"><data value='validate_pipe_or_vessel_ids'>HeatTracingDesignInputSchema.validate_pipe_or_vessel_ids</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html">backend\core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>283</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="283 283">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_project_schemas_py.html#t51">backend\core\schemas\project_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_project_schemas_py.html#t51"><data value='validate_name'>ProjectBaseSchema.validate_name</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_project_schemas_py.html#t59">backend\core\schemas\project_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_project_schemas_py.html#t59"><data value='validate_project_number'>ProjectBaseSchema.validate_project_number</data></a></td>
                <td>6</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="5 6">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_project_schemas_py.html#t104">backend\core\schemas\project_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_project_schemas_py.html#t104"><data value='validate_temp_range'>ProjectEnvironmentalSchema.validate_temp_range</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_project_schemas_py.html#t134">backend\core\schemas\project_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_project_schemas_py.html#t134"><data value='validate_voltages_json'>ProjectDefaultsSchema.validate_voltages_json</data></a></td>
                <td>13</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="12 13">92%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_project_schemas_py.html">backend\core\schemas\project_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_project_schemas_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>76</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="76 76">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_switchboard_schemas_py.html#t47">backend\core\schemas\switchboard_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_switchboard_schemas_py.html#t47"><data value='validate_name'>SwitchboardBaseSchema.validate_name</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_switchboard_schemas_py.html#t55">backend\core\schemas\switchboard_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_switchboard_schemas_py.html#t55"><data value='validate_phases'>SwitchboardBaseSchema.validate_phases</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_switchboard_schemas_py.html#t161">backend\core\schemas\switchboard_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_switchboard_schemas_py.html#t161"><data value='validate_name'>FeederBaseSchema.validate_name</data></a></td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_switchboard_schemas_py.html#t241">backend\core\schemas\switchboard_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_switchboard_schemas_py.html#t241"><data value='validate_position'>SwitchboardComponentBaseSchema.validate_position</data></a></td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_switchboard_schemas_py.html#t330">backend\core\schemas\switchboard_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_switchboard_schemas_py.html#t330"><data value='validate_position'>FeederComponentBaseSchema.validate_position</data></a></td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_switchboard_schemas_py.html">backend\core\schemas\switchboard_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_switchboard_schemas_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>141</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="141 141">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_user_schemas_py.html#t40">backend\core\schemas\user_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_user_schemas_py.html#t40"><data value='validate_name'>UserBaseSchema.validate_name</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_user_schemas_py.html#t56">backend\core\schemas\user_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_user_schemas_py.html#t56"><data value='validate_password'>UserCreateSchema.validate_password</data></a></td>
                <td>8</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="7 8">88%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_user_schemas_py.html#t169">backend\core\schemas\user_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_user_schemas_py.html#t169"><data value='validate_ui_theme'>UserPreferenceBaseSchema.validate_ui_theme</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_user_schemas_py.html#t177">backend\core\schemas\user_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_user_schemas_py.html#t177"><data value='validate_temperature_range'>UserPreferenceBaseSchema.validate_temperature_range</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_user_schemas_py.html#t343">backend\core\schemas\user_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_user_schemas_py.html#t343"><data value='validate_new_password'>PasswordChangeRequestSchema.validate_new_password</data></a></td>
                <td>8</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="6 8">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_user_schemas_py.html#t395">backend\core\schemas\user_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_user_schemas_py.html#t395"><data value='validate_new_password'>PasswordResetConfirmSchema.validate_new_password</data></a></td>
                <td>8</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="6 8">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_user_schemas_py.html">backend\core\schemas\user_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_user_schemas_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>98</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="98 98">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_activity_log_service_py.html#t85">backend\core\services\activity_log_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_activity_log_service_py.html#t85"><data value='init__'>ActivityLogService.__init__</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_activity_log_service_py.html#t101">backend\core\services\activity_log_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_activity_log_service_py.html#t101"><data value='log_event'>ActivityLogService.log_event</data></a></td>
                <td>20</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="20 20">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_activity_log_service_py.html#t169">backend\core\services\activity_log_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_activity_log_service_py.html#t169"><data value='log_user_action'>ActivityLogService.log_user_action</data></a></td>
                <td>19</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="16 19">84%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_activity_log_service_py.html#t229">backend\core\services\activity_log_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_activity_log_service_py.html#t229"><data value='log_authentication_event'>ActivityLogService.log_authentication_event</data></a></td>
                <td>10</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="8 10">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_activity_log_service_py.html#t276">backend\core\services\activity_log_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_activity_log_service_py.html#t276"><data value='log_system_event'>ActivityLogService.log_system_event</data></a></td>
                <td>13</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="8 13">62%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_activity_log_service_py.html#t318">backend\core\services\activity_log_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_activity_log_service_py.html#t318"><data value='get_activity_log'>ActivityLogService.get_activity_log</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_activity_log_service_py.html#t342">backend\core\services\activity_log_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_activity_log_service_py.html#t342"><data value='get_activity_logs'>ActivityLogService.get_activity_logs</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_activity_log_service_py.html#t392">backend\core\services\activity_log_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_activity_log_service_py.html#t392"><data value='get_user_activity_logs'>ActivityLogService.get_user_activity_logs</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_activity_log_service_py.html#t424">backend\core\services\activity_log_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_activity_log_service_py.html#t424"><data value='get_entity_activity_logs'>ActivityLogService.get_entity_activity_logs</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_activity_log_service_py.html#t457">backend\core\services\activity_log_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_activity_log_service_py.html#t457"><data value='get_recent_activity'>ActivityLogService.get_recent_activity</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_activity_log_service_py.html#t472">backend\core\services\activity_log_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_activity_log_service_py.html#t472"><data value='search_activity_logs'>ActivityLogService.search_activity_logs</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_activity_log_service_py.html#t497">backend\core\services\activity_log_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_activity_log_service_py.html#t497"><data value='get_security_events'>ActivityLogService.get_security_events</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_activity_log_service_py.html#t529">backend\core\services\activity_log_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_activity_log_service_py.html#t529"><data value='log_security_event'>ActivityLogService.log_security_event</data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_activity_log_service_py.html#t590">backend\core\services\activity_log_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_activity_log_service_py.html#t590"><data value='generate_audit_report'>ActivityLogService.generate_audit_report</data></a></td>
                <td>13</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="10 13">77%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_activity_log_service_py.html#t678">backend\core\services\activity_log_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_activity_log_service_py.html#t678"><data value='generate_report_summary'>ActivityLogService._generate_report_summary</data></a></td>
                <td>17</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="17 17">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_activity_log_service_py.html#t734">backend\core\services\activity_log_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_activity_log_service_py.html#t734"><data value='get_user_activity_summary'>ActivityLogService.get_user_activity_summary</data></a></td>
                <td>7</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="6 7">86%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_activity_log_service_py.html#t775">backend\core\services\activity_log_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_activity_log_service_py.html#t775"><data value='update_activity_log'>ActivityLogService.update_activity_log</data></a></td>
                <td>20</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="15 20">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_activity_log_service_py.html#t828">backend\core\services\activity_log_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_activity_log_service_py.html#t828"><data value='delete_old_activity_logs'>ActivityLogService.delete_old_activity_logs</data></a></td>
                <td>15</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="10 15">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_activity_log_service_py.html">backend\core\services\activity_log_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_activity_log_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>39</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="33 39">85%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_component_service_py.html#t47">backend\core\services\component_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_component_service_py.html#t47"><data value='init__'>ComponentService.__init__</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_component_service_py.html#t63">backend\core\services\component_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_component_service_py.html#t63"><data value='create_component'>ComponentService.create_component</data></a></td>
                <td>24</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="20 24">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_component_service_py.html#t142">backend\core\services\component_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_component_service_py.html#t142"><data value='get_component_details'>ComponentService.get_component_details</data></a></td>
                <td>13</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="10 13">77%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_component_service_py.html#t180">backend\core\services\component_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_component_service_py.html#t180"><data value='update_component'>ComponentService.update_component</data></a></td>
                <td>30</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="17 30">57%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_component_service_py.html#t260">backend\core\services\component_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_component_service_py.html#t260"><data value='delete_component'>ComponentService.delete_component</data></a></td>
                <td>19</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="15 19">79%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_component_service_py.html#t311">backend\core\services\component_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_component_service_py.html#t311"><data value='get_components_list'>ComponentService.get_components_list</data></a></td>
                <td>24</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="21 24">88%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_component_service_py.html#t407">backend\core\services\component_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_component_service_py.html#t407"><data value='validate_component_creation'>ComponentService._validate_component_creation</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_component_service_py.html#t439">backend\core\services\component_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_component_service_py.html#t439"><data value='validate_component_update'>ComponentService._validate_component_update</data></a></td>
                <td>10</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="6 10">60%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_component_service_py.html#t493">backend\core\services\component_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_component_service_py.html#t493"><data value='init__'>ComponentCategoryService.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_component_service_py.html#t503">backend\core\services\component_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_component_service_py.html#t503"><data value='create_category'>ComponentCategoryService.create_category</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_component_service_py.html#t570">backend\core\services\component_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_component_service_py.html#t570"><data value='get_category_details'>ComponentCategoryService.get_category_details</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_component_service_py.html#t607">backend\core\services\component_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_component_service_py.html#t607"><data value='get_categories_list'>ComponentCategoryService.get_categories_list</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_component_service_py.html#t694">backend\core\services\component_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_component_service_py.html#t694"><data value='validate_category_creation'>ComponentCategoryService._validate_category_creation</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_component_service_py.html">backend\core\services\component_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_component_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>23</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="23 23">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_document_service_py.html#t65">backend\core\services\document_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_document_service_py.html#t65"><data value='init__'>DocumentService.__init__</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_document_service_py.html#t94">backend\core\services\document_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_document_service_py.html#t94"><data value='create_imported_data_revision'>DocumentService.create_imported_data_revision</data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_document_service_py.html#t183">backend\core\services\document_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_document_service_py.html#t183"><data value='get_imported_data_revision_by_id'>DocumentService.get_imported_data_revision_by_id</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_document_service_py.html#t222">backend\core\services\document_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_document_service_py.html#t222"><data value='get_imported_data_revisions_by_project'>DocumentService.get_imported_data_revisions_by_project</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_document_service_py.html#t284">backend\core\services\document_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_document_service_py.html#t284"><data value='update_imported_data_revision'>DocumentService.update_imported_data_revision</data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_document_service_py.html#t369">backend\core\services\document_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_document_service_py.html#t369"><data value='delete_imported_data_revision'>DocumentService.delete_imported_data_revision</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_document_service_py.html#t428">backend\core\services\document_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_document_service_py.html#t428"><data value='create_exported_document'>DocumentService.create_exported_document</data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_document_service_py.html#t508">backend\core\services\document_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_document_service_py.html#t508"><data value='get_exported_document_by_id'>DocumentService.get_exported_document_by_id</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_document_service_py.html#t547">backend\core\services\document_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_document_service_py.html#t547"><data value='get_exported_documents_by_project'>DocumentService.get_exported_documents_by_project</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_document_service_py.html#t613">backend\core\services\document_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_document_service_py.html#t613"><data value='validate_imported_data_revision_creation'>DocumentService._validate_imported_data_revision_creation</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_document_service_py.html#t639">backend\core\services\document_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_document_service_py.html#t639"><data value='validate_imported_data_revision_update'>DocumentService._validate_imported_data_revision_update</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_document_service_py.html#t672">backend\core\services\document_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_document_service_py.html#t672"><data value='validate_imported_data_revision_deletion'>DocumentService._validate_imported_data_revision_deletion</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_document_service_py.html#t700">backend\core\services\document_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_document_service_py.html#t700"><data value='validate_exported_document_creation'>DocumentService._validate_exported_document_creation</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_document_service_py.html#t721">backend\core\services\document_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_document_service_py.html#t721"><data value='create_calculation_standard'>DocumentService.create_calculation_standard</data></a></td>
                <td>20</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="9 20">45%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_document_service_py.html#t783">backend\core\services\document_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_document_service_py.html#t783"><data value='get_calculation_standard_by_id'>DocumentService.get_calculation_standard_by_id</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_document_service_py.html#t822">backend\core\services\document_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_document_service_py.html#t822"><data value='get_calculation_standard_by_code'>DocumentService.get_calculation_standard_by_code</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_document_service_py.html#t863">backend\core\services\document_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_document_service_py.html#t863"><data value='get_all_calculation_standards'>DocumentService.get_all_calculation_standards</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_document_service_py.html#t905">backend\core\services\document_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_document_service_py.html#t905"><data value='update_calculation_standard'>DocumentService.update_calculation_standard</data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_document_service_py.html#t976">backend\core\services\document_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_document_service_py.html#t976"><data value='delete_calculation_standard'>DocumentService.delete_calculation_standard</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_document_service_py.html#t1027">backend\core\services\document_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_document_service_py.html#t1027"><data value='validate_calculation_standard_creation'>DocumentService._validate_calculation_standard_creation</data></a></td>
                <td>6</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="4 6">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_document_service_py.html#t1061">backend\core\services\document_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_document_service_py.html#t1061"><data value='validate_calculation_standard_update'>DocumentService._validate_calculation_standard_update</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_document_service_py.html">backend\core\services\document_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_document_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>36</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="36 36">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_electrical_service_py.html#t133">backend\core\services\electrical_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_electrical_service_py.html#t133"><data value='init__'>ElectricalService.__init__</data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_electrical_service_py.html#t150">backend\core\services\electrical_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_electrical_service_py.html#t150"><data value='perform_cable_sizing_calculation'>ElectricalService.perform_cable_sizing_calculation</data></a></td>
                <td>12</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="11 12">92%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_electrical_service_py.html#t219">backend\core\services\electrical_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_electrical_service_py.html#t219"><data value='perform_voltage_drop_calculation'>ElectricalService.perform_voltage_drop_calculation</data></a></td>
                <td>17</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="16 17">94%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_electrical_service_py.html#t314">backend\core\services\electrical_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_electrical_service_py.html#t314"><data value='validate_electrical_standards'>ElectricalService.validate_electrical_standards</data></a></td>
                <td>17</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="12 17">71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_electrical_service_py.html#t391">backend\core\services\electrical_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_electrical_service_py.html#t391"><data value='generate_recommendations'>ElectricalService._generate_recommendations</data></a></td>
                <td>19</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="17 19">89%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_electrical_service_py.html#t440">backend\core\services\electrical_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_electrical_service_py.html#t440"><data value='calculate_load_for_electrical_node'>ElectricalService.calculate_load_for_electrical_node</data></a></td>
                <td>28</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="25 28">89%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_electrical_service_py.html#t562">backend\core\services\electrical_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_electrical_service_py.html#t562"><data value='optimize_cable_route'>ElectricalService.optimize_cable_route</data></a></td>
                <td>28</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="0 28">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_electrical_service_py.html#t702">backend\core\services\electrical_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_electrical_service_py.html#t702"><data value='perform_electrical_design_workflow'>ElectricalService.perform_electrical_design_workflow</data></a></td>
                <td>30</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="0 30">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_electrical_service_py.html">backend\core\services\electrical_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_electrical_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>33</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="21 33">64%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_heat_tracing_service_py.html#t105">backend\core\services\heat_tracing_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_heat_tracing_service_py.html#t105"><data value='init__'>HeatTracingService.__init__</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_heat_tracing_service_py.html#t130">backend\core\services\heat_tracing_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_heat_tracing_service_py.html#t130"><data value='create_pipe'>HeatTracingService.create_pipe</data></a></td>
                <td>17</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="14 17">82%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_heat_tracing_service_py.html#t188">backend\core\services\heat_tracing_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_heat_tracing_service_py.html#t188"><data value='get_pipe_details'>HeatTracingService.get_pipe_details</data></a></td>
                <td>13</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="11 13">85%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_heat_tracing_service_py.html#t226">backend\core\services\heat_tracing_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_heat_tracing_service_py.html#t226"><data value='update_pipe'>HeatTracingService.update_pipe</data></a></td>
                <td>27</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="19 27">70%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_heat_tracing_service_py.html#t293">backend\core\services\heat_tracing_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_heat_tracing_service_py.html#t293"><data value='delete_pipe'>HeatTracingService.delete_pipe</data></a></td>
                <td>18</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="10 18">56%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_heat_tracing_service_py.html#t342">backend\core\services\heat_tracing_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_heat_tracing_service_py.html#t342"><data value='get_pipes_list'>HeatTracingService.get_pipes_list</data></a></td>
                <td>13</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="7 13">54%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_heat_tracing_service_py.html#t404">backend\core\services\heat_tracing_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_heat_tracing_service_py.html#t404"><data value='create_vessel'>HeatTracingService.create_vessel</data></a></td>
                <td>17</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="11 17">65%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_heat_tracing_service_py.html#t463">backend\core\services\heat_tracing_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_heat_tracing_service_py.html#t463"><data value='get_vessel_details'>HeatTracingService.get_vessel_details</data></a></td>
                <td>13</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="8 13">62%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_heat_tracing_service_py.html#t502">backend\core\services\heat_tracing_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_heat_tracing_service_py.html#t502"><data value='get_vessels_list'>HeatTracingService.get_vessels_list</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_heat_tracing_service_py.html#t564">backend\core\services\heat_tracing_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_heat_tracing_service_py.html#t564"><data value='calculate_pipe_heat_loss'>HeatTracingService.calculate_pipe_heat_loss</data></a></td>
                <td>18</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="3 18">17%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_heat_tracing_service_py.html#t629">backend\core\services\heat_tracing_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_heat_tracing_service_py.html#t629"><data value='validate_standards_compliance'>HeatTracingService.validate_standards_compliance</data></a></td>
                <td>10</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="3 10">30%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_heat_tracing_service_py.html#t669">backend\core\services\heat_tracing_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_heat_tracing_service_py.html#t669"><data value='execute_heat_tracing_design'>HeatTracingService.execute_heat_tracing_design</data></a></td>
                <td>45</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="23 45">51%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_heat_tracing_service_py.html#t776">backend\core\services\heat_tracing_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_heat_tracing_service_py.html#t776"><data value='get_project_heat_tracing_summary'>HeatTracingService.get_project_heat_tracing_summary</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_heat_tracing_service_py.html#t801">backend\core\services\heat_tracing_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_heat_tracing_service_py.html#t801"><data value='get_project_design_readiness'>HeatTracingService.get_project_design_readiness</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_heat_tracing_service_py.html#t830">backend\core\services\heat_tracing_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_heat_tracing_service_py.html#t830"><data value='validate_pipe_creation'>HeatTracingService._validate_pipe_creation</data></a></td>
                <td>5</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="3 5">60%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_heat_tracing_service_py.html#t857">backend\core\services\heat_tracing_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_heat_tracing_service_py.html#t857"><data value='validate_pipe_update'>HeatTracingService._validate_pipe_update</data></a></td>
                <td>5</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="3 5">60%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_heat_tracing_service_py.html#t891">backend\core\services\heat_tracing_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_heat_tracing_service_py.html#t891"><data value='validate_vessel_creation'>HeatTracingService._validate_vessel_creation</data></a></td>
                <td>10</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="6 10">60%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_heat_tracing_service_py.html#t924">backend\core\services\heat_tracing_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_heat_tracing_service_py.html#t924"><data value='perform_heat_loss_calculation'>HeatTracingService._perform_heat_loss_calculation</data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_heat_tracing_service_py.html#t970">backend\core\services\heat_tracing_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_heat_tracing_service_py.html#t970"><data value='perform_standards_validation'>HeatTracingService._perform_standards_validation</data></a></td>
                <td>9</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="8 9">89%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_heat_tracing_service_py.html#t1010">backend\core\services\heat_tracing_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_heat_tracing_service_py.html#t1010"><data value='design_pipe_heat_tracing'>HeatTracingService._design_pipe_heat_tracing</data></a></td>
                <td>10</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="9 10">90%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_heat_tracing_service_py.html#t1086">backend\core\services\heat_tracing_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_heat_tracing_service_py.html#t1086"><data value='design_vessel_heat_tracing'>HeatTracingService._design_vessel_heat_tracing</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_heat_tracing_service_py.html#t1167">backend\core\services\heat_tracing_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_heat_tracing_service_py.html#t1167"><data value='generate_design_summary'>HeatTracingService._generate_design_summary</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_heat_tracing_service_py.html">backend\core\services\heat_tracing_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_heat_tracing_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>44</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="34 44">77%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_project_service_py.html#t45">backend\core\services\project_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_project_service_py.html#t45"><data value='init__'>ProjectService.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_project_service_py.html#t57">backend\core\services\project_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_project_service_py.html#t57"><data value='create_project'>ProjectService.create_project</data></a></td>
                <td>26</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="9 26">35%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_project_service_py.html#t139">backend\core\services\project_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_project_service_py.html#t139"><data value='get_project_details'>ProjectService.get_project_details</data></a></td>
                <td>23</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="19 23">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_project_service_py.html#t192">backend\core\services\project_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_project_service_py.html#t192"><data value='update_project'>ProjectService.update_project</data></a></td>
                <td>30</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="15 30">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_project_service_py.html#t274">backend\core\services\project_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_project_service_py.html#t274"><data value='delete_project'>ProjectService.delete_project</data></a></td>
                <td>19</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="10 19">53%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_project_service_py.html#t325">backend\core\services\project_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_project_service_py.html#t325"><data value='get_projects_list'>ProjectService.get_projects_list</data></a></td>
                <td>18</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="15 18">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_project_service_py.html#t394">backend\core\services\project_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_project_service_py.html#t394"><data value='get_project_by_id_or_code'>ProjectService._get_project_by_id_or_code</data></a></td>
                <td>11</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="6 11">55%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_project_service_py.html#t423">backend\core\services\project_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_project_service_py.html#t423"><data value='validate_project_creation'>ProjectService._validate_project_creation</data></a></td>
                <td>4</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="3 4">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_project_service_py.html#t449">backend\core\services\project_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_project_service_py.html#t449"><data value='validate_project_update'>ProjectService._validate_project_update</data></a></td>
                <td>7</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="5 7">71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_project_service_py.html">backend\core\services\project_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_project_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>18</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="18 18">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_switchboard_service_py.html#t96">backend\core\services\switchboard_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_switchboard_service_py.html#t96"><data value='init__'>SwitchboardService.__init__</data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_switchboard_service_py.html#t116">backend\core\services\switchboard_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_switchboard_service_py.html#t116"><data value='create_switchboard'>SwitchboardService.create_switchboard</data></a></td>
                <td>17</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="14 17">82%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_switchboard_service_py.html#t164">backend\core\services\switchboard_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_switchboard_service_py.html#t164"><data value='get_switchboard'>SwitchboardService.get_switchboard</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_switchboard_service_py.html#t188">backend\core\services\switchboard_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_switchboard_service_py.html#t188"><data value='update_switchboard'>SwitchboardService.update_switchboard</data></a></td>
                <td>19</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="15 19">79%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_switchboard_service_py.html#t248">backend\core\services\switchboard_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_switchboard_service_py.html#t248"><data value='delete_switchboard'>SwitchboardService.delete_switchboard</data></a></td>
                <td>15</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="11 15">73%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_switchboard_service_py.html#t293">backend\core\services\switchboard_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_switchboard_service_py.html#t293"><data value='get_switchboards_by_project'>SwitchboardService.get_switchboards_by_project</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_switchboard_service_py.html#t316">backend\core\services\switchboard_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_switchboard_service_py.html#t316"><data value='create_feeder'>SwitchboardService.create_feeder</data></a></td>
                <td>18</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="18 18">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_switchboard_service_py.html#t361">backend\core\services\switchboard_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_switchboard_service_py.html#t361"><data value='get_feeder'>SwitchboardService.get_feeder</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_switchboard_service_py.html#t385">backend\core\services\switchboard_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_switchboard_service_py.html#t385"><data value='get_feeders_by_switchboard'>SwitchboardService.get_feeders_by_switchboard</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_switchboard_service_py.html#t408">backend\core\services\switchboard_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_switchboard_service_py.html#t408"><data value='add_switchboard_component'>SwitchboardService.add_switchboard_component</data></a></td>
                <td>22</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="22 22">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_switchboard_service_py.html#t480">backend\core\services\switchboard_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_switchboard_service_py.html#t480"><data value='get_switchboard_load_summary'>SwitchboardService.get_switchboard_load_summary</data></a></td>
                <td>17</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="13 17">76%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_switchboard_service_py.html#t547">backend\core\services\switchboard_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_switchboard_service_py.html#t547"><data value='get_switchboard_capacity_analysis'>SwitchboardService.get_switchboard_capacity_analysis</data></a></td>
                <td>21</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="14 21">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_switchboard_service_py.html">backend\core\services\switchboard_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_switchboard_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>33</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="23 33">70%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_user_service_py.html#t96">backend\core\services\user_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_user_service_py.html#t96"><data value='init__'>UserService.__init__</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_user_service_py.html#t112">backend\core\services\user_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_user_service_py.html#t112"><data value='create_user'>UserService.create_user</data></a></td>
                <td>21</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="21 21">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_user_service_py.html#t163">backend\core\services\user_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_user_service_py.html#t163"><data value='get_user'>UserService.get_user</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_user_service_py.html#t187">backend\core\services\user_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_user_service_py.html#t187"><data value='get_user_by_email'>UserService.get_user_by_email</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_user_service_py.html#t211">backend\core\services\user_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_user_service_py.html#t211"><data value='update_user'>UserService.update_user</data></a></td>
                <td>21</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="15 21">71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_user_service_py.html#t266">backend\core\services\user_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_user_service_py.html#t266"><data value='deactivate_user'>UserService.deactivate_user</data></a></td>
                <td>15</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="7 15">47%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_user_service_py.html#t304">backend\core\services\user_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_user_service_py.html#t304"><data value='get_users'>UserService.get_users</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_user_service_py.html#t324">backend\core\services\user_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_user_service_py.html#t324"><data value='authenticate_user'>UserService.authenticate_user</data></a></td>
                <td>14</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="7 14">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_user_service_py.html#t365">backend\core\services\user_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_user_service_py.html#t365"><data value='generate_access_token'>UserService.generate_access_token</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_user_service_py.html#t394">backend\core\services\user_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_user_service_py.html#t394"><data value='login'>UserService.login</data></a></td>
                <td>12</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="5 12">42%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_user_service_py.html#t437">backend\core\services\user_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_user_service_py.html#t437"><data value='logout'>UserService.logout</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_user_service_py.html#t473">backend\core\services\user_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_user_service_py.html#t473"><data value='change_password'>UserService.change_password</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_user_service_py.html#t540">backend\core\services\user_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_user_service_py.html#t540"><data value='get_user_preferences'>UserService.get_user_preferences</data></a></td>
                <td>8</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="7 8">88%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_user_service_py.html#t568">backend\core\services\user_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_user_service_py.html#t568"><data value='create_or_update_user_preferences'>UserService.create_or_update_user_preferences</data></a></td>
                <td>18</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="15 18">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_user_service_py.html#t619">backend\core\services\user_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_user_service_py.html#t619"><data value='delete_user_preferences'>UserService.delete_user_preferences</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_user_service_py.html#t674">backend\core\services\user_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_user_service_py.html#t674"><data value='search_users'>UserService.search_users</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_user_service_py.html#t693">backend\core\services\user_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_user_service_py.html#t693"><data value='count_active_users'>UserService.count_active_users</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_user_service_py.html">backend\core\services\user_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_user_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>41</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="32 41">78%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3d2460180cf335ae___init___py.html">backend\core\standards\__init__.py</a></td>
                <td class="name left"><a href="z_3d2460180cf335ae___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b97b1182853e7f12___init___py.html">backend\core\standards\iec_60079_30_1\__init__.py</a></td>
                <td class="name left"><a href="z_b97b1182853e7f12___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b97b1182853e7f12_hazardous_area_compliance_py.html#t58">backend\core\standards\iec_60079_30_1\hazardous_area_compliance.py</a></td>
                <td class="name left"><a href="z_b97b1182853e7f12_hazardous_area_compliance_py.html#t58"><data value='validate_hazardous_area_compliance'>validate_hazardous_area_compliance</data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b97b1182853e7f12_hazardous_area_compliance_py.html#t144">backend\core\standards\iec_60079_30_1\hazardous_area_compliance.py</a></td>
                <td class="name left"><a href="z_b97b1182853e7f12_hazardous_area_compliance_py.html#t144"><data value='validate_temperature_class_for_gas'>_validate_temperature_class_for_gas</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b97b1182853e7f12_hazardous_area_compliance_py.html#t159">backend\core\standards\iec_60079_30_1\hazardous_area_compliance.py</a></td>
                <td class="name left"><a href="z_b97b1182853e7f12_hazardous_area_compliance_py.html#t159"><data value='get_safety_requirements'>_get_safety_requirements</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b97b1182853e7f12_hazardous_area_compliance_py.html#t207">backend\core\standards\iec_60079_30_1\hazardous_area_compliance.py</a></td>
                <td class="name left"><a href="z_b97b1182853e7f12_hazardous_area_compliance_py.html#t207"><data value='get_recommended_protection_types'>get_recommended_protection_types</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b97b1182853e7f12_hazardous_area_compliance_py.html#t216">backend\core\standards\iec_60079_30_1\hazardous_area_compliance.py</a></td>
                <td class="name left"><a href="z_b97b1182853e7f12_hazardous_area_compliance_py.html#t216"><data value='validate_cable_certification'>validate_cable_certification</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b97b1182853e7f12_hazardous_area_compliance_py.html">backend\core\standards\iec_60079_30_1\hazardous_area_compliance.py</a></td>
                <td class="name left"><a href="z_b97b1182853e7f12_hazardous_area_compliance_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b97b1182853e7f12_temperature_class_limits_py.html#t37">backend\core\standards\iec_60079_30_1\temperature_class_limits.py</a></td>
                <td class="name left"><a href="z_b97b1182853e7f12_temperature_class_limits_py.html#t37"><data value='validate_temperature_class'>validate_temperature_class</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b97b1182853e7f12_temperature_class_limits_py.html#t78">backend\core\standards\iec_60079_30_1\temperature_class_limits.py</a></td>
                <td class="name left"><a href="z_b97b1182853e7f12_temperature_class_limits_py.html#t78"><data value='get_temperature_class_for_temperature'>get_temperature_class_for_temperature</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b97b1182853e7f12_temperature_class_limits_py.html#t103">backend\core\standards\iec_60079_30_1\temperature_class_limits.py</a></td>
                <td class="name left"><a href="z_b97b1182853e7f12_temperature_class_limits_py.html#t103"><data value='get_temperature_class_limits'>get_temperature_class_limits</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b97b1182853e7f12_temperature_class_limits_py.html#t115">backend\core\standards\iec_60079_30_1\temperature_class_limits.py</a></td>
                <td class="name left"><a href="z_b97b1182853e7f12_temperature_class_limits_py.html#t115"><data value='validate_temperature_derating'>validate_temperature_derating</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b97b1182853e7f12_temperature_class_limits_py.html">backend\core\standards\iec_60079_30_1\temperature_class_limits.py</a></td>
                <td class="name left"><a href="z_b97b1182853e7f12_temperature_class_limits_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3d2460180cf335ae_standards_manager_py.html#t56">backend\core\standards\standards_manager.py</a></td>
                <td class="name left"><a href="z_3d2460180cf335ae_standards_manager_py.html#t56"><data value='init__'>StandardsManager.__init__</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3d2460180cf335ae_standards_manager_py.html#t62">backend\core\standards\standards_manager.py</a></td>
                <td class="name left"><a href="z_3d2460180cf335ae_standards_manager_py.html#t62"><data value='set_context'>StandardsManager.set_context</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3d2460180cf335ae_standards_manager_py.html#t73">backend\core\standards\standards_manager.py</a></td>
                <td class="name left"><a href="z_3d2460180cf335ae_standards_manager_py.html#t73"><data value='validate_heat_loss_calculation'>StandardsManager.validate_heat_loss_calculation</data></a></td>
                <td>32</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="0 32">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3d2460180cf335ae_standards_manager_py.html#t150">backend\core\standards\standards_manager.py</a></td>
                <td class="name left"><a href="z_3d2460180cf335ae_standards_manager_py.html#t150"><data value='validate_cable_selection'>StandardsManager.validate_cable_selection</data></a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3d2460180cf335ae_standards_manager_py.html#t216">backend\core\standards\standards_manager.py</a></td>
                <td class="name left"><a href="z_3d2460180cf335ae_standards_manager_py.html#t216"><data value='apply_safety_factors'>StandardsManager.apply_safety_factors</data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3d2460180cf335ae_standards_manager_py.html#t275">backend\core\standards\standards_manager.py</a></td>
                <td class="name left"><a href="z_3d2460180cf335ae_standards_manager_py.html#t275"><data value='validate_tr_50410_heat_loss'>StandardsManager._validate_tr_50410_heat_loss</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3d2460180cf335ae_standards_manager_py.html#t285">backend\core\standards\standards_manager.py</a></td>
                <td class="name left"><a href="z_3d2460180cf335ae_standards_manager_py.html#t285"><data value='validate_iec_60079_heat_loss'>StandardsManager._validate_iec_60079_heat_loss</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3d2460180cf335ae_standards_manager_py.html#t311">backend\core\standards\standards_manager.py</a></td>
                <td class="name left"><a href="z_3d2460180cf335ae_standards_manager_py.html#t311"><data value='validate_iec_60079_cable'>StandardsManager._validate_iec_60079_cable</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3d2460180cf335ae_standards_manager_py.html#t342">backend\core\standards\standards_manager.py</a></td>
                <td class="name left"><a href="z_3d2460180cf335ae_standards_manager_py.html#t342"><data value='validate_general_heat_loss'>StandardsManager._validate_general_heat_loss</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3d2460180cf335ae_standards_manager_py.html#t368">backend\core\standards\standards_manager.py</a></td>
                <td class="name left"><a href="z_3d2460180cf335ae_standards_manager_py.html#t368"><data value='validate_general_electrical'>StandardsManager._validate_general_electrical</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3d2460180cf335ae_standards_manager_py.html#t396">backend\core\standards\standards_manager.py</a></td>
                <td class="name left"><a href="z_3d2460180cf335ae_standards_manager_py.html#t396"><data value='get_tr_50410_safety_factors'>StandardsManager._get_tr_50410_safety_factors</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3d2460180cf335ae_standards_manager_py.html#t402">backend\core\standards\standards_manager.py</a></td>
                <td class="name left"><a href="z_3d2460180cf335ae_standards_manager_py.html#t402"><data value='get_iec_60079_safety_factors'>StandardsManager._get_iec_60079_safety_factors</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3d2460180cf335ae_standards_manager_py.html#t408">backend\core\standards\standards_manager.py</a></td>
                <td class="name left"><a href="z_3d2460180cf335ae_standards_manager_py.html#t408"><data value='get_general_safety_factors'>StandardsManager._get_general_safety_factors</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3d2460180cf335ae_standards_manager_py.html">backend\core\standards\standards_manager.py</a></td>
                <td class="name left"><a href="z_3d2460180cf335ae_standards_manager_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>40</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="40 40">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e6723df25685444a___init___py.html">backend\core\standards\tr_50410\__init__.py</a></td>
                <td class="name left"><a href="z_e6723df25685444a___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e6723df25685444a_heat_loss_factors_py.html#t32">backend\core\standards\tr_50410\heat_loss_factors.py</a></td>
                <td class="name left"><a href="z_e6723df25685444a_heat_loss_factors_py.html#t32"><data value='validate_tr_50410_heat_loss'>validate_tr_50410_heat_loss</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e6723df25685444a_heat_loss_factors_py.html">backend\core\standards\tr_50410\heat_loss_factors.py</a></td>
                <td class="name left"><a href="z_e6723df25685444a_heat_loss_factors_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>8077</td>
                <td>3399</td>
                <td>0</td>
                <td class="right" data-ratio="4678 8077">58%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.0">coverage.py v7.8.0</a>,
            created at 2025-06-01 22:35 +0300
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>

# backend/tests/test_activity_log_schemas.py
"""
Tests for Activity Log Pydantic schemas.

This module tests the validation, serialization, and deserialization
of Activity Log-related schemas including event logging, filtering,
and audit reporting schemas.
"""

import json
import pytest
from datetime import datetime, timedelta
from pydantic import ValidationError

# Mark all tests in this file
pytestmark = [pytest.mark.unit, pytest.mark.schema, pytest.mark.activity_log]

from core.schemas.activity_log_schemas import (
    ActivityLogBaseSchema,
    ActivityLogCreateSchema,
    ActivityLogFilterSchema,
    ActivityLogReadSchema,
    ActivityLogSummarySchema,
    ActivityLogUpdateSchema,
    AuditReportRequestSchema,
    AuditReportSummarySchema,
    EventCategoryEnum,
    EventCategoryMappingSchema,
    EventTypeEnum,
    EntityTypeEnum,
    SecurityEventSchema,
)


class TestEventEnums:
    """Test event type and entity type enums."""

    def test_event_type_enum_values(self):
        """Test EventTypeEnum has expected values."""
        expected_values = [
            "CREATE",
            "READ",
            "UPDATE",
            "DELETE",
            "LOGIN",
            "LOGOUT",
            "LOGIN_FAILED",
            "PASSWORD_CHANGE",
            "PASSWORD_RESET",
            "SYSTEM_START",
            "SYSTEM_SHUTDOWN",
            "SYSTEM_ERROR",
            "UNAUTHORIZED_ACCESS",
            "PERMISSION_DENIED",
            "SECURITY_VIOLATION",
            "CALCULATION_PERFORMED",
            "REPORT_GENERATED",
            "DATA_IMPORTED",
            "DATA_EXPORTED",
            "FILE_UPLOADED",
            "FILE_DOWNLOADED",
            "FILE_DELETED",
        ]

        for value in expected_values:
            assert hasattr(EventTypeEnum, value)
            assert EventTypeEnum(value).value == value

    def test_entity_type_enum_values(self):
        """Test EntityTypeEnum has expected values."""
        expected_values = [
            "Project",
            "Component",
            "HeatTracing",
            "Electrical",
            "Switchboard",
            "User",
            "UserPreference",
            "Document",
            "ImportedDataRevision",
            "ExportedDocument",
            "CalculationStandard",
            "ActivityLog",
        ]

        # Get all enum values
        actual_values = [e.value for e in EntityTypeEnum]

        # Check that all expected values are present
        for value in expected_values:
            assert value in actual_values
            assert EntityTypeEnum(value).value == value

    def test_event_category_enum_values(self):
        """Test EventCategoryEnum has expected values."""
        expected_values = [
            "AUTHENTICATION",
            "AUTHORIZATION",
            "DATA_MANAGEMENT",
            "CALCULATION",
            "REPORTING",
            "SYSTEM",
            "SECURITY",
            "FILE_MANAGEMENT",
        ]

        for value in expected_values:
            assert hasattr(EventCategoryEnum, value)
            assert EventCategoryEnum(value).value == value


class TestActivityLogBaseSchema:
    """Test ActivityLogBaseSchema validation."""

    def test_valid_activity_log_base(self):
        """Test valid activity log base schema."""
        data = {
            "event_type": EventTypeEnum.CREATE,
            "entity_type": EntityTypeEnum.PROJECT,
            "entity_id": 123,
            "details": "Created new project",
        }

        schema = ActivityLogBaseSchema(**data)
        assert schema.event_type == EventTypeEnum.CREATE
        assert schema.entity_type == EntityTypeEnum.PROJECT
        assert schema.entity_id == 123
        assert schema.details == "Created new project"

    def test_minimal_activity_log_base(self):
        """Test minimal activity log base schema."""
        data = {"event_type": EventTypeEnum.SYSTEM_START}

        schema = ActivityLogBaseSchema(**data)
        assert schema.event_type == EventTypeEnum.SYSTEM_START
        assert schema.entity_type is None
        assert schema.entity_id is None
        assert schema.details is None

    def test_json_details_validation(self):
        """Test JSON details validation."""
        # Valid JSON
        data = {
            "event_type": EventTypeEnum.CREATE,
            "details": '{"project_name": "Test Project", "user": "admin"}',
        }

        schema = ActivityLogBaseSchema(**data)
        assert schema.details == '{"project_name": "Test Project", "user": "admin"}'

    def test_invalid_json_details_validation(self):
        """Test invalid JSON details validation."""
        data = {"event_type": EventTypeEnum.CREATE, "details": '{"invalid": json}'}

        with pytest.raises(ValidationError) as exc_info:
            ActivityLogBaseSchema(**data)

        assert "Details field must contain valid JSON" in str(exc_info.value)

    def test_entity_consistency_validation(self):
        """Test entity type and ID consistency validation."""
        # Valid: both entity_type and entity_id provided
        data = {
            "event_type": EventTypeEnum.UPDATE,
            "entity_type": EntityTypeEnum.USER,
            "entity_id": 456,
        }
        schema = ActivityLogBaseSchema(**data)
        assert schema.entity_type == EntityTypeEnum.USER
        assert schema.entity_id == 456

        # Valid: entity_type without entity_id (system-wide events)
        data = {
            "event_type": EventTypeEnum.SYSTEM_ERROR,
            "entity_type": EntityTypeEnum.PROJECT,
            "entity_id": None,
        }
        schema = ActivityLogBaseSchema(**data)
        assert schema.entity_type == EntityTypeEnum.PROJECT
        assert schema.entity_id is None

        # Invalid: entity_id without entity_type
        data = {
            "event_type": EventTypeEnum.DELETE,
            "entity_type": None,
            "entity_id": 789,
        }

        with pytest.raises(ValidationError) as exc_info:
            ActivityLogBaseSchema(**data)

        assert "entity_type must be provided when entity_id is specified" in str(
            exc_info.value
        )


class TestActivityLogCreateSchema:
    """Test ActivityLogCreateSchema validation."""

    def test_valid_activity_log_create(self):
        """Test valid activity log create schema."""
        data = {
            "user_id": 1,
            "event_type": EventTypeEnum.CREATE,
            "entity_type": EntityTypeEnum.PROJECT,
            "entity_id": 123,
            "details": "Created new heat tracing project",
        }

        schema = ActivityLogCreateSchema(**data)
        assert schema.user_id == 1
        assert schema.event_type == EventTypeEnum.CREATE
        assert schema.entity_type == EntityTypeEnum.PROJECT
        assert schema.entity_id == 123
        assert schema.details == "Created new heat tracing project"

    def test_system_event_without_user(self):
        """Test system event without user ID."""
        data = {
            "event_type": EventTypeEnum.SYSTEM_START,
            "details": "Application started successfully",
        }

        schema = ActivityLogCreateSchema(**data)
        assert schema.user_id is None
        assert schema.event_type == EventTypeEnum.SYSTEM_START


class TestActivityLogFilterSchema:
    """Test ActivityLogFilterSchema validation."""

    def test_valid_filter_schema(self):
        """Test valid filter schema."""
        start_date = datetime.now() - timedelta(days=30)
        end_date = datetime.now()

        data = {
            "user_id": 1,
            "event_type": EventTypeEnum.CREATE,
            "entity_type": EntityTypeEnum.PROJECT,
            "entity_id": 123,
            "start_date": start_date,
            "end_date": end_date,
            "event_category": EventCategoryEnum.DATA_MANAGEMENT,
            "search_details": "project",
        }

        schema = ActivityLogFilterSchema(**data)
        assert schema.user_id == 1
        assert schema.event_type == EventTypeEnum.CREATE
        assert schema.entity_type == EntityTypeEnum.PROJECT
        assert schema.entity_id == 123
        assert schema.start_date == start_date
        assert schema.end_date == end_date
        assert schema.event_category == EventCategoryEnum.DATA_MANAGEMENT
        assert schema.search_details == "project"

    def test_date_range_validation(self):
        """Test date range validation."""
        start_date = datetime.now()
        end_date = datetime.now() - timedelta(days=1)  # End before start

        data = {"start_date": start_date, "end_date": end_date}

        with pytest.raises(ValidationError) as exc_info:
            ActivityLogFilterSchema(**data)

        assert "start_date must be before end_date" in str(exc_info.value)

    def test_minimal_filter_schema(self):
        """Test minimal filter schema."""
        data = {}

        schema = ActivityLogFilterSchema(**data)
        assert schema.user_id is None
        assert schema.event_type is None
        assert schema.entity_type is None
        assert schema.entity_id is None
        assert schema.start_date is None
        assert schema.end_date is None
        assert schema.event_category is None
        assert schema.search_details is None


class TestAuditReportRequestSchema:
    """Test AuditReportRequestSchema validation."""

    def test_valid_audit_report_request(self):
        """Test valid audit report request."""
        start_date = datetime.now() - timedelta(days=30)
        end_date = datetime.now()

        data = {
            "report_type": "user_activity",
            "start_date": start_date,
            "end_date": end_date,
            "user_ids": [1, 2, 3],
            "entity_types": [EntityTypeEnum.PROJECT, EntityTypeEnum.COMPONENT],
            "event_types": [EventTypeEnum.CREATE, EventTypeEnum.UPDATE],
            "event_categories": [EventCategoryEnum.DATA_MANAGEMENT],
            "include_details": True,
            "group_by": "user",
        }

        schema = AuditReportRequestSchema(**data)
        assert schema.report_type == "user_activity"
        assert schema.start_date == start_date
        assert schema.end_date == end_date
        assert schema.user_ids == [1, 2, 3]
        assert schema.entity_types == [EntityTypeEnum.PROJECT, EntityTypeEnum.COMPONENT]
        assert schema.event_types == [EventTypeEnum.CREATE, EventTypeEnum.UPDATE]
        assert schema.event_categories == [EventCategoryEnum.DATA_MANAGEMENT]
        assert schema.include_details is True
        assert schema.group_by == "user"

    def test_invalid_report_type(self):
        """Test invalid report type validation."""
        start_date = datetime.now() - timedelta(days=30)
        end_date = datetime.now()

        data = {
            "report_type": "invalid_type",
            "start_date": start_date,
            "end_date": end_date,
        }

        with pytest.raises(ValidationError) as exc_info:
            AuditReportRequestSchema(**data)

        assert "Report type must be one of" in str(exc_info.value)

    def test_date_range_too_large(self):
        """Test date range validation for large ranges."""
        start_date = datetime.now() - timedelta(days=400)  # More than 365 days
        end_date = datetime.now()

        data = {
            "report_type": "compliance",
            "start_date": start_date,
            "end_date": end_date,
        }

        with pytest.raises(ValidationError) as exc_info:
            AuditReportRequestSchema(**data)

        assert "Report date range cannot exceed 365 days" in str(exc_info.value)


class TestSecurityEventSchema:
    """Test SecurityEventSchema validation."""

    def test_valid_security_event(self):
        """Test valid security event schema."""
        data = {
            "event_id": 1,
            "severity": "HIGH",
            "threat_level": "MEDIUM",
            "source_ip": "*************",
            "user_agent": "Mozilla/5.0...",
            "additional_context": {"failed_attempts": 3, "account_locked": False},
        }

        schema = SecurityEventSchema(**data)
        assert schema.event_id == 1
        assert schema.severity == "HIGH"
        assert schema.threat_level == "MEDIUM"
        assert schema.source_ip == "*************"
        assert schema.user_agent == "Mozilla/5.0..."
        assert schema.additional_context == {
            "failed_attempts": 3,
            "account_locked": False,
        }

    def test_invalid_severity(self):
        """Test invalid severity validation."""
        data = {"event_id": 1, "severity": "INVALID", "threat_level": "LOW"}

        with pytest.raises(ValidationError) as exc_info:
            SecurityEventSchema(**data)

        assert "Severity must be one of" in str(exc_info.value)

    def test_invalid_threat_level(self):
        """Test invalid threat level validation."""
        data = {"event_id": 1, "severity": "LOW", "threat_level": "INVALID"}

        with pytest.raises(ValidationError) as exc_info:
            SecurityEventSchema(**data)

        assert "Threat level must be one of" in str(exc_info.value)


class TestEventCategoryMappingSchema:
    """Test EventCategoryMappingSchema functionality."""

    def test_event_category_mapping(self):
        """Test event type to category mapping."""
        # Test authentication events
        assert (
            EventCategoryMappingSchema.get_event_category(EventTypeEnum.LOGIN)
            == EventCategoryEnum.AUTHENTICATION
        )
        assert (
            EventCategoryMappingSchema.get_event_category(EventTypeEnum.LOGOUT)
            == EventCategoryEnum.AUTHENTICATION
        )
        assert (
            EventCategoryMappingSchema.get_event_category(EventTypeEnum.LOGIN_FAILED)
            == EventCategoryEnum.AUTHENTICATION
        )

        # Test data management events
        assert (
            EventCategoryMappingSchema.get_event_category(EventTypeEnum.CREATE)
            == EventCategoryEnum.DATA_MANAGEMENT
        )
        assert (
            EventCategoryMappingSchema.get_event_category(EventTypeEnum.UPDATE)
            == EventCategoryEnum.DATA_MANAGEMENT
        )
        assert (
            EventCategoryMappingSchema.get_event_category(EventTypeEnum.DELETE)
            == EventCategoryEnum.DATA_MANAGEMENT
        )

        # Test system events
        assert (
            EventCategoryMappingSchema.get_event_category(EventTypeEnum.SYSTEM_START)
            == EventCategoryEnum.SYSTEM
        )
        assert (
            EventCategoryMappingSchema.get_event_category(EventTypeEnum.SYSTEM_ERROR)
            == EventCategoryEnum.SYSTEM
        )

        # Test security events
        assert (
            EventCategoryMappingSchema.get_event_category(
                EventTypeEnum.SECURITY_VIOLATION
            )
            == EventCategoryEnum.SECURITY
        )

        # Test file management events
        assert (
            EventCategoryMappingSchema.get_event_category(EventTypeEnum.FILE_UPLOADED)
            == EventCategoryEnum.FILE_MANAGEMENT
        )

# backend/api/v1/user_routes.py
"""
User API Routes

This module provides REST API endpoints for user operations including:
- Authentication endpoints (login, logout, password management)
- User management CRUD operations
- User preferences and settings management
- Session management and security operations
"""

from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session

try:
    from config.logging_config import get_logger
    from core.database.session import get_db_session
    from core.errors.exceptions import (
        BaseApplicationException,
        BusinessLogicError,
        DatabaseError,
        NotFoundError,
    )
    from core.schemas.user_schemas import (
        LoginRequestSchema,
        LoginResponseSchema,
        LogoutResponseSchema,
        PasswordChangeRequestSchema,
        PasswordResetConfirmSchema,
        PasswordResetRequestSchema,
        UserCreateSchema,
        UserPaginatedResponseSchema,
        UserPreferenceCreateSchema,
        UserPreferenceReadSchema,
        UserPreferenceUpdateSchema,
        UserReadSchema,
        UserSessionSchema,
        UserSummarySchema,
        UserUpdateSchema,
    )
    from core.services.user_service import UserService
except ImportError:
    # For testing and relative imports
    import sys
    import os

    sys.path.insert(
        0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    )
    from config.logging_config import get_logger
    from core.database.session import get_db_session
    from core.errors.exceptions import (
        BaseApplicationException,
        BusinessLogicError,
        DatabaseError,
        NotFoundError,
    )
    from core.schemas.user_schemas import (
        LoginRequestSchema,
        LoginResponseSchema,
        LogoutResponseSchema,
        PasswordChangeRequestSchema,
        PasswordResetConfirmSchema,
        PasswordResetRequestSchema,
        UserCreateSchema,
        UserPaginatedResponseSchema,
        UserPreferenceCreateSchema,
        UserPreferenceReadSchema,
        UserPreferenceUpdateSchema,
        UserReadSchema,
        UserSessionSchema,
        UserSummarySchema,
        UserUpdateSchema,
    )
    from core.services.user_service import UserService

# Initialize logger for this module
logger = get_logger(__name__)

# Create router
router = APIRouter(prefix="/users", tags=["users"])


def handle_user_exceptions(e: Exception) -> HTTPException:
    """
    Handle user-specific exceptions and convert to HTTP responses.

    Args:
        e: Exception to handle

    Returns:
        HTTPException: Appropriate HTTP exception
    """
    if isinstance(e, NotFoundError):
        logger.warning(f"Resource not found: {e.detail}")
        return HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=e.detail,
        )
    elif isinstance(e, BusinessLogicError):
        logger.warning(f"Business logic error: {e.detail}")
        if e.code in ["INVALID_CREDENTIALS", "INVALID_CURRENT_PASSWORD"]:
            return HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail=e.detail,
            )
        elif e.code in ["EMAIL_ALREADY_EXISTS"]:
            return HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail=e.detail,
            )
        else:
            return HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=e.detail,
            )
    elif isinstance(e, DatabaseError):
        logger.error(f"Database error: {e.detail}")
        return HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )
    elif isinstance(e, BaseApplicationException):
        logger.error(f"Application error: {e.detail}")
        return HTTPException(
            status_code=e.status_code,
            detail=e.detail,
        )
    else:
        logger.error(f"Unexpected error: {str(e)}", exc_info=True)
        return HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


# ============================================================================
# AUTHENTICATION ENDPOINTS
# ============================================================================


@router.post(
    "/login",
    response_model=LoginResponseSchema,
    status_code=status.HTTP_200_OK,
    summary="User login",
    description="Authenticate user and return access token",
)
async def login(
    login_data: LoginRequestSchema,
    db: Session = Depends(get_db_session),
) -> LoginResponseSchema:
    """Authenticate user and return access token."""
    logger.info(f"Login attempt for: {login_data.email}")

    try:
        user_service = UserService(db)
        login_response = user_service.login(login_data)

        logger.info(f"Login successful for user: {login_response.user.id}")
        return login_response

    except Exception as e:
        raise handle_user_exceptions(e)


@router.post(
    "/logout",
    response_model=LogoutResponseSchema,
    status_code=status.HTTP_200_OK,
    summary="User logout",
    description="Logout user and invalidate session",
)
async def logout(
    user_id: int = Query(..., description="User ID to logout"),
    db: Session = Depends(get_db_session),
) -> LogoutResponseSchema:
    """Logout user and invalidate session."""
    logger.info(f"Logout request for user: {user_id}")

    try:
        user_service = UserService(db)
        logout_response = user_service.logout(user_id)

        logger.info(f"Logout successful for user: {user_id}")
        return logout_response

    except Exception as e:
        raise handle_user_exceptions(e)


@router.post(
    "/change-password",
    status_code=status.HTTP_200_OK,
    summary="Change password",
    description="Change user password with current password verification",
)
async def change_password(
    user_id: int,
    password_data: PasswordChangeRequestSchema,
    db: Session = Depends(get_db_session),
) -> dict:
    """Change user password."""
    logger.info(f"Password change request for user: {user_id}")

    try:
        user_service = UserService(db)
        success = user_service.change_password(user_id, password_data)

        if success:
            logger.info(f"Password changed successfully for user: {user_id}")
            return {"message": "Password changed successfully"}
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to change password",
            )

    except Exception as e:
        raise handle_user_exceptions(e)


@router.post(
    "/reset-password-request",
    status_code=status.HTTP_200_OK,
    summary="Request password reset",
    description="Request password reset for user email",
)
async def request_password_reset(
    reset_data: PasswordResetRequestSchema,
    db: Session = Depends(get_db_session),
) -> dict:
    """Request password reset for user email."""
    logger.info(f"Password reset request for: {reset_data.email}")

    try:
        # In a real implementation, this would:
        # 1. Verify user exists
        # 2. Generate reset token
        # 3. Send reset email
        # For now, return success message

        logger.info(f"Password reset request processed for: {reset_data.email}")
        return {"message": "Password reset instructions sent to email"}

    except Exception as e:
        raise handle_user_exceptions(e)


@router.post(
    "/reset-password-confirm",
    status_code=status.HTTP_200_OK,
    summary="Confirm password reset",
    description="Confirm password reset with token",
)
async def confirm_password_reset(
    reset_data: PasswordResetConfirmSchema,
    db: Session = Depends(get_db_session),
) -> dict:
    """Confirm password reset with token."""
    logger.info("Password reset confirmation request")

    try:
        # In a real implementation, this would:
        # 1. Verify reset token
        # 2. Update user password
        # 3. Invalidate reset token
        # For now, return success message

        logger.info("Password reset confirmed successfully")
        return {"message": "Password reset successfully"}

    except Exception as e:
        raise handle_user_exceptions(e)


# ============================================================================
# USER MANAGEMENT ENDPOINTS
# ============================================================================


@router.post(
    "",
    response_model=UserReadSchema,
    status_code=status.HTTP_201_CREATED,
    summary="Create user",
    description="Create a new user account",
)
async def create_user(
    user_data: UserCreateSchema,
    db: Session = Depends(get_db_session),
) -> UserReadSchema:
    """Create a new user account."""
    logger.info(f"Creating user: {user_data.name}")

    try:
        user_service = UserService(db)
        user = user_service.create_user(user_data)

        logger.info(f"Created user {user.id}: {user.name}")
        return user

    except Exception as e:
        raise handle_user_exceptions(e)


@router.get(
    "/{user_id}",
    response_model=UserReadSchema,
    summary="Get user",
    description="Retrieve a specific user by ID",
)
async def get_user(
    user_id: int,
    db: Session = Depends(get_db_session),
) -> UserReadSchema:
    """Get a specific user by ID."""
    logger.debug(f"Retrieving user {user_id}")

    try:
        user_service = UserService(db)
        user = user_service.get_user(user_id)

        return user

    except Exception as e:
        raise handle_user_exceptions(e)


@router.get(
    "",
    response_model=UserPaginatedResponseSchema,
    summary="List users",
    description="Retrieve a paginated list of active users",
)
async def list_users(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(
        10, ge=1, le=100, description="Maximum number of records to return"
    ),
    search: Optional[str] = Query(None, description="Search term for name or email"),
    db: Session = Depends(get_db_session),
) -> UserPaginatedResponseSchema:
    """List users with optional search."""
    logger.debug(f"Listing users: skip={skip}, limit={limit}, search={search}")

    try:
        user_service = UserService(db)

        if search:
            users = user_service.search_users(search, skip, limit)
            # For search, we'll use the count from the search results
            # In a real implementation, you'd have a separate count method for search
            total = len(user_service.search_users(search, 0, 1000))  # Get all for count
        else:
            users = user_service.get_users(skip, limit)
            total = user_service.count_active_users()

        user_summaries = [UserSummarySchema.model_validate(user) for user in users]

        return UserPaginatedResponseSchema(
            users=user_summaries,
            total=total,
            page=(skip // limit) + 1,
            per_page=limit,
            total_pages=(total + limit - 1) // limit,
        )

    except Exception as e:
        raise handle_user_exceptions(e)


@router.put(
    "/{user_id}",
    response_model=UserReadSchema,
    summary="Update user",
    description="Update an existing user",
)
async def update_user(
    user_id: int,
    user_data: UserUpdateSchema,
    db: Session = Depends(get_db_session),
) -> UserReadSchema:
    """Update an existing user."""
    logger.info(f"Updating user {user_id}")

    try:
        user_service = UserService(db)
        user = user_service.update_user(user_id, user_data)

        logger.info(f"Updated user {user_id}")
        return user

    except Exception as e:
        raise handle_user_exceptions(e)


@router.delete(
    "/{user_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Deactivate user",
    description="Deactivate a user account",
)
async def deactivate_user(
    user_id: int,
    db: Session = Depends(get_db_session),
) -> None:
    """Deactivate a user account."""
    logger.info(f"Deactivating user {user_id}")

    try:
        user_service = UserService(db)
        user_service.deactivate_user(user_id)

        logger.info(f"Deactivated user {user_id}")

    except Exception as e:
        raise handle_user_exceptions(e)


# ============================================================================
# USER PREFERENCES ENDPOINTS
# ============================================================================


@router.get(
    "/{user_id}/preferences",
    response_model=UserPreferenceReadSchema,
    summary="Get user preferences",
    description="Retrieve user preferences and settings",
)
async def get_user_preferences(
    user_id: int,
    db: Session = Depends(get_db_session),
) -> UserPreferenceReadSchema:
    """Get user preferences and settings."""
    logger.debug(f"Retrieving preferences for user {user_id}")

    try:
        user_service = UserService(db)
        preferences = user_service.get_user_preferences(user_id)

        if not preferences:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User preferences not found",
            )

        return preferences

    except Exception as e:
        raise handle_user_exceptions(e)


@router.put(
    "/{user_id}/preferences",
    response_model=UserPreferenceReadSchema,
    summary="Update user preferences",
    description="Create or update user preferences and settings",
)
async def update_user_preferences(
    user_id: int,
    preferences_data: UserPreferenceUpdateSchema,
    db: Session = Depends(get_db_session),
) -> UserPreferenceReadSchema:
    """Create or update user preferences and settings."""
    logger.info(f"Updating preferences for user {user_id}")

    try:
        user_service = UserService(db)
        preferences = user_service.create_or_update_user_preferences(
            user_id, preferences_data
        )

        logger.info(f"Updated preferences for user {user_id}")
        return preferences

    except Exception as e:
        raise handle_user_exceptions(e)


@router.delete(
    "/{user_id}/preferences",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete user preferences",
    description="Delete user preferences and reset to defaults",
)
async def delete_user_preferences(
    user_id: int,
    deleted_by_user_id: Optional[int] = Query(
        None, description="ID of user performing deletion"
    ),
    db: Session = Depends(get_db_session),
) -> None:
    """Delete user preferences and reset to defaults."""
    logger.info(f"Deleting preferences for user {user_id}")

    try:
        user_service = UserService(db)
        user_service.delete_user_preferences(user_id, deleted_by_user_id)

        logger.info(f"Deleted preferences for user {user_id}")

    except Exception as e:
        raise handle_user_exceptions(e)

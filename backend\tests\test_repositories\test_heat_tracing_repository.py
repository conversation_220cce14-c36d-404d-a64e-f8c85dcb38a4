# backend/tests/test_repositories/test_heat_tracing_repository.py
"""
Tests for Heat Tracing Repository

This module contains comprehensive tests for all heat tracing repository classes including:
- CRUD operation tests
- Query method tests
- Error handling tests
- Transaction management tests
"""

import os
import sys

import pytest

# Mark all tests in this file
pytestmark = [pytest.mark.unit, pytest.mark.repository, pytest.mark.database, pytest.mark.heat_tracing]

# Add the backend directory to the Python path
sys.path.insert(
    0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
)

from core.repositories.heat_tracing_repository import (
    ControlCircuitRepository,
    HeatTracingRepository,
    HTCircuitRepository,
    PipeRepository,
    VesselRepository,
)


class TestPipeRepository:
    """Test PipeRepository class."""

    @pytest.fixture
    def pipe_repository(self, db_session):
        """Create a PipeRepository instance for testing."""
        return PipeRepository(db_session)

    @pytest.fixture
    def sample_pipe_data(self):
        """Sample pipe data for testing."""
        return {
            "name": "Test Pipe 001",
            "project_id": 1,
            "pipe_material_id": 10,
            "insulation_material_id": 15,
            "nominal_diameter_mm": 100.0,
            "wall_thickness_mm": 5.0,
            "outer_diameter_mm": 110.0,
            "length_m": 50.0,
            "insulation_thickness_mm": 50.0,
            "fluid_type": "Process Water",
            "line_tag": "L-001",
        }

    def test_create_pipe_success(self, pipe_repository, sample_pipe_data):
        """Test successful pipe creation."""
        pipe = pipe_repository.create(sample_pipe_data)
        pipe_repository.db_session.commit()

        assert pipe.id is not None
        assert pipe.name == "Test Pipe 001"
        assert pipe.project_id == 1
        assert pipe.length_m == 50.0
        assert pipe.is_deleted is False

    def test_get_pipe_by_id_success(self, pipe_repository, sample_pipe_data):
        """Test successful pipe retrieval by ID."""
        # Create pipe first
        created_pipe = pipe_repository.create(sample_pipe_data)
        pipe_repository.db_session.commit()

        # Retrieve pipe
        retrieved_pipe = pipe_repository.get_by_id(created_pipe.id)

        assert retrieved_pipe is not None
        assert retrieved_pipe.id == created_pipe.id
        assert retrieved_pipe.name == "Test Pipe 001"

    def test_get_pipe_by_id_not_found(self, pipe_repository):
        """Test pipe retrieval with non-existent ID."""
        pipe = pipe_repository.get_by_id(999)
        assert pipe is None

    def test_get_pipes_by_project_id(self, pipe_repository, sample_pipe_data):
        """Test retrieving pipes by project ID."""
        # Create multiple pipes
        pipe1_data = sample_pipe_data.copy()
        pipe1_data["name"] = "Pipe 1"
        pipe1_data["line_tag"] = "L-001"

        pipe2_data = sample_pipe_data.copy()
        pipe2_data["name"] = "Pipe 2"
        pipe2_data["line_tag"] = "L-002"

        pipe_repository.create(pipe1_data)
        pipe_repository.create(pipe2_data)
        pipe_repository.db_session.commit()

        # Retrieve pipes by project
        pipes = pipe_repository.get_by_project_id(1)

        assert len(pipes) == 2
        assert all(pipe.project_id == 1 for pipe in pipes)

    def test_get_pipe_by_line_tag(self, pipe_repository, sample_pipe_data):
        """Test retrieving pipe by line tag."""
        pipe_repository.create(sample_pipe_data)
        pipe_repository.db_session.commit()

        pipe = pipe_repository.get_by_line_tag(1, "L-001")

        assert pipe is not None
        assert pipe.line_tag == "L-001"
        assert pipe.project_id == 1

    def test_get_pipe_by_line_tag_not_found(self, pipe_repository):
        """Test pipe retrieval with non-existent line tag."""
        pipe = pipe_repository.get_by_line_tag(1, "NON-EXISTENT")
        assert pipe is None

    def test_get_pipes_without_circuits(self, pipe_repository, sample_pipe_data):
        """Test retrieving pipes without circuits."""
        pipe_repository.create(sample_pipe_data)
        pipe_repository.db_session.commit()

        pipes = pipe_repository.get_pipes_without_circuits(1)

        assert len(pipes) >= 1
        # All pipes should not have circuits (since we haven't created any)

    def test_update_heat_loss_calculation(self, pipe_repository, sample_pipe_data):
        """Test updating pipe heat loss calculation."""
        pipe = pipe_repository.create(sample_pipe_data)
        pipe_repository.db_session.commit()

        updated_pipe = pipe_repository.update_heat_loss_calculation(
            pipe_id=pipe.id, heat_loss_wm=25.5, required_output_wm=30.6
        )

        assert updated_pipe is not None
        assert updated_pipe.calculated_heat_loss_wm == 25.5
        assert updated_pipe.required_heat_output_wm == 30.6

    def test_update_heat_loss_calculation_not_found(self, pipe_repository):
        """Test updating heat loss calculation for non-existent pipe."""
        result = pipe_repository.update_heat_loss_calculation(
            pipe_id=999, heat_loss_wm=25.5, required_output_wm=30.6
        )

        assert result is None

    def test_count_by_project(self, pipe_repository, sample_pipe_data):
        """Test counting pipes by project."""
        # Initially no pipes
        count = pipe_repository.count_by_project(1)
        assert count == 0

        # Create a pipe
        pipe_repository.create(sample_pipe_data)
        pipe_repository.db_session.commit()

        # Count should be 1
        count = pipe_repository.count_by_project(1)
        assert count == 1

    def test_get_with_heat_loss_calculations(self, pipe_repository, sample_pipe_data):
        """Test retrieving pipes with heat loss calculations."""
        # Create pipe with calculation
        pipe = pipe_repository.create(sample_pipe_data)
        pipe_repository.db_session.commit()

        pipe_repository.update_heat_loss_calculation(
            pipe_id=pipe.id, heat_loss_wm=25.5, required_output_wm=30.6
        )
        pipe_repository.db_session.commit()

        pipes = pipe_repository.get_with_heat_loss_calculations(1)

        assert len(pipes) == 1
        assert pipes[0].calculated_heat_loss_wm == 25.5


class TestVesselRepository:
    """Test VesselRepository class."""

    @pytest.fixture
    def vessel_repository(self, db_session):
        """Create a VesselRepository instance for testing."""
        return VesselRepository(db_session)

    @pytest.fixture
    def sample_vessel_data(self):
        """Sample vessel data for testing."""
        return {
            "name": "Test Vessel T-001",
            "project_id": 1,
            "material_id": 12,
            "insulation_material_id": 15,
            "dimensions_json": '{"type": "cylinder", "diameter": 2.0, "height": 3.0}',
            "surface_area_m2": 25.13,
            "insulation_thickness_mm": 75.0,
            "equipment_tag": "T-001",
        }

    def test_create_vessel_success(self, vessel_repository, sample_vessel_data):
        """Test successful vessel creation."""
        vessel = vessel_repository.create(sample_vessel_data)
        vessel_repository.db_session.commit()

        assert vessel.id is not None
        assert vessel.name == "Test Vessel T-001"
        assert vessel.project_id == 1
        assert vessel.surface_area_m2 == 25.13

    def test_get_vessel_by_equipment_tag(self, vessel_repository, sample_vessel_data):
        """Test retrieving vessel by equipment tag."""
        vessel_repository.create(sample_vessel_data)
        vessel_repository.db_session.commit()

        vessel = vessel_repository.get_by_equipment_tag(1, "T-001")

        assert vessel is not None
        assert vessel.equipment_tag == "T-001"
        assert vessel.project_id == 1

    def test_get_vessels_without_circuits(self, vessel_repository, sample_vessel_data):
        """Test retrieving vessels without circuits."""
        vessel_repository.create(sample_vessel_data)
        vessel_repository.db_session.commit()

        vessels = vessel_repository.get_vessels_without_circuits(1)

        assert len(vessels) >= 1

    def test_update_heat_loss_calculation(self, vessel_repository, sample_vessel_data):
        """Test updating vessel heat loss calculation."""
        vessel = vessel_repository.create(sample_vessel_data)
        vessel_repository.db_session.commit()

        updated_vessel = vessel_repository.update_heat_loss_calculation(
            vessel_id=vessel.id, heat_loss_w=1250.0, required_output_w=1562.5
        )

        assert updated_vessel is not None
        assert updated_vessel.calculated_heat_loss_w == 1250.0
        assert updated_vessel.required_heat_output_w == 1562.5


class TestHTCircuitRepository:
    """Test HTCircuitRepository class."""

    @pytest.fixture
    def htcircuit_repository(self, db_session):
        """Create an HTCircuitRepository instance for testing."""
        return HTCircuitRepository(db_session)

    @pytest.fixture
    def sample_htcircuit_data(self):
        """Sample HT circuit data for testing."""
        return {
            "name": "HTC-001-A",
            "feeder_id": 1,
            "heat_tracing_cable_id": 20,
            "pipe_id": 1,
            "vessel_id": None,
            "control_circuit_id": 1,
            "number_of_circuits": 1,
            "isometric_no": "ISO-001",
        }

    def test_create_htcircuit_success(
        self, htcircuit_repository, sample_htcircuit_data
    ):
        """Test successful HT circuit creation."""
        circuit = htcircuit_repository.create(sample_htcircuit_data)
        htcircuit_repository.db_session.commit()

        assert circuit.id is not None
        assert circuit.name == "HTC-001-A"
        assert circuit.feeder_id == 1
        assert circuit.pipe_id == 1

    def test_get_by_feeder_id(self, htcircuit_repository, sample_htcircuit_data):
        """Test retrieving HT circuits by feeder ID."""
        htcircuit_repository.create(sample_htcircuit_data)
        htcircuit_repository.db_session.commit()

        circuits = htcircuit_repository.get_by_feeder_id(1)

        assert len(circuits) >= 1
        assert all(circuit.feeder_id == 1 for circuit in circuits)

    def test_get_by_pipe_id(self, htcircuit_repository, sample_htcircuit_data):
        """Test retrieving HT circuit by pipe ID."""
        htcircuit_repository.create(sample_htcircuit_data)
        htcircuit_repository.db_session.commit()

        circuit = htcircuit_repository.get_by_pipe_id(1)

        assert circuit is not None
        assert circuit.pipe_id == 1

    def test_update_load_calculation(self, htcircuit_repository, sample_htcircuit_data):
        """Test updating HT circuit load calculation."""
        circuit = htcircuit_repository.create(sample_htcircuit_data)
        htcircuit_repository.db_session.commit()

        updated_circuit = htcircuit_repository.update_load_calculation(
            circuit_id=circuit.id, load_amps=5.2, load_kw=1.25, required_length_m=50.0
        )

        assert updated_circuit is not None
        assert updated_circuit.calculated_load_amps == 5.2
        assert updated_circuit.calculated_load_kw == 1.25
        assert updated_circuit.required_length_m == 50.0

    def test_get_total_feeder_load(self, htcircuit_repository, sample_htcircuit_data):
        """Test calculating total feeder load."""
        # Create circuit with load
        circuit = htcircuit_repository.create(sample_htcircuit_data)
        htcircuit_repository.db_session.commit()

        htcircuit_repository.update_load_calculation(
            circuit_id=circuit.id, load_amps=5.2, load_kw=1.25, required_length_m=50.0
        )
        htcircuit_repository.db_session.commit()

        total_load = htcircuit_repository.get_total_feeder_load(1)

        assert total_load == 1.25


class TestControlCircuitRepository:
    """Test ControlCircuitRepository class."""

    @pytest.fixture
    def control_circuit_repository(self, db_session):
        """Create a ControlCircuitRepository instance for testing."""
        return ControlCircuitRepository(db_session)

    @pytest.fixture
    def sample_control_circuit_data(self):
        """Sample control circuit data for testing."""
        from core.models.enums import ControlCircuitType, SensorType

        return {
            "name": "Temperature Control TC-001",
            "switchboard_id": 1,
            "type": ControlCircuitType.TEMPERATURE_CONTROL,
            "sensor_type": SensorType.RTD,
            "primary_setpoint_c": 65.0,
            "limiting_setpoint_c": 85.0,
            "has_limiting_function": True,
        }

    def test_create_control_circuit_success(
        self, control_circuit_repository, sample_control_circuit_data
    ):
        """Test successful control circuit creation."""
        circuit = control_circuit_repository.create(sample_control_circuit_data)
        control_circuit_repository.db_session.commit()

        assert circuit.id is not None
        assert circuit.name == "Temperature Control TC-001"
        assert circuit.primary_setpoint_c == 65.0
        assert circuit.has_limiting_function is True

    def test_get_by_switchboard_id(
        self, control_circuit_repository, sample_control_circuit_data
    ):
        """Test retrieving control circuits by switchboard ID."""
        control_circuit_repository.create(sample_control_circuit_data)
        control_circuit_repository.db_session.commit()

        circuits = control_circuit_repository.get_by_switchboard_id(1)

        assert len(circuits) >= 1
        assert all(circuit.switchboard_id == 1 for circuit in circuits)

    def test_get_with_limiting_function(
        self, control_circuit_repository, sample_control_circuit_data
    ):
        """Test retrieving control circuits with limiting function."""
        control_circuit_repository.create(sample_control_circuit_data)
        control_circuit_repository.db_session.commit()

        circuits = control_circuit_repository.get_with_limiting_function(1)

        assert len(circuits) >= 1
        assert all(circuit.has_limiting_function is True for circuit in circuits)


class TestHeatTracingRepository:
    """Test HeatTracingRepository aggregate class."""

    @pytest.fixture
    def heat_tracing_repository(self, db_session):
        """Create a HeatTracingRepository instance for testing."""
        return HeatTracingRepository(db_session)

    def test_initialization(self, heat_tracing_repository):
        """Test repository initialization."""
        assert heat_tracing_repository.pipes is not None
        assert heat_tracing_repository.vessels is not None
        assert heat_tracing_repository.ht_circuits is not None
        assert heat_tracing_repository.control_circuits is not None

    def test_get_project_summary_empty(self, heat_tracing_repository):
        """Test project summary with no data."""
        summary = heat_tracing_repository.get_project_summary(1)

        assert summary["project_id"] == 1
        assert summary["total_pipes"] == 0
        assert summary["total_vessels"] == 0
        assert summary["pipes_without_circuits"] == 0
        assert summary["vessels_without_circuits"] == 0

    def test_get_design_readiness_empty(self, heat_tracing_repository):
        """Test design readiness with no data."""
        readiness = heat_tracing_repository.get_design_readiness(1)

        assert readiness["project_id"] == 1
        assert (
            readiness["is_ready_for_circuit_assignment"] is True
        )  # No items to assign
        assert readiness["completion_percentage"] == 0.0
        assert len(readiness["missing_items"]) == 0

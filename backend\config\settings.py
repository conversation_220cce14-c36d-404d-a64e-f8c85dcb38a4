# backend/config/settings.py
import os
from typing import Optional

from pydantic import (  # Field for more control, HttpUrl for URL validation
    Field,
)
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """
    Application settings loaded from environment variables or a .env file.
    Pydantic's BaseSettings handles the loading and validation.
    """

    # --- Application Metadata ---
    APP_NAME: str = "Heat Tracing Design Application"
    APP_VERSION: str = "1.0.0"
    APP_DESCRIPTION: str = "An engineering application for heat tracing design, calculations, and management."

    # Environment can be 'development', 'testing', 'production'
    ENVIRONMENT: str = Field(
        "development",
        pattern="^(development|testing|production)$",
        description="Application environment (development, testing, production)",
    )
    DEBUG: bool = Field(
        True,
        description="Enable debug features (e.g., debug endpoints, detailed error messages)",
    )

    APP_PORT: int = Field(
        8000,
        description="Port to run the FastAPI server on.",
    )

    # --- Database Configuration ---
    DATABASE_URL: Optional[str] = Field(
        None,
        description="Primary database connection string (e.g., SQL Server, PostgreSQL)."
        " If not provided, SQLite fallback will be used.",
    )
    SQLITE_DATABASE_PATH: str = Field(
        "./data/app.db",  # Default path for SQLite database file
        description="Path to the SQLite database file for offline mode or fallback.",
    )
    DB_ECHO: bool = Field(
        False,
        description="Set to True to log all SQLAlchemy statements to stdout (useful for debugging DB queries).",
    )

    # --- Caching & Rate Limiting (Redis) ---
    REDIS_ENABLED: bool = Field(
        False,
        description="Set to True to enable Redis for caching and/or rate limiting.",
    )
    REDIS_URL: Optional[str] = Field(
        None,
        description="Redis connection URL (e.g., 'redis://localhost:6379/0'). Required if REDIS_ENABLED is True.",
    )
    RATE_LIMIT_ENABLED: bool = Field(
        True,  # Default to True for security, can be disabled for dev
        description="Set to True to enable API rate limiting.",
    )
    RATE_LIMIT_DEFAULT_REQUESTS_PER_MINUTE: int = Field(
        100,  # Default rate limit
        description="Default number of requests allowed per minute for rate limiting.",
    )

    # --- Security ---
    # !!! IMPORTANT !!!
    # This key MUST be changed in production. Generate a strong, random string (e.g., using secrets.token_urlsafe(32)).
    # It should be loaded from an environment variable in production deployments.
    SECRET_KEY: str = Field(
        "your_super_secret_key_please_change_this_in_production_!!!",
        min_length=32,
        description="Secret key for cryptographic operations (e.g., JWT signing). Must be strong and secret.",
    )
    JWT_ALGORITHM: str = Field(
        "HS256", description="Algorithm used for signing JSON Web Tokens (JWTs)."
    )
    JWT_ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(
        30, description="Expiration time for JWT access tokens in minutes."
    )

    # --- Logging ---
    # Supported levels: CRITICAL, ERROR, WARNING, INFO, DEBUG, NOTSET
    LOG_LEVEL: str = Field(
        "INFO",
        pattern="^(CRITICAL|ERROR|WARNING|INFO|DEBUG|NOTSET)$",
        description="Minimum logging level for the application.",
    )

    # --- Computed Properties ---
    @property
    def effective_database_url(self) -> str:
        """
        Returns the effective database URL to use, with automatic fallback to SQLite.

        Logic:
        1. If DATABASE_URL is provided and environment is production, use it
        2. If DATABASE_URL is provided and environment is development/testing, try it but fallback to SQLite
        3. If DATABASE_URL is None, use SQLite
        """
        if self.DATABASE_URL:
            # For production, always use the provided DATABASE_URL
            if self.ENVIRONMENT == "production":
                return self.DATABASE_URL
            # For development/testing, we'll try the DATABASE_URL but the database layer will handle fallback
            return self.DATABASE_URL

        # Fallback to SQLite
        return self._get_sqlite_url()

    def _get_sqlite_url(self) -> str:
        """Generate SQLite database URL with proper path handling."""
        # Ensure the directory exists
        db_path = os.path.abspath(self.SQLITE_DATABASE_PATH)
        db_dir = os.path.dirname(db_path)
        os.makedirs(db_dir, exist_ok=True)

        # Return SQLite URL
        return f"sqlite:///{db_path}"

    # --- Pydantic Settings Configuration ---
    # This tells Pydantic's BaseSettings how to load the settings.
    model_config = SettingsConfigDict(
        env_file=".env",  # Look for a .env file in the current directory
        env_file_encoding="utf-8",  # Encoding for the .env file
        case_sensitive=True,  # Environment variable names are case-sensitive
        extra="ignore",  # Ignore extra environment variables not defined here
    )


# Instantiate the settings object to make it globally available
settings = Settings()


def get_settings() -> Settings:
    """Get the application settings instance."""
    return settings

# backend/tests/test_repositories/test_document_repository.py
"""
Unit tests for Document repositories.

Tests CRUD operations and business logic for document-related repositories.
"""

import pytest
from sqlalchemy.orm import Session

from backend.core.models.documents import (
    ImportedDataRevision,
    ExportedDocument,
    CalculationStandard,
)
from backend.core.models.project import Project
from backend.core.models.users import User
from backend.core.repositories.document_repository import (
    ImportedDataRevisionRepository,
    ExportedDocumentRepository,
    CalculationStandardRepository,
)
from backend.core.errors.exceptions import NotFoundError


class TestImportedDataRevisionRepository:
    """Test ImportedDataRevision repository operations."""

    def test_create_imported_data_revision(
        self, db_session: Session, sample_project: Project, sample_user: User
    ):
        """Test creating an imported data revision."""
        repository = ImportedDataRevisionRepository(db_session)

        revision_data = {
            "project_id": sample_project.id,
            "imported_by_user_id": sample_user.id,
            "source_filename": "test_data.xlsx",
            "revision_identifier": "REV-001",
            "import_type": "pipe_data",
            "is_active_revision": True,
        }

        revision = repository.create(revision_data)
        db_session.commit()

        assert revision.id is not None
        assert revision.project_id == sample_project.id
        assert revision.imported_by_user_id == sample_user.id
        assert revision.source_filename == "test_data.xlsx"
        assert revision.revision_identifier == "REV-001"
        assert revision.import_type == "pipe_data"
        assert revision.is_active_revision is True

    def test_get_by_project_id(
        self, db_session: Session, sample_project: Project, sample_user: User
    ):
        """Test getting revisions by project ID."""
        repository = ImportedDataRevisionRepository(db_session)

        # Create test revisions
        revision1_data = {
            "project_id": sample_project.id,
            "imported_by_user_id": sample_user.id,
            "source_filename": "test_data1.xlsx",
            "import_type": "pipe_data",
            "is_active_revision": True,
        }
        revision2_data = {
            "project_id": sample_project.id,
            "imported_by_user_id": sample_user.id,
            "source_filename": "test_data2.xlsx",
            "import_type": "vessel_data",
            "is_active_revision": False,
        }

        revision1 = repository.create(revision1_data)
        revision2 = repository.create(revision2_data)
        db_session.commit()

        # Test retrieval
        revisions = repository.get_by_project_id(sample_project.id)

        assert len(revisions) == 2
        revision_ids = [r.id for r in revisions]
        assert revision1.id in revision_ids
        assert revision2.id in revision_ids

    def test_get_active_revision_by_filename(
        self, db_session: Session, sample_project: Project, sample_user: User
    ):
        """Test getting active revision by filename."""
        repository = ImportedDataRevisionRepository(db_session)

        # Create test revisions
        revision1_data = {
            "project_id": sample_project.id,
            "imported_by_user_id": sample_user.id,
            "source_filename": "test_data.xlsx",
            "import_type": "pipe_data",
            "is_active_revision": False,
        }
        revision2_data = {
            "project_id": sample_project.id,
            "imported_by_user_id": sample_user.id,
            "source_filename": "test_data.xlsx",
            "import_type": "pipe_data",
            "is_active_revision": True,
        }

        revision1 = repository.create(revision1_data)
        revision2 = repository.create(revision2_data)
        db_session.commit()

        # Test retrieval
        active_revision = repository.get_active_revision_by_filename(
            sample_project.id, "test_data.xlsx"
        )

        assert active_revision is not None
        assert active_revision.id == revision2.id
        assert active_revision.is_active_revision is True

    def test_deactivate_other_revisions(
        self, db_session: Session, sample_project: Project, sample_user: User
    ):
        """Test deactivating other revisions."""
        repository = ImportedDataRevisionRepository(db_session)

        # Create test revisions
        revision1_data = {
            "project_id": sample_project.id,
            "imported_by_user_id": sample_user.id,
            "source_filename": "test_data.xlsx",
            "import_type": "pipe_data",
            "is_active_revision": True,
        }
        revision2_data = {
            "project_id": sample_project.id,
            "imported_by_user_id": sample_user.id,
            "source_filename": "test_data.xlsx",
            "import_type": "pipe_data",
            "is_active_revision": True,
        }

        revision1 = repository.create(revision1_data)
        revision2 = repository.create(revision2_data)
        db_session.commit()

        # Deactivate others except revision2
        count = repository.deactivate_other_revisions(
            sample_project.id, "test_data.xlsx", exclude_id=revision2.id
        )
        db_session.commit()

        assert count == 1

        # Verify revision1 is deactivated
        db_session.refresh(revision1)
        assert revision1.is_active_revision is False

        # Verify revision2 is still active
        db_session.refresh(revision2)
        assert revision2.is_active_revision is True


class TestExportedDocumentRepository:
    """Test ExportedDocument repository operations."""

    def test_create_exported_document(
        self, db_session: Session, sample_project: Project, sample_user: User
    ):
        """Test creating an exported document."""
        repository = ExportedDocumentRepository(db_session)

        document_data = {
            "project_id": sample_project.id,
            "generated_by_user_id": sample_user.id,
            "document_type": "heat_tracing_report",
            "filename": "HT_Report_v1.pdf",
            "revision": "v1.0",
            "file_path_or_url": "/documents/ht_report_1.pdf",
            "is_latest_revision": True,
        }

        document = repository.create(document_data)
        db_session.commit()

        assert document.id is not None
        assert document.project_id == sample_project.id
        assert document.generated_by_user_id == sample_user.id
        assert document.document_type == "heat_tracing_report"
        assert document.filename == "HT_Report_v1.pdf"
        assert document.revision == "v1.0"
        assert document.is_latest_revision is True

    def test_get_latest_by_document_type(
        self, db_session: Session, sample_project: Project, sample_user: User
    ):
        """Test getting latest document by type."""
        repository = ExportedDocumentRepository(db_session)

        # Create test documents
        doc1_data = {
            "project_id": sample_project.id,
            "generated_by_user_id": sample_user.id,
            "document_type": "heat_tracing_report",
            "filename": "HT_Report_v1.pdf",
            "is_latest_revision": False,
        }
        doc2_data = {
            "project_id": sample_project.id,
            "generated_by_user_id": sample_user.id,
            "document_type": "heat_tracing_report",
            "filename": "HT_Report_v2.pdf",
            "is_latest_revision": True,
        }

        doc1 = repository.create(doc1_data)
        doc2 = repository.create(doc2_data)
        db_session.commit()

        # Test retrieval
        latest_doc = repository.get_latest_by_document_type(
            sample_project.id, "heat_tracing_report"
        )

        assert latest_doc is not None
        assert latest_doc.id == doc2.id
        assert latest_doc.is_latest_revision is True

    def test_mark_others_as_not_latest(
        self, db_session: Session, sample_project: Project, sample_user: User
    ):
        """Test marking other documents as not latest."""
        repository = ExportedDocumentRepository(db_session)

        # Create test documents
        doc1_data = {
            "project_id": sample_project.id,
            "generated_by_user_id": sample_user.id,
            "document_type": "heat_tracing_report",
            "filename": "HT_Report_v1.pdf",
            "is_latest_revision": True,
        }
        doc2_data = {
            "project_id": sample_project.id,
            "generated_by_user_id": sample_user.id,
            "document_type": "heat_tracing_report",
            "filename": "HT_Report_v2.pdf",
            "is_latest_revision": True,
        }

        doc1 = repository.create(doc1_data)
        doc2 = repository.create(doc2_data)
        db_session.commit()

        # Mark others as not latest except doc2
        count = repository.mark_others_as_not_latest(
            sample_project.id, "heat_tracing_report", exclude_id=doc2.id
        )
        db_session.commit()

        assert count == 1

        # Verify doc1 is not latest
        db_session.refresh(doc1)
        assert doc1.is_latest_revision is False

        # Verify doc2 is still latest
        db_session.refresh(doc2)
        assert doc2.is_latest_revision is True


class TestCalculationStandardRepository:
    """Test CalculationStandard repository operations."""

    def test_create_calculation_standard(self, db_session: Session):
        """Test creating a calculation standard."""
        repository = CalculationStandardRepository(db_session)

        standard_data = {
            "name": "TR 50410 Heat Tracing Standard",
            "standard_code": "TR-50410",
            "description": "Technical Report 50410 for heat tracing calculations",
            "parameters_json": '{"safety_factor": 1.2, "max_temp_rating": 250}',
        }

        standard = repository.create(standard_data)
        db_session.commit()

        assert standard.id is not None
        assert standard.name == "TR 50410 Heat Tracing Standard"
        assert standard.standard_code == "TR-50410"
        assert (
            standard.description
            == "Technical Report 50410 for heat tracing calculations"
        )
        assert (
            standard.parameters_json == '{"safety_factor": 1.2, "max_temp_rating": 250}'
        )

    def test_get_by_standard_code(self, db_session: Session):
        """Test getting standard by code."""
        repository = CalculationStandardRepository(db_session)

        standard_data = {
            "name": "Test Standard",
            "standard_code": "TEST-001",
            "description": "Test description",
        }

        created_standard = repository.create(standard_data)
        db_session.commit()

        # Test retrieval
        found_standard = repository.get_by_standard_code("TEST-001")

        assert found_standard is not None
        assert found_standard.id == created_standard.id
        assert found_standard.standard_code == "TEST-001"

    def test_get_by_name(self, db_session: Session):
        """Test getting standard by name."""
        repository = CalculationStandardRepository(db_session)

        standard_data = {
            "name": "Unique Test Standard",
            "standard_code": "UTS-001",
            "description": "Test description",
        }

        created_standard = repository.create(standard_data)
        db_session.commit()

        # Test retrieval
        found_standard = repository.get_by_name("Unique Test Standard")

        assert found_standard is not None
        assert found_standard.id == created_standard.id
        assert found_standard.name == "Unique Test Standard"

    def test_search_by_name_or_code(self, db_session: Session):
        """Test searching standards by name or code."""
        repository = CalculationStandardRepository(db_session)

        # Create test standards
        standard1_data = {
            "name": "Heat Tracing Standard",
            "standard_code": "HT-001",
        }
        standard2_data = {
            "name": "Electrical Standard",
            "standard_code": "EL-001",
        }
        standard3_data = {
            "name": "Heat Loss Calculation",
            "standard_code": "HL-001",
        }

        std1 = repository.create(standard1_data)
        std2 = repository.create(standard2_data)
        std3 = repository.create(standard3_data)
        db_session.commit()

        # Test search by name
        heat_standards = repository.search_by_name_or_code("Heat")
        assert len(heat_standards) == 2
        heat_ids = [s.id for s in heat_standards]
        assert std1.id in heat_ids
        assert std3.id in heat_ids

        # Test search by code
        ht_standards = repository.search_by_name_or_code("HT")
        assert len(ht_standards) == 1
        assert ht_standards[0].id == std1.id

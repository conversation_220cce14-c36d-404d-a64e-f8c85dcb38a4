# backend/core/services/activity_log_service.py
"""
Service for Activity Log operations and audit trail management.

This module provides high-level business logic for activity log workflows including:
- Event logging and audit trail creation
- User activity tracking and monitoring
- Security event monitoring and alerting
- Compliance reporting and audit analysis
- System event logging and performance monitoring
"""

import json
import logging
import secrets
import uuid
from datetime import datetime, timed<PERSON><PERSON>
from typing import Any, Dict, List, Optional

from sqlalchemy.orm import Session

try:
    from core.errors.exceptions import (
        BaseApplicationException,
        InvalidInputError,
        NotFoundError,
    )
    from core.repositories.activity_log_repository import ActivityLogRepository
    from core.repositories.user_repository import UserRepository
    from core.schemas.activity_log_schemas import (
        ActivityLogCreateSchema,
        ActivityLogFilterSchema,
        ActivityLogPaginatedResponseSchema,
        ActivityLogReadSchema,
        ActivityLogSummarySchema,
        ActivityLogUpdateSchema,
        AuditReportRequestSchema,
        AuditReportResponseSchema,
        AuditReportSummarySchema,
        EventCategoryEnum,
        EventCategoryMappingSchema,
        EventTypeEnum,
        EntityTypeEnum,
        SecurityEventSchema,
    )
    from config.logging_config import get_logger
except ImportError:
    from core.errors.exceptions import (
        BaseApplicationException,
        InvalidInputError,
        NotFoundError,
    )
    from core.repositories.activity_log_repository import ActivityLogRepository
    from core.repositories.user_repository import UserRepository
    from core.schemas.activity_log_schemas import (
        ActivityLogCreateSchema,
        ActivityLogFilterSchema,
        ActivityLogPaginatedResponseSchema,
        ActivityLogReadSchema,
        ActivityLogSummarySchema,
        ActivityLogUpdateSchema,
        AuditReportRequestSchema,
        AuditReportResponseSchema,
        AuditReportSummarySchema,
        EventCategoryEnum,
        EventCategoryMappingSchema,
        EventTypeEnum,
        EntityTypeEnum,
        SecurityEventSchema,
    )
    from config.logging_config import get_logger

# Initialize logger for this module
logger = get_logger(__name__)


class ActivityLogService:
    """
    Service for activity log operations and audit trail management.

    This service provides high-level business logic for activity log workflows,
    including event logging, audit trail management, and compliance reporting.
    """

    def __init__(self, db_session: Session):
        """
        Initialize the activity log service.

        Args:
            db_session: SQLAlchemy database session
        """
        self.db_session = db_session
        self.activity_log_repo = ActivityLogRepository(db_session)
        self.user_repo = UserRepository(db_session)
        logger.debug("ActivityLogService initialized")

    # ============================================================================
    # EVENT LOGGING OPERATIONS
    # ============================================================================

    def log_event(
        self,
        event_type: EventTypeEnum,
        user_id: Optional[int] = None,
        entity_type: Optional[EntityTypeEnum] = None,
        entity_id: Optional[int] = None,
        details: Optional[str] = None,
    ) -> ActivityLogReadSchema:
        """
        Log a new activity event.

        Args:
            event_type: Type of event that occurred
            user_id: ID of the user who performed the action (optional for system events)
            entity_type: Type of entity affected (optional)
            entity_id: ID of the entity affected (optional)
            details: Additional event details (optional)

        Returns:
            ActivityLogReadSchema: Created activity log entry

        Raises:
            InvalidInputError: If input validation fails
            BaseApplicationException: If logging fails
        """
        logger.debug(
            f"Logging event: {event_type.value}, user_id={user_id}, "
            f"entity_type={entity_type}, entity_id={entity_id}"
        )

        try:
            # Validate user exists if user_id is provided
            if user_id is not None:
                user = self.user_repo.get_by_id(user_id)
                if not user or not user.is_active:
                    raise InvalidInputError(f"User {user_id} not found or inactive")

            # Create activity log data
            log_data = ActivityLogCreateSchema(
                user_id=user_id,
                event_type=event_type,
                entity_type=entity_type,
                entity_id=entity_id,
                details=details,
            )

            # Create activity log entry
            log_dict = log_data.model_dump(exclude_unset=True)
            activity_log = self.activity_log_repo.create(log_dict)
            self.db_session.commit()
            self.db_session.refresh(activity_log)

            logger.info(
                f"Event logged successfully: {event_type.value}, log_id={activity_log.id}"
            )
            return ActivityLogReadSchema.model_validate(activity_log)

        except (InvalidInputError, NotFoundError):
            self.db_session.rollback()
            raise
        except Exception as e:
            self.db_session.rollback()
            logger.error(f"Failed to log event {event_type.value}: {e}", exc_info=True)
            raise BaseApplicationException(
                code="EVENT_LOGGING_ERROR",
                detail=f"Failed to log event: {str(e)}",
            )

    def log_user_action(
        self,
        user_id: int,
        action: str,
        entity_type: Optional[str] = None,
        entity_id: Optional[int] = None,
        details: Optional[Dict[str, Any]] = None,
    ) -> ActivityLogReadSchema:
        """
        Log a user action with standardized format.

        Args:
            user_id: ID of the user performing the action
            action: Action performed (CREATE, UPDATE, DELETE, etc.)
            entity_type: Type of entity affected
            entity_id: ID of the entity affected
            details: Additional action details

        Returns:
            ActivityLogReadSchema: Created activity log entry

        Raises:
            InvalidInputError: If input validation fails
        """
        logger.debug(
            f"Logging user action: user_id={user_id}, action={action}, "
            f"entity_type={entity_type}, entity_id={entity_id}"
        )

        try:
            # Convert action string to EventTypeEnum
            event_type = EventTypeEnum(action.upper())
        except ValueError:
            raise InvalidInputError(f"Invalid action type: {action}")

        try:
            # Convert entity_type string to EntityTypeEnum if provided
            entity_type_enum = None
            if entity_type:
                entity_type_enum = EntityTypeEnum(entity_type)
        except ValueError:
            raise InvalidInputError(f"Invalid entity type: {entity_type}")

        # Convert details to JSON string if provided
        details_str = None
        if details:
            try:
                details_str = json.dumps(details, default=str)
            except (TypeError, ValueError) as e:
                logger.warning(f"Failed to serialize details to JSON: {e}")
                details_str = str(details)

        return self.log_event(
            event_type=event_type,
            user_id=user_id,
            entity_type=entity_type_enum,
            entity_id=entity_id,
            details=details_str,
        )

    def log_authentication_event(
        self,
        user_id: Optional[int],
        event_type: str,
        success: bool,
        details: Optional[Dict[str, Any]] = None,
    ) -> ActivityLogReadSchema:
        """
        Log an authentication event.

        Args:
            user_id: ID of the user (None for failed login attempts)
            event_type: Type of authentication event (LOGIN, LOGOUT, etc.)
            success: Whether the authentication was successful
            details: Additional authentication details

        Returns:
            ActivityLogReadSchema: Created activity log entry
        """
        logger.debug(
            f"Logging authentication event: user_id={user_id}, "
            f"event_type={event_type}, success={success}"
        )

        # Determine the actual event type based on success
        if event_type.upper() == "LOGIN" and not success:
            auth_event_type = EventTypeEnum.LOGIN_FAILED
        else:
            try:
                auth_event_type = EventTypeEnum(event_type.upper())
            except ValueError:
                raise InvalidInputError(
                    f"Invalid authentication event type: {event_type}"
                )

        # Add success status to details
        auth_details = details or {}
        auth_details["success"] = success

        return self.log_event(
            event_type=auth_event_type,
            user_id=user_id,
            entity_type=EntityTypeEnum.USER,
            entity_id=user_id,
            details=json.dumps(auth_details, default=str),
        )

    def log_system_event(
        self,
        event_type: str,
        details: Optional[Dict[str, Any]] = None,
    ) -> ActivityLogReadSchema:
        """
        Log a system event.

        Args:
            event_type: Type of system event
            details: Additional system event details

        Returns:
            ActivityLogReadSchema: Created activity log entry
        """
        logger.debug(f"Logging system event: event_type={event_type}")

        try:
            system_event_type = EventTypeEnum(event_type.upper())
        except ValueError:
            raise InvalidInputError(f"Invalid system event type: {event_type}")

        details_str = None
        if details:
            try:
                details_str = json.dumps(details, default=str)
            except (TypeError, ValueError) as e:
                logger.warning(f"Failed to serialize system event details to JSON: {e}")
                details_str = str(details)

        return self.log_event(
            event_type=system_event_type,
            user_id=None,  # System events don't have a user
            entity_type=None,
            entity_id=None,
            details=details_str,
        )

    # ============================================================================
    # ACTIVITY LOG RETRIEVAL OPERATIONS
    # ============================================================================

    def get_activity_log(self, log_id: int) -> ActivityLogReadSchema:
        """
        Get activity log by ID.

        Args:
            log_id: Activity log ID

        Returns:
            ActivityLogReadSchema: Activity log data

        Raises:
            NotFoundError: If activity log not found
        """
        logger.debug(f"Retrieving activity log: {log_id}")

        activity_log = self.activity_log_repo.get_by_id(log_id)
        if not activity_log:
            raise NotFoundError(
                code="ACTIVITY_LOG_NOT_FOUND",
                detail=f"Activity log {log_id} not found",
            )

        return ActivityLogReadSchema.model_validate(activity_log)

    def get_activity_logs(
        self,
        filters: Optional[ActivityLogFilterSchema] = None,
        skip: int = 0,
        limit: int = 100,
    ) -> ActivityLogPaginatedResponseSchema:
        """
        Get activity logs with optional filtering.

        Args:
            filters: Optional filter criteria
            skip: Number of records to skip
            limit: Maximum number of records to return

        Returns:
            ActivityLogPaginatedResponseSchema: Paginated activity logs
        """
        logger.debug(f"Retrieving activity logs: skip={skip}, limit={limit}")

        if filters:
            activity_logs = self.activity_log_repo.filter_activity_logs(
                filters, skip, limit
            )
            total = self.activity_log_repo.count_filtered_activity_logs(filters)
        else:
            activity_logs = self.activity_log_repo.get_all(skip, limit)
            # For unfiltered queries, we need to count all records
            from sqlalchemy import func, select

            total_stmt = select(func.count(self.activity_log_repo.model.id))
            total = self.db_session.scalar(total_stmt) or 0

        # Convert to summary schemas
        activity_log_summaries = [
            ActivityLogSummarySchema.model_validate(log) for log in activity_logs
        ]

        # Calculate pagination info
        import math

        total_pages = math.ceil(total / limit) if total > 0 else 1

        return ActivityLogPaginatedResponseSchema(
            activity_logs=activity_log_summaries,
            total=total,
            page=(skip // limit) + 1,
            per_page=limit,
            total_pages=total_pages,
        )

    def get_user_activity_logs(
        self, user_id: int, skip: int = 0, limit: int = 100
    ) -> List[ActivityLogSummarySchema]:
        """
        Get activity logs for a specific user.

        Args:
            user_id: User ID
            skip: Number of records to skip
            limit: Maximum number of records to return

        Returns:
            List[ActivityLogSummarySchema]: User's activity logs

        Raises:
            NotFoundError: If user not found
        """
        logger.debug(
            f"Retrieving activity logs for user {user_id}: skip={skip}, limit={limit}"
        )

        # Validate user exists
        user = self.user_repo.get_by_id(user_id)
        if not user or not user.is_active:
            raise NotFoundError(
                code="USER_NOT_FOUND",
                detail=f"User {user_id} not found or inactive",
            )

        activity_logs = self.activity_log_repo.get_by_user_id(user_id, skip, limit)
        return [ActivityLogSummarySchema.model_validate(log) for log in activity_logs]

    def get_entity_activity_logs(
        self, entity_type: str, entity_id: int, skip: int = 0, limit: int = 100
    ) -> List[ActivityLogSummarySchema]:
        """
        Get activity logs for a specific entity.

        Args:
            entity_type: Type of entity
            entity_id: Entity ID
            skip: Number of records to skip
            limit: Maximum number of records to return

        Returns:
            List[ActivityLogSummarySchema]: Entity's activity logs

        Raises:
            InvalidInputError: If entity type is invalid
        """
        logger.debug(
            f"Retrieving activity logs for entity {entity_type}:{entity_id}: skip={skip}, limit={limit}"
        )

        # Validate entity type
        try:
            EntityTypeEnum(entity_type)
        except ValueError:
            raise InvalidInputError(f"Invalid entity type: {entity_type}")

        activity_logs = self.activity_log_repo.get_by_entity(
            entity_type, entity_id, skip, limit
        )
        return [ActivityLogSummarySchema.model_validate(log) for log in activity_logs]

    def get_recent_activity(self, limit: int = 50) -> List[ActivityLogSummarySchema]:
        """
        Get most recent activity logs.

        Args:
            limit: Maximum number of records to return

        Returns:
            List[ActivityLogSummarySchema]: Recent activity logs
        """
        logger.debug(f"Retrieving {limit} most recent activity logs")

        activity_logs = self.activity_log_repo.get_recent_activity(limit)
        return [ActivityLogSummarySchema.model_validate(log) for log in activity_logs]

    def search_activity_logs(
        self, search_term: str, skip: int = 0, limit: int = 100
    ) -> List[ActivityLogSummarySchema]:
        """
        Search activity logs by details content.

        Args:
            search_term: Search term to match against details
            skip: Number of records to skip
            limit: Maximum number of records to return

        Returns:
            List[ActivityLogSummarySchema]: Matching activity logs
        """
        logger.debug(f"Searching activity logs with term: {search_term}")

        activity_logs = self.activity_log_repo.search_by_details(
            search_term, skip, limit
        )
        return [ActivityLogSummarySchema.model_validate(log) for log in activity_logs]

    # ============================================================================
    # SECURITY EVENT OPERATIONS
    # ============================================================================

    def get_security_events(
        self, start_date: datetime, end_date: datetime, skip: int = 0, limit: int = 100
    ) -> List[ActivityLogSummarySchema]:
        """
        Get security-related events within a date range.

        Args:
            start_date: Start date for filtering
            end_date: End date for filtering
            skip: Number of records to skip
            limit: Maximum number of records to return

        Returns:
            List[ActivityLogSummarySchema]: Security events

        Raises:
            InvalidInputError: If date range is invalid
        """
        logger.debug(
            f"Retrieving security events for date range {start_date} to {end_date}: skip={skip}, limit={limit}"
        )

        if start_date >= end_date:
            raise InvalidInputError("start_date must be before end_date")

        security_events = self.activity_log_repo.get_security_events(
            start_date, end_date, skip, limit
        )
        return [
            ActivityLogSummarySchema.model_validate(event) for event in security_events
        ]

    def log_security_event(
        self,
        event_type: str,
        user_id: Optional[int] = None,
        severity: str = "MEDIUM",
        threat_level: str = "LOW",
        details: Optional[Dict[str, Any]] = None,
    ) -> ActivityLogReadSchema:
        """
        Log a security event with enhanced security context.

        Args:
            event_type: Type of security event
            user_id: ID of the user involved (if applicable)
            severity: Security event severity (LOW, MEDIUM, HIGH, CRITICAL)
            threat_level: Assessed threat level (NONE, LOW, MEDIUM, HIGH, CRITICAL)
            details: Additional security event details

        Returns:
            ActivityLogReadSchema: Created activity log entry

        Raises:
            InvalidInputError: If input validation fails
        """
        logger.debug(
            f"Logging security event: event_type={event_type}, user_id={user_id}, "
            f"severity={severity}, threat_level={threat_level}"
        )

        # Validate severity and threat level
        allowed_severities = ["LOW", "MEDIUM", "HIGH", "CRITICAL"]
        allowed_threat_levels = ["NONE", "LOW", "MEDIUM", "HIGH", "CRITICAL"]

        if severity not in allowed_severities:
            raise InvalidInputError(f"Invalid severity: {severity}")

        if threat_level not in allowed_threat_levels:
            raise InvalidInputError(f"Invalid threat level: {threat_level}")

        # Enhance details with security context
        security_details = details or {}
        security_details.update(
            {
                "severity": severity,
                "threat_level": threat_level,
                "timestamp": datetime.now().isoformat(),
            }
        )

        return self.log_event(
            event_type=EventTypeEnum(event_type.upper()),
            user_id=user_id,
            entity_type=EntityTypeEnum.USER if user_id else None,
            entity_id=user_id,
            details=json.dumps(security_details, default=str),
        )

    # ============================================================================
    # AUDIT REPORTING OPERATIONS
    # ============================================================================

    def generate_audit_report(
        self,
        report_request: AuditReportRequestSchema,
        generated_by_user_id: Optional[int] = None,
    ) -> AuditReportResponseSchema:
        """
        Generate an audit report based on the request criteria.

        Args:
            report_request: Report generation request
            generated_by_user_id: ID of the user generating the report

        Returns:
            AuditReportResponseSchema: Generated audit report

        Raises:
            InvalidInputError: If request validation fails
        """
        logger.info(
            f"Generating audit report: type={report_request.report_type}, "
            f"date_range={report_request.start_date} to {report_request.end_date}"
        )

        try:
            # Create filter criteria from report request
            filters = ActivityLogFilterSchema(
                start_date=report_request.start_date,
                end_date=report_request.end_date,
                user_id=report_request.user_ids[0]
                if report_request.user_ids and len(report_request.user_ids) == 1
                else None,
                event_type=report_request.event_types[0]
                if report_request.event_types and len(report_request.event_types) == 1
                else None,
                entity_type=report_request.entity_types[0]
                if report_request.entity_types and len(report_request.entity_types) == 1
                else None,
                entity_id=None,
                event_category=report_request.event_categories[0]
                if report_request.event_categories
                and len(report_request.event_categories) == 1
                else None,
                search_details=None,
            )

            # Get activity logs for the report
            activity_logs = self.activity_log_repo.filter_activity_logs(
                filters, skip=0, limit=10000
            )  # Large limit for reports

            # Generate report summary
            summary = self._generate_report_summary(
                activity_logs, report_request.start_date, report_request.end_date
            )

            # Convert to summary schemas
            event_summaries = [
                ActivityLogSummarySchema.model_validate(log) for log in activity_logs
            ]

            # Generate unique report ID
            report_id = str(uuid.uuid4())

            # Create audit report response
            report = AuditReportResponseSchema(
                report_id=report_id,
                report_type=report_request.report_type,
                generated_at=datetime.now(),
                generated_by_user_id=generated_by_user_id,
                start_date=report_request.start_date,
                end_date=report_request.end_date,
                summary=summary,
                events=event_summaries,
            )

            logger.info(
                f"Audit report generated successfully: {report_id}, "
                f"total_events={summary.total_events}"
            )
            return report

        except Exception as e:
            logger.error(f"Failed to generate audit report: {e}", exc_info=True)
            raise BaseApplicationException(
                code="AUDIT_REPORT_ERROR",
                detail=f"Failed to generate audit report: {str(e)}",
            )

    def _generate_report_summary(
        self, activity_logs: List, start_date: datetime, end_date: datetime
    ) -> AuditReportSummarySchema:
        """
        Generate summary statistics for an audit report.

        Args:
            activity_logs: List of activity logs
            start_date: Report start date
            end_date: Report end date

        Returns:
            AuditReportSummarySchema: Report summary
        """
        total_events = len(activity_logs)
        unique_users = len(
            set(log.user_id for log in activity_logs if log.user_id is not None)
        )
        unique_entities = len(
            set(
                f"{log.entity_type}:{log.entity_id}"
                for log in activity_logs
                if log.entity_type and log.entity_id
            )
        )

        # Count events by type
        event_type_counts = {}
        for log in activity_logs:
            event_type = log.event_type
            event_type_counts[event_type] = event_type_counts.get(event_type, 0) + 1

        # Count events by entity type
        entity_type_counts = {}
        for log in activity_logs:
            if log.entity_type:
                entity_type = log.entity_type
                entity_type_counts[entity_type] = (
                    entity_type_counts.get(entity_type, 0) + 1
                )

        # Count daily activity
        daily_activity_counts = {}
        for log in activity_logs:
            date_str = log.timestamp.date().isoformat()
            daily_activity_counts[date_str] = daily_activity_counts.get(date_str, 0) + 1

        return AuditReportSummarySchema(
            total_events=total_events,
            unique_users=unique_users,
            unique_entities=unique_entities,
            event_type_counts=event_type_counts,
            entity_type_counts=entity_type_counts,
            daily_activity_counts=daily_activity_counts,
        )

    def get_user_activity_summary(
        self, user_id: int, start_date: datetime, end_date: datetime
    ) -> Dict[str, Any]:
        """
        Get activity summary for a specific user.

        Args:
            user_id: User ID
            start_date: Start date for filtering
            end_date: End date for filtering

        Returns:
            Dict[str, Any]: User activity summary

        Raises:
            NotFoundError: If user not found
            InvalidInputError: If date range is invalid
        """
        logger.debug(
            f"Generating activity summary for user {user_id} from {start_date} to {end_date}"
        )

        if start_date >= end_date:
            raise InvalidInputError("start_date must be before end_date")

        # Validate user exists
        user = self.user_repo.get_by_id(user_id)
        if not user or not user.is_active:
            raise NotFoundError(
                code="USER_NOT_FOUND",
                detail=f"User {user_id} not found or inactive",
            )

        return self.activity_log_repo.get_user_activity_summary(
            user_id, start_date, end_date
        )

    # ============================================================================
    # UTILITY OPERATIONS
    # ============================================================================

    def update_activity_log(
        self, log_id: int, update_data: ActivityLogUpdateSchema
    ) -> ActivityLogReadSchema:
        """
        Update an activity log entry.

        Args:
            log_id: Activity log ID
            update_data: Update data

        Returns:
            ActivityLogReadSchema: Updated activity log

        Raises:
            NotFoundError: If activity log not found
        """
        logger.debug(f"Updating activity log: {log_id}")

        try:
            # Get existing activity log
            activity_log = self.activity_log_repo.get_by_id(log_id)
            if not activity_log:
                raise NotFoundError(
                    code="ACTIVITY_LOG_NOT_FOUND",
                    detail=f"Activity log {log_id} not found",
                )

            # Update activity log
            update_dict = update_data.model_dump(exclude_unset=True)
            updated_log = self.activity_log_repo.update(log_id, update_dict)
            if not updated_log:
                raise NotFoundError(
                    code="ACTIVITY_LOG_NOT_FOUND",
                    detail=f"Activity log {log_id} not found",
                )

            self.db_session.commit()
            self.db_session.refresh(updated_log)

            logger.info(f"Activity log updated successfully: {log_id}")
            return ActivityLogReadSchema.model_validate(updated_log)

        except (NotFoundError, InvalidInputError):
            self.db_session.rollback()
            raise
        except Exception as e:
            self.db_session.rollback()
            logger.error(f"Failed to update activity log {log_id}: {e}", exc_info=True)
            raise BaseApplicationException(
                code="ACTIVITY_LOG_UPDATE_ERROR",
                detail=f"Failed to update activity log: {str(e)}",
            )

    def delete_old_activity_logs(self, days_to_keep: int = 365) -> int:
        """
        Delete activity logs older than the specified number of days.

        Args:
            days_to_keep: Number of days to keep logs (default: 365)

        Returns:
            int: Number of logs deleted

        Raises:
            InvalidInputError: If days_to_keep is invalid
        """
        logger.info(f"Deleting activity logs older than {days_to_keep} days")

        if days_to_keep < 1:
            raise InvalidInputError("days_to_keep must be at least 1")

        try:
            cutoff_date = datetime.now() - timedelta(days=days_to_keep)
            deleted_count = self.activity_log_repo.delete_old_logs(cutoff_date)

            if deleted_count > 0:
                self.db_session.commit()
                logger.info(f"Deleted {deleted_count} old activity logs")
            else:
                logger.info("No old activity logs to delete")

            return deleted_count

        except Exception as e:
            self.db_session.rollback()
            logger.error(f"Failed to delete old activity logs: {e}", exc_info=True)
            raise BaseApplicationException(
                code="ACTIVITY_LOG_CLEANUP_ERROR",
                detail=f"Failed to delete old activity logs: {str(e)}",
            )

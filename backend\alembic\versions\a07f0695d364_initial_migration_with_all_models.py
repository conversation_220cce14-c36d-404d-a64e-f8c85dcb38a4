"""Initial migration with all models

Revision ID: a07f0695d364
Revises:
Create Date: 2025-05-29 19:48:53.638860

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

# Import for EnumType
import sys
import os

project_root = os.path.abspath(
    os.path.join(os.path.dirname(__file__), "..", "..", "..")
)
sys.path.insert(0, project_root)
from core.models.base import EnumType


# revision identifiers, used by Alembic.
revision: str = "a07f0695d364"
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "User",
        sa.Column("email", sa.String(), nullable=True),
        sa.Column("password_hash", sa.String(), nullable=True),
        sa.Column("is_active", sa.<PERSON>(), nullable=False),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("notes", sa.String(), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("email"),
        sa.UniqueConstraint("name", name="uq_user_name"),
    )
    op.create_table(
        "CalculationStandard",
        sa.Column("standard_code", sa.String(), nullable=False),
        sa.Column("description", sa.Text(), nullable=True),
        sa.Column("parameters_json", sa.Text(), nullable=True),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("notes", sa.String(), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.Column("is_deleted", sa.Boolean(), nullable=False),
        sa.Column("deleted_at", sa.DateTime(), nullable=True),
        sa.Column("deleted_by_user_id", sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(
            ["deleted_by_user_id"],
            ["User.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("standard_code"),
    )
    op.create_table(
        "ComponentCategory",
        sa.Column("parent_category_id", sa.Integer(), nullable=True),
        sa.Column("description", sa.Text(), nullable=True),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("notes", sa.String(), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.Column("is_deleted", sa.Boolean(), nullable=False),
        sa.Column("deleted_at", sa.DateTime(), nullable=True),
        sa.Column("deleted_by_user_id", sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(
            ["deleted_by_user_id"],
            ["User.id"],
        ),
        sa.ForeignKeyConstraint(
            ["parent_category_id"],
            ["ComponentCategory.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("name", name="uq_component_category_name"),
    )
    op.create_table(
        "Project",
        sa.Column("project_number", sa.String(), nullable=False),
        sa.Column("description", sa.Text(), nullable=True),
        sa.Column("designer", sa.String(), nullable=True),
        sa.Column("min_ambient_temp_c", sa.Float(), nullable=False),
        sa.Column("max_ambient_temp_c", sa.Float(), nullable=False),
        sa.Column("desired_maintenance_temp_c", sa.Float(), nullable=False),
        sa.Column("wind_speed_ms", sa.Float(), nullable=True),
        sa.Column("installation_environment", sa.String(), nullable=True),
        sa.Column("available_voltages_json", sa.String(), nullable=True),
        sa.Column("default_cable_manufacturer", sa.String(), nullable=True),
        sa.Column("default_control_device_manufacturer", sa.String(), nullable=True),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("notes", sa.String(), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.Column("is_deleted", sa.Boolean(), nullable=False),
        sa.Column("deleted_at", sa.DateTime(), nullable=True),
        sa.Column("deleted_by_user_id", sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(
            ["deleted_by_user_id"],
            ["User.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("name", name="uq_project_name"),
        sa.UniqueConstraint("project_number", name="uq_project_number"),
    )
    op.create_table(
        "UserPreference",
        sa.Column("user_id", sa.Integer(), nullable=False),
        sa.Column("ui_theme", sa.String(), nullable=False),
        sa.Column("default_min_ambient_temp_c", sa.Float(), nullable=True),
        sa.Column("default_max_ambient_temp_c", sa.Float(), nullable=True),
        sa.Column("default_desired_maintenance_temp_c", sa.Float(), nullable=True),
        sa.Column("default_safety_margin_percent", sa.Float(), nullable=False),
        sa.Column("preferred_cable_manufacturers_json", sa.Text(), nullable=True),
        sa.Column(
            "preferred_control_device_manufacturers_json", sa.Text(), nullable=True
        ),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("notes", sa.String(), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.Column("is_deleted", sa.Boolean(), nullable=False),
        sa.Column("deleted_at", sa.DateTime(), nullable=True),
        sa.Column("deleted_by_user_id", sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(
            ["deleted_by_user_id"],
            ["User.id"],
        ),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["User.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("user_id"),
    )
    op.create_table(
        "Component",
        sa.Column("category_id", sa.Integer(), nullable=False),
        sa.Column("manufacturer", sa.String(), nullable=True),
        sa.Column("model", sa.String(), nullable=True),
        sa.Column("specific_data", sa.Text(), nullable=True),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("notes", sa.String(), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.Column("is_deleted", sa.Boolean(), nullable=False),
        sa.Column("deleted_at", sa.DateTime(), nullable=True),
        sa.Column("deleted_by_user_id", sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(
            ["category_id"],
            ["ComponentCategory.id"],
        ),
        sa.ForeignKeyConstraint(
            ["deleted_by_user_id"],
            ["User.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("name", "category_id", name="uq_component_name_category"),
    )
    op.create_table(
        "ExportedDocument",
        sa.Column("project_id", sa.Integer(), nullable=False),
        sa.Column("generated_by_user_id", sa.Integer(), nullable=True),
        sa.Column("document_type", sa.String(), nullable=False),
        sa.Column("filename", sa.String(), nullable=False),
        sa.Column("revision", sa.String(), nullable=True),
        sa.Column("file_path_or_url", sa.String(), nullable=True),
        sa.Column("is_latest_revision", sa.Boolean(), nullable=False),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("notes", sa.String(), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.Column("is_deleted", sa.Boolean(), nullable=False),
        sa.Column("deleted_at", sa.DateTime(), nullable=True),
        sa.Column("deleted_by_user_id", sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(
            ["deleted_by_user_id"],
            ["User.id"],
        ),
        sa.ForeignKeyConstraint(
            ["generated_by_user_id"],
            ["User.id"],
        ),
        sa.ForeignKeyConstraint(
            ["project_id"],
            ["Project.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        "uq_latest_export_doc",
        "ExportedDocument",
        ["project_id", "document_type", "name", "is_latest_revision"],
        unique=True,
    )
    op.create_table(
        "ImportedDataRevision",
        sa.Column("project_id", sa.Integer(), nullable=False),
        sa.Column("imported_by_user_id", sa.Integer(), nullable=True),
        sa.Column("source_filename", sa.String(), nullable=False),
        sa.Column("revision_identifier", sa.String(), nullable=True),
        sa.Column("import_type", sa.String(), nullable=False),
        sa.Column("is_active_revision", sa.Boolean(), nullable=False),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("notes", sa.String(), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.Column("is_deleted", sa.Boolean(), nullable=False),
        sa.Column("deleted_at", sa.DateTime(), nullable=True),
        sa.Column("deleted_by_user_id", sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(
            ["deleted_by_user_id"],
            ["User.id"],
        ),
        sa.ForeignKeyConstraint(
            ["imported_by_user_id"],
            ["User.id"],
        ),
        sa.ForeignKeyConstraint(
            ["project_id"],
            ["Project.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        "uq_active_import_revision",
        "ImportedDataRevision",
        ["project_id", "source_filename", "is_active_revision"],
        unique=True,
    )
    op.create_table(
        "Switchboard",
        sa.Column("project_id", sa.Integer(), nullable=False),
        sa.Column("location", sa.String(), nullable=True),
        sa.Column("voltage_level_v", sa.Integer(), nullable=False),
        sa.Column("number_of_phases", sa.Integer(), nullable=False),
        sa.Column("type", sa.String(), nullable=True),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("notes", sa.String(), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.Column("is_deleted", sa.Boolean(), nullable=False),
        sa.Column("deleted_at", sa.DateTime(), nullable=True),
        sa.Column("deleted_by_user_id", sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(
            ["deleted_by_user_id"],
            ["User.id"],
        ),
        sa.ForeignKeyConstraint(
            ["project_id"],
            ["Project.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("project_id", "name", name="uq_switchboard_project_name"),
    )
    op.create_table(
        "ControlCircuit",
        sa.Column("switchboard_id", sa.Integer(), nullable=True),
        sa.Column("type", sa.String(), nullable=False),
        sa.Column("sensor_type", sa.String(), nullable=False),
        sa.Column("primary_setpoint_c", sa.Float(), nullable=False),
        sa.Column("limiting_setpoint_c", sa.Float(), nullable=True),
        sa.Column("has_limiting_function", sa.Boolean(), nullable=False),
        sa.Column("control_philosophy", sa.String(), nullable=True),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("notes", sa.String(), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.Column("is_deleted", sa.Boolean(), nullable=False),
        sa.Column("deleted_at", sa.DateTime(), nullable=True),
        sa.Column("deleted_by_user_id", sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(
            ["deleted_by_user_id"],
            ["User.id"],
        ),
        sa.ForeignKeyConstraint(
            ["switchboard_id"],
            ["Switchboard.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint(
            "name", "switchboard_id", name="uq_control_circuit_name_swbd"
        ),
    )
    op.create_table(
        "Feeder",
        sa.Column("switchboard_id", sa.Integer(), nullable=False),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("notes", sa.String(), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.Column("is_deleted", sa.Boolean(), nullable=False),
        sa.Column("deleted_at", sa.DateTime(), nullable=True),
        sa.Column("deleted_by_user_id", sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(
            ["deleted_by_user_id"],
            ["User.id"],
        ),
        sa.ForeignKeyConstraint(
            ["switchboard_id"],
            ["Switchboard.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint(
            "switchboard_id", "name", name="uq_feeder_switchboard_name"
        ),
    )
    op.create_table(
        "Pipe",
        sa.Column("project_id", sa.Integer(), nullable=False),
        sa.Column("pipe_material_id", sa.Integer(), nullable=False),
        sa.Column("nominal_diameter_mm", sa.Float(), nullable=True),
        sa.Column("wall_thickness_mm", sa.Float(), nullable=True),
        sa.Column("outer_diameter_mm", sa.Float(), nullable=True),
        sa.Column("length_m", sa.Float(), nullable=False),
        sa.Column("insulation_material_id", sa.Integer(), nullable=False),
        sa.Column("insulation_thickness_mm", sa.Float(), nullable=False),
        sa.Column("fluid_type", sa.String(), nullable=True),
        sa.Column("specific_heat_capacity_jkgc", sa.Float(), nullable=True),
        sa.Column("viscosity_cp", sa.Float(), nullable=True),
        sa.Column("freezing_point_c", sa.Float(), nullable=True),
        sa.Column("calculated_heat_loss_wm", sa.Float(), nullable=True),
        sa.Column("required_heat_output_wm", sa.Float(), nullable=True),
        sa.Column("safety_margin_percent", sa.Float(), nullable=False),
        sa.Column("pid", sa.String(), nullable=True),
        sa.Column("line_tag", sa.String(), nullable=True),
        sa.Column("from_location", sa.String(), nullable=True),
        sa.Column("to_location", sa.String(), nullable=True),
        sa.Column("valve_count", sa.Integer(), nullable=True),
        sa.Column("support_count", sa.Integer(), nullable=True),
        sa.Column("imported_data_revision_id", sa.Integer(), nullable=True),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("notes", sa.String(), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.Column("is_deleted", sa.Boolean(), nullable=False),
        sa.Column("deleted_at", sa.DateTime(), nullable=True),
        sa.Column("deleted_by_user_id", sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(
            ["deleted_by_user_id"],
            ["User.id"],
        ),
        sa.ForeignKeyConstraint(
            ["imported_data_revision_id"],
            ["ImportedDataRevision.id"],
        ),
        sa.ForeignKeyConstraint(
            ["insulation_material_id"],
            ["Component.id"],
        ),
        sa.ForeignKeyConstraint(
            ["pipe_material_id"],
            ["Component.id"],
        ),
        sa.ForeignKeyConstraint(
            ["project_id"],
            ["Project.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("project_id", "name", name="uq_pipe_project_name"),
    )
    op.create_table(
        "SwitchboardComponent",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("switchboard_id", sa.Integer(), nullable=False),
        sa.Column("component_id", sa.Integer(), nullable=False),
        sa.Column("quantity", sa.Integer(), nullable=False),
        sa.Column("position", sa.String(), nullable=True),
        sa.ForeignKeyConstraint(
            ["component_id"],
            ["Component.id"],
        ),
        sa.ForeignKeyConstraint(
            ["switchboard_id"],
            ["Switchboard.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint(
            "switchboard_id", "component_id", "position", name="uq_swbd_comp"
        ),
    )
    op.create_table(
        "Vessel",
        sa.Column("project_id", sa.Integer(), nullable=False),
        sa.Column("material_id", sa.Integer(), nullable=False),
        sa.Column("dimensions_json", sa.String(), nullable=False),
        sa.Column("surface_area_m2", sa.Float(), nullable=False),
        sa.Column("insulation_material_id", sa.Integer(), nullable=False),
        sa.Column("insulation_thickness_mm", sa.Float(), nullable=False),
        sa.Column("fluid_type", sa.String(), nullable=True),
        sa.Column("specific_heat_capacity_jkgc", sa.Float(), nullable=True),
        sa.Column("viscosity_cp", sa.Float(), nullable=True),
        sa.Column("freezing_point_c", sa.Float(), nullable=True),
        sa.Column("calculated_heat_loss_w", sa.Float(), nullable=True),
        sa.Column("required_heat_output_w", sa.Float(), nullable=True),
        sa.Column("safety_margin_percent", sa.Float(), nullable=False),
        sa.Column("pid", sa.String(), nullable=True),
        sa.Column("equipment_tag", sa.String(), nullable=True),
        sa.Column("imported_data_revision_id", sa.Integer(), nullable=True),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("notes", sa.String(), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.Column("is_deleted", sa.Boolean(), nullable=False),
        sa.Column("deleted_at", sa.DateTime(), nullable=True),
        sa.Column("deleted_by_user_id", sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(
            ["deleted_by_user_id"],
            ["User.id"],
        ),
        sa.ForeignKeyConstraint(
            ["imported_data_revision_id"],
            ["ImportedDataRevision.id"],
        ),
        sa.ForeignKeyConstraint(
            ["insulation_material_id"],
            ["Component.id"],
        ),
        sa.ForeignKeyConstraint(
            ["material_id"],
            ["Component.id"],
        ),
        sa.ForeignKeyConstraint(
            ["project_id"],
            ["Project.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("project_id", "name", name="uq_vessel_project_name"),
    )
    op.create_table(
        "ControlCircuitComponent",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("control_circuit_id", sa.Integer(), nullable=False),
        sa.Column("component_id", sa.Integer(), nullable=False),
        sa.Column("quantity", sa.Integer(), nullable=False),
        sa.Column("position", sa.String(), nullable=True),
        sa.ForeignKeyConstraint(
            ["component_id"],
            ["Component.id"],
        ),
        sa.ForeignKeyConstraint(
            ["control_circuit_id"],
            ["ControlCircuit.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint(
            "control_circuit_id", "component_id", "position", name="uq_control_comp"
        ),
    )
    op.create_table(
        "ElectricalNode",
        sa.Column("project_id", sa.Integer(), nullable=False),
        sa.Column("node_type", sa.String(), nullable=False),
        sa.Column("location_description", sa.String(), nullable=True),
        sa.Column("related_switchboard_id", sa.Integer(), nullable=True),
        sa.Column("related_feeder_id", sa.Integer(), nullable=True),
        sa.Column("related_control_circuit_id", sa.Integer(), nullable=True),
        sa.Column("related_pipe_id", sa.Integer(), nullable=True),
        sa.Column("related_vessel_id", sa.Integer(), nullable=True),
        sa.Column("related_component_id", sa.Integer(), nullable=True),
        sa.Column("voltage_v", sa.Float(), nullable=True),
        sa.Column("power_capacity_kva", sa.Float(), nullable=True),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("notes", sa.String(), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.Column("is_deleted", sa.Boolean(), nullable=False),
        sa.Column("deleted_at", sa.DateTime(), nullable=True),
        sa.Column("deleted_by_user_id", sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(
            ["deleted_by_user_id"],
            ["User.id"],
        ),
        sa.ForeignKeyConstraint(
            ["project_id"],
            ["Project.id"],
        ),
        sa.ForeignKeyConstraint(
            ["related_component_id"],
            ["Component.id"],
        ),
        sa.ForeignKeyConstraint(
            ["related_control_circuit_id"],
            ["ControlCircuit.id"],
        ),
        sa.ForeignKeyConstraint(
            ["related_feeder_id"],
            ["Feeder.id"],
        ),
        sa.ForeignKeyConstraint(
            ["related_pipe_id"],
            ["Pipe.id"],
        ),
        sa.ForeignKeyConstraint(
            ["related_switchboard_id"],
            ["Switchboard.id"],
        ),
        sa.ForeignKeyConstraint(
            ["related_vessel_id"],
            ["Vessel.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("related_component_id"),
    )
    op.create_table(
        "FeederComponent",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("feeder_id", sa.Integer(), nullable=False),
        sa.Column("component_id", sa.Integer(), nullable=False),
        sa.Column("quantity", sa.Integer(), nullable=False),
        sa.Column("position", sa.String(), nullable=True),
        sa.ForeignKeyConstraint(
            ["component_id"],
            ["Component.id"],
        ),
        sa.ForeignKeyConstraint(
            ["feeder_id"],
            ["Feeder.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint(
            "feeder_id", "component_id", "position", name="uq_feeder_comp"
        ),
    )
    op.create_table(
        "HTCircuit",
        sa.Column("feeder_id", sa.Integer(), nullable=False),
        sa.Column("heat_tracing_cable_id", sa.Integer(), nullable=False),
        sa.Column("pipe_id", sa.Integer(), nullable=True),
        sa.Column("vessel_id", sa.Integer(), nullable=True),
        sa.Column("control_circuit_id", sa.Integer(), nullable=True),
        sa.Column("calculated_load_amps", sa.Float(), nullable=True),
        sa.Column("calculated_load_kw", sa.Float(), nullable=True),
        sa.Column("required_length_m", sa.Float(), nullable=True),
        sa.Column("number_of_circuits", sa.Integer(), nullable=False),
        sa.Column("isometric_no", sa.String(), nullable=True),
        sa.Column("schedule_no", sa.String(), nullable=True),
        sa.Column("schedule_page", sa.String(), nullable=True),
        sa.Column("schedule_revision", sa.String(), nullable=True),
        sa.Column("application_type", sa.String(), nullable=True),
        sa.Column("heating_method", sa.String(), nullable=True),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("notes", sa.String(), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.Column("is_deleted", sa.Boolean(), nullable=False),
        sa.Column("deleted_at", sa.DateTime(), nullable=True),
        sa.Column("deleted_by_user_id", sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(
            ["control_circuit_id"],
            ["ControlCircuit.id"],
        ),
        sa.ForeignKeyConstraint(
            ["deleted_by_user_id"],
            ["User.id"],
        ),
        sa.ForeignKeyConstraint(
            ["feeder_id"],
            ["Feeder.id"],
        ),
        sa.ForeignKeyConstraint(
            ["heat_tracing_cable_id"],
            ["Component.id"],
        ),
        sa.ForeignKeyConstraint(
            ["pipe_id"],
            ["Pipe.id"],
        ),
        sa.ForeignKeyConstraint(
            ["vessel_id"],
            ["Vessel.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("feeder_id", "name", name="uq_htcircuit_feeder_name"),
    )
    op.create_table(
        "CableRoute",
        sa.Column("project_id", sa.Integer(), nullable=False),
        sa.Column("from_node_id", sa.Integer(), nullable=False),
        sa.Column("to_node_id", sa.Integer(), nullable=False),
        sa.Column("cable_component_id", sa.Integer(), nullable=False),
        sa.Column("length_m", sa.Float(), nullable=False),
        sa.Column("number_of_runs", sa.Integer(), nullable=False),
        sa.Column("installation_method", sa.String(), nullable=False),
        sa.Column("max_ambient_temp_c", sa.Float(), nullable=True),
        sa.Column("min_ambient_temp_c", sa.Float(), nullable=True),
        sa.Column("calculated_voltage_drop_v", sa.Float(), nullable=True),
        sa.Column("calculated_current_capacity_a", sa.Float(), nullable=True),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("notes", sa.String(), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.Column("is_deleted", sa.Boolean(), nullable=False),
        sa.Column("deleted_at", sa.DateTime(), nullable=True),
        sa.Column("deleted_by_user_id", sa.Integer(), nullable=True),
        sa.CheckConstraint("from_node_id != to_node_id", name="chk_different_nodes"),
        sa.ForeignKeyConstraint(
            ["cable_component_id"],
            ["Component.id"],
        ),
        sa.ForeignKeyConstraint(
            ["deleted_by_user_id"],
            ["User.id"],
        ),
        sa.ForeignKeyConstraint(
            ["from_node_id"],
            ["ElectricalNode.id"],
        ),
        sa.ForeignKeyConstraint(
            ["project_id"],
            ["Project.id"],
        ),
        sa.ForeignKeyConstraint(
            ["to_node_id"],
            ["ElectricalNode.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint(
            "project_id",
            "from_node_id",
            "to_node_id",
            "cable_component_id",
            name="uq_cable_route_connection",
        ),
    )
    op.create_table(
        "HTCircuitComponent",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("htcircuit_id", sa.Integer(), nullable=False),
        sa.Column("component_id", sa.Integer(), nullable=False),
        sa.Column("quantity", sa.Integer(), nullable=False),
        sa.Column("position", sa.String(), nullable=True),
        sa.ForeignKeyConstraint(
            ["component_id"],
            ["Component.id"],
        ),
        sa.ForeignKeyConstraint(
            ["htcircuit_id"],
            ["HTCircuit.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint(
            "htcircuit_id", "component_id", "position", name="uq_htc_comp"
        ),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("HTCircuitComponent")
    op.drop_table("CableRoute")
    op.drop_table("HTCircuit")
    op.drop_table("FeederComponent")
    op.drop_table("ElectricalNode")
    op.drop_table("ControlCircuitComponent")
    op.drop_table("Vessel")
    op.drop_table("SwitchboardComponent")
    op.drop_table("Pipe")
    op.drop_table("Feeder")
    op.drop_table("ControlCircuit")
    op.drop_table("Switchboard")
    op.drop_index(
        "uq_active_import_revision",
        table_name="ImportedDataRevision",
    )
    op.drop_table("ImportedDataRevision")
    op.drop_index(
        "uq_latest_export_doc",
        table_name="ExportedDocument",
    )
    op.drop_table("ExportedDocument")
    op.drop_table("Component")
    op.drop_table("UserPreference")
    op.drop_table("Project")
    op.drop_table("ComponentCategory")
    op.drop_table("CalculationStandard")
    op.drop_table("User")
    # ### end Alembic commands ###

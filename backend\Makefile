# Ultimate Electrical Designer Backend - Development Makefile
# 
# This Makefile provides convenient commands for development tasks
# including testing, code quality, and environment management.

.PHONY: help install test test-unit test-integration test-api test-security test-performance
.PHONY: test-coverage test-all lint format type-check security-check quality
.PHONY: clean clean-cache clean-coverage clean-all dev-setup
.PHONY: run-dev run-prod migrate-db create-migration

# Default target
help:
	@echo "Ultimate Electrical Designer Backend - Development Commands"
	@echo ""
	@echo "Environment Setup:"
	@echo "  install          Install all dependencies"
	@echo "  dev-setup        Setup development environment"
	@echo ""
	@echo "Testing Commands:"
	@echo "  test             Run all tests with coverage"
	@echo "  test-unit        Run unit tests"
	@echo "  test-integration Run integration tests"
	@echo "  test-api         Run API tests"
	@echo "  test-security    Run security tests"
	@echo "  test-performance Run performance tests"
	@echo "  test-coverage    Run tests with detailed coverage report"
	@echo "  test-smoke       Run smoke tests"
	@echo ""
	@echo "Test Categories by Layer:"
	@echo "  test-schemas     Run schema validation tests"
	@echo "  test-services    Run service layer tests"
	@echo "  test-repositories Run repository layer tests"
	@echo "  test-database    Run database tests"
	@echo ""
	@echo "Entity-specific Tests:"
	@echo "  test-project     Run project entity tests"
	@echo "  test-component   Run component entity tests"
	@echo "  test-heat-tracing Run heat tracing entity tests"
	@echo "  test-electrical  Run electrical entity tests"
	@echo "  test-switchboard Run switchboard entity tests"
	@echo "  test-user        Run user entity tests"
	@echo "  test-document    Run document entity tests"
	@echo ""
	@echo "Code Quality:"
	@echo "  lint             Run Ruff linting"
	@echo "  format           Run Ruff formatting"
	@echo "  type-check       Run MyPy type checking"
	@echo "  security-check   Run Bandit security analysis"
	@echo "  quality          Run all code quality checks"
	@echo ""
	@echo "Development Server:"
	@echo "  run-dev          Run development server"
	@echo "  run-prod         Run production server"
	@echo ""
	@echo "Database:"
	@echo "  migrate-db       Run database migrations"
	@echo "  create-migration Create new migration"
	@echo ""
	@echo "Cleanup:"
	@echo "  clean            Clean Python cache files"
	@echo "  clean-coverage   Clean coverage reports"
	@echo "  clean-all        Clean everything"

# ============================================================================
# ENVIRONMENT SETUP
# ============================================================================

install:
	@echo "Installing dependencies..."
	pip install -e .[dev]

dev-setup: install
	@echo "Setting up development environment..."
	pre-commit install
	@echo "Development environment ready!"

# ============================================================================
# TESTING COMMANDS
# ============================================================================

# Set environment variables for testing
export PYTHONPATH := .
export DATABASE_URL := sqlite:///:memory:
export TESTING := true

test:
	@python scripts/test_runner.py all --coverage

test-unit:
	@python scripts/test_runner.py unit

test-integration:
	@python scripts/test_runner.py integration

test-api:
	@python scripts/test_runner.py api

test-security:
	@python scripts/test_runner.py security

test-performance:
	@python scripts/test_runner.py performance

test-smoke:
	@python scripts/test_runner.py smoke

test-coverage:
	@python scripts/test_runner.py all --coverage
	@echo "Coverage report generated in htmlcov/index.html"

test-all: test-coverage

# ============================================================================
# LAYER-SPECIFIC TESTS
# ============================================================================

test-schemas:
	@python scripts/test_runner.py schema --coverage

test-services:
	@python scripts/test_runner.py service --coverage

test-repositories:
	@python scripts/test_runner.py repository --coverage

test-database:
	@python scripts/test_runner.py database --coverage

# ============================================================================
# ENTITY-SPECIFIC TESTS
# ============================================================================

test-project:
	@python scripts/test_runner.py project --coverage

test-component:
	@python scripts/test_runner.py component --coverage

test-heat-tracing:
	@python scripts/test_runner.py heat_tracing --coverage

test-electrical:
	@python scripts/test_runner.py electrical --coverage

test-switchboard:
	@python scripts/test_runner.py switchboard --coverage

test-user:
	@python scripts/test_runner.py user --coverage

test-document:
	@python scripts/test_runner.py document --coverage

# ============================================================================
# CODE QUALITY
# ============================================================================

lint:
	@echo "Running Ruff linting..."
	ruff check . --fix

format:
	@echo "Running Ruff formatting..."
	ruff format .

type-check:
	@echo "Running MyPy type checking..."
	mypy core api config

security-check:
	@echo "Running Bandit security analysis..."
	bandit -r core api config -f json -o bandit-report.json
	@echo "Security report generated: bandit-report.json"

quality: lint format type-check security-check
	@echo "All code quality checks completed!"

# ============================================================================
# DEVELOPMENT SERVER
# ============================================================================

run-dev:
	@echo "Starting development server..."
	uvicorn main:app --reload --host 0.0.0.0 --port 8000

run-prod:
	@echo "Starting production server..."
	uvicorn main:app --host 0.0.0.0 --port 8000

# ============================================================================
# DATABASE MANAGEMENT
# ============================================================================

migrate-db:
	@echo "Running database migrations..."
	alembic upgrade head

create-migration:
	@echo "Creating new migration..."
	@read -p "Enter migration message: " message; \
	alembic revision --autogenerate -m "$$message"

# ============================================================================
# CLEANUP COMMANDS
# ============================================================================

clean:
	@echo "Cleaning Python cache files..."
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +

clean-coverage:
	@echo "Cleaning coverage reports..."
	rm -rf htmlcov/
	rm -f coverage.xml
	rm -f .coverage

clean-cache:
	@echo "Cleaning cache directories..."
	rm -rf .pytest_cache/
	rm -rf .mypy_cache/
	rm -rf .ruff_cache/

clean-all: clean clean-coverage clean-cache
	@echo "All cleanup completed!"

# ============================================================================
# CONTINUOUS INTEGRATION COMMANDS
# ============================================================================

ci-test:
	@echo "Running CI test suite..."
	python scripts/test_runner.py all --coverage
	python scripts/test_runner.py quality

ci-security:
	@echo "Running CI security checks..."
	python scripts/test_runner.py security
	bandit -r core api config
	safety check

# ============================================================================
# DEVELOPMENT UTILITIES
# ============================================================================

check-deps:
	@echo "Checking for outdated dependencies..."
	pip list --outdated

update-deps:
	@echo "Updating dependencies..."
	pip install --upgrade pip
	pip install --upgrade -e .[dev]

# ============================================================================
# DOCUMENTATION
# ============================================================================

docs-serve:
	@echo "Serving documentation..."
	mkdocs serve

docs-build:
	@echo "Building documentation..."
	mkdocs build

# ============================================================================
# QUICK DEVELOPMENT WORKFLOWS
# ============================================================================

# Quick check before committing
pre-commit: quality test-smoke
	@echo "Pre-commit checks completed!"

# Full validation before merging
pre-merge: quality test-all security-check
	@echo "Pre-merge validation completed!"

# Test Suite Quality Improvement workflow
test-improvement:
	@echo "Running Test Suite Quality Improvement validation..."
	@echo "Phase 1: Schema validation tests..."
	@python scripts/test_runner.py schema --coverage
	@echo "Phase 2: Database tests..."
	@python scripts/test_runner.py database --coverage
	@echo "Phase 3: Service tests..."
	@python scripts/test_runner.py service --coverage
	@echo "Phase 4: API tests..."
	@python scripts/test_runner.py api --coverage
	@echo "Final validation: All tests..."
	@python scripts/test_runner.py all --coverage
	@echo "Test Suite Quality Improvement validation completed!"

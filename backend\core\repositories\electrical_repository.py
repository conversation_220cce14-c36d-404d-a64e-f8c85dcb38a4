# backend/core/repositories/electrical_repository.py
"""
Electrical Repository

This module provides data access layer for Electrical entities, extending the base
repository with electrical-specific query methods and operations.
"""

from typing import Any, Dict, List, Optional

from sqlalchemy import and_, func, select, update
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session

try:
    from config.logging_config import get_logger
    from core.models.electrical import (
        CableRoute,
        CableSegment,
        ElectricalNode,
        LoadCalculation,
        VoltageDropCalculation,
    )
    from core.repositories.base_repository import BaseRepository
except ImportError:
    # For testing and relative imports
    import sys
    import os

    sys.path.insert(
        0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    )
    from config.logging_config import get_logger
    from core.models.electrical import (
        CableRoute,
        CableSegment,
        ElectricalNode,
        LoadCalculation,
        VoltageDropCalculation,
    )
    from core.repositories.base_repository import BaseRepository

# Initialize logger for this module
logger = get_logger(__name__)


class ElectricalNodeRepository(BaseRepository[ElectricalNode]):
    """
    Repository for ElectricalNode entity data access operations.

    Extends BaseRepository with electrical node-specific query methods and
    enhanced error handling for electrical node operations.
    """

    def __init__(self, db_session: Session):
        """
        Initialize the ElectricalNode repository.

        Args:
            db_session: SQLAlchemy database session
        """
        super().__init__(db_session, ElectricalNode)
        logger.debug("ElectricalNodeRepository initialized")

    def get_by_project_id(
        self, project_id: int, skip: int = 0, limit: int = 100
    ) -> List[ElectricalNode]:
        """
        Get electrical nodes by project ID.

        Args:
            project_id: Project ID to filter by
            skip: Number of records to skip (for pagination)
            limit: Maximum number of records to return

        Returns:
            List[ElectricalNode]: List of electrical nodes in the project

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(
            f"Retrieving electrical nodes for project {project_id}: skip={skip}, limit={limit}"
        )

        try:
            stmt = (
                select(self.model)
                .where(
                    and_(
                        self.model.project_id == project_id,
                        self.model.is_deleted == False,
                    )
                )
                .offset(skip)
                .limit(limit)
                .order_by(self.model.name)
            )
            results = list(self.db_session.scalars(stmt).all())

            logger.debug(
                f"Retrieved {len(results)} electrical nodes for project {project_id}"
            )
            return results

        except SQLAlchemyError as e:
            logger.error(
                f"Database error retrieving electrical nodes for project {project_id}: {e}"
            )
            self._handle_db_exception(e, "electrical_node")
            raise  # This will never be reached due to _handle_db_exception raising

    def get_by_node_type(
        self, project_id: int, node_type: str, skip: int = 0, limit: int = 100
    ) -> List[ElectricalNode]:
        """
        Get electrical nodes by type within a project.

        Args:
            project_id: Project ID
            node_type: Node type to filter by
            skip: Number of records to skip (for pagination)
            limit: Maximum number of records to return

        Returns:
            List[ElectricalNode]: List of electrical nodes of specified type

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(
            f"Searching for electrical nodes with type: {node_type} in project {project_id}"
        )

        try:
            stmt = (
                select(self.model)
                .where(
                    and_(
                        self.model.project_id == project_id,
                        self.model.node_type == node_type,
                        self.model.is_deleted == False,
                    )
                )
                .offset(skip)
                .limit(limit)
                .order_by(self.model.name)
            )
            results = list(self.db_session.scalars(stmt).all())

            logger.debug(
                f"Found {len(results)} electrical nodes with type: {node_type}"
            )
            return results

        except SQLAlchemyError as e:
            logger.error(
                f"Database error searching for electrical nodes by type {node_type}: {e}"
            )
            self._handle_db_exception(e, "electrical_node")
            raise

    def get_nodes_with_capacity(
        self, project_id: int, min_capacity_kva: float = 0.0
    ) -> List[ElectricalNode]:
        """
        Get electrical nodes with power capacity above threshold.

        Args:
            project_id: Project ID to filter by
            min_capacity_kva: Minimum power capacity in kVA

        Returns:
            List[ElectricalNode]: List of electrical nodes with sufficient capacity

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(
            f"Retrieving electrical nodes with capacity >= {min_capacity_kva}kVA for project {project_id}"
        )

        try:
            stmt = (
                select(self.model)
                .where(
                    and_(
                        self.model.project_id == project_id,
                        self.model.is_deleted == False,
                        self.model.power_capacity_kva.isnot(None),
                        self.model.power_capacity_kva >= min_capacity_kva,
                    )
                )
                .order_by(self.model.power_capacity_kva.desc())
            )
            results = list(self.db_session.scalars(stmt).all())

            logger.debug(
                f"Found {len(results)} electrical nodes with sufficient capacity"
            )
            return results

        except SQLAlchemyError as e:
            logger.error(
                f"Database error retrieving electrical nodes with capacity: {e}"
            )
            self._handle_db_exception(e, "electrical_node")
            raise

    def get_nodes_without_routes(self, project_id: int) -> List[ElectricalNode]:
        """
        Get electrical nodes that don't have cable routes connected.

        Args:
            project_id: Project ID to filter by

        Returns:
            List[ElectricalNode]: List of electrical nodes without routes

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(
            f"Retrieving electrical nodes without routes for project {project_id}"
        )

        try:
            stmt = (
                select(self.model)
                .where(
                    and_(
                        self.model.project_id == project_id,
                        self.model.is_deleted == False,
                        ~self.model.outgoing_routes.has(),  # No outgoing routes
                        ~self.model.incoming_routes.has(),  # No incoming routes
                    )
                )
                .order_by(self.model.name)
            )
            results = list(self.db_session.scalars(stmt).all())

            logger.debug(f"Found {len(results)} electrical nodes without routes")
            return results

        except SQLAlchemyError as e:
            logger.error(
                f"Database error retrieving electrical nodes without routes: {e}"
            )
            self._handle_db_exception(e, "electrical_node")
            raise

    def count_by_project(self, project_id: int) -> int:
        """
        Count total number of active electrical nodes in a project.

        Args:
            project_id: Project ID

        Returns:
            int: Number of active electrical nodes

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Counting electrical nodes for project {project_id}")

        try:
            stmt = select(func.count(self.model.id)).where(
                and_(
                    self.model.project_id == project_id, self.model.is_deleted == False
                )
            )
            result = self.db_session.scalar(stmt)

            logger.debug(f"Total electrical nodes in project {project_id}: {result}")
            return result or 0

        except SQLAlchemyError as e:
            logger.error(
                f"Database error counting electrical nodes for project {project_id}: {e}"
            )
            self._handle_db_exception(e, "electrical_node")
            raise

    def update(
        self, node_id: int, update_data: Dict[str, Any]
    ) -> Optional[ElectricalNode]:
        """
        Update electrical node with given data.

        Args:
            node_id: ID of electrical node to update
            update_data: Dictionary of fields to update

        Returns:
            Optional[ElectricalNode]: Updated electrical node or None if not found

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(
            f"Updating electrical node {node_id} with data: {list(update_data.keys())}"
        )

        try:
            # First check if node exists
            existing_node = self.get_by_id(node_id)
            if existing_node is None:
                logger.debug(f"Electrical node {node_id} not found for update")
                return None

            # Update the node
            stmt = (
                update(self.model).where(self.model.id == node_id).values(**update_data)
            )
            self.db_session.execute(stmt)

            # Return updated node
            updated_node = self.get_by_id(node_id)
            logger.debug(f"Electrical node {node_id} updated successfully")
            return updated_node

        except SQLAlchemyError as e:
            logger.error(f"Database error updating electrical node {node_id}: {e}")
            self._handle_db_exception(e, "electrical_node")
            raise

    def soft_delete(
        self, node_id: int, deleted_by_user_id: Optional[int] = None
    ) -> bool:
        """
        Soft delete an electrical node.

        Args:
            node_id: ID of electrical node to delete
            deleted_by_user_id: ID of user performing the deletion

        Returns:
            bool: True if deletion was successful, False if node not found

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Soft deleting electrical node {node_id}")

        try:
            from datetime import datetime, timezone

            update_data = {
                "is_deleted": True,
                "deleted_at": datetime.now(timezone.utc),
                "deleted_by_user_id": deleted_by_user_id,
            }

            stmt = (
                update(self.model)
                .where(and_(self.model.id == node_id, self.model.is_deleted == False))
                .values(**update_data)
            )

            result = self.db_session.execute(stmt)

            if result.rowcount > 0:
                logger.debug(f"Electrical node {node_id} soft deleted successfully")
                return True
            else:
                logger.debug(f"Electrical node {node_id} not found or already deleted")
                return False

        except SQLAlchemyError as e:
            logger.error(f"Database error soft deleting electrical node {node_id}: {e}")
            self._handle_db_exception(e, "electrical_node")
            raise


class CableRouteRepository(BaseRepository[CableRoute]):
    """
    Repository for CableRoute entity data access operations.

    Extends BaseRepository with cable route-specific query methods and
    enhanced error handling for cable route operations.
    """

    def __init__(self, db_session: Session):
        """
        Initialize the CableRoute repository.

        Args:
            db_session: SQLAlchemy database session
        """
        super().__init__(db_session, CableRoute)
        logger.debug("CableRouteRepository initialized")

    def get_by_project_id(
        self, project_id: int, skip: int = 0, limit: int = 100
    ) -> List[CableRoute]:
        """
        Get cable routes by project ID.

        Args:
            project_id: Project ID to filter by
            skip: Number of records to skip (for pagination)
            limit: Maximum number of records to return

        Returns:
            List[CableRoute]: List of cable routes in the project

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(
            f"Retrieving cable routes for project {project_id}: skip={skip}, limit={limit}"
        )

        try:
            stmt = (
                select(self.model)
                .where(
                    and_(
                        self.model.project_id == project_id,
                        self.model.is_deleted == False,
                    )
                )
                .offset(skip)
                .limit(limit)
                .order_by(self.model.name)
            )
            results = list(self.db_session.scalars(stmt).all())

            logger.debug(
                f"Retrieved {len(results)} cable routes for project {project_id}"
            )
            return results

        except SQLAlchemyError as e:
            logger.error(
                f"Database error retrieving cable routes for project {project_id}: {e}"
            )
            self._handle_db_exception(e, "cable_route")
            raise

    def get_routes_by_node(
        self, node_id: int, direction: str = "both"
    ) -> List[CableRoute]:
        """
        Get cable routes connected to a specific electrical node.

        Args:
            node_id: Electrical node ID
            direction: Direction filter ("incoming", "outgoing", or "both")

        Returns:
            List[CableRoute]: List of cable routes connected to the node

        Raises:
            DatabaseError: If database operation fails
            ValueError: If invalid direction specified
        """
        if direction not in ["incoming", "outgoing", "both"]:
            raise ValueError("Direction must be 'incoming', 'outgoing', or 'both'")

        logger.debug(f"Retrieving {direction} cable routes for node {node_id}")

        try:
            if direction == "incoming":
                condition = self.model.to_node_id == node_id
            elif direction == "outgoing":
                condition = self.model.from_node_id == node_id
            else:  # both
                condition = (self.model.from_node_id == node_id) | (
                    self.model.to_node_id == node_id
                )

            stmt = (
                select(self.model)
                .where(and_(condition, self.model.is_deleted == False))
                .order_by(self.model.name)
            )
            results = list(self.db_session.scalars(stmt).all())

            logger.debug(
                f"Found {len(results)} {direction} cable routes for node {node_id}"
            )
            return results

        except SQLAlchemyError as e:
            logger.error(
                f"Database error retrieving cable routes for node {node_id}: {e}"
            )
            self._handle_db_exception(e, "cable_route")
            raise

    def get_routes_by_installation_method(
        self, project_id: int, installation_method: str
    ) -> List[CableRoute]:
        """
        Get cable routes by installation method.

        Args:
            project_id: Project ID to filter by
            installation_method: Installation method to filter by

        Returns:
            List[CableRoute]: List of cable routes with specified installation method

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(
            f"Retrieving cable routes with installation method: {installation_method} for project {project_id}"
        )

        try:
            stmt = (
                select(self.model)
                .where(
                    and_(
                        self.model.project_id == project_id,
                        self.model.installation_method == installation_method,
                        self.model.is_deleted == False,
                    )
                )
                .order_by(self.model.name)
            )
            results = list(self.db_session.scalars(stmt).all())

            logger.debug(
                f"Found {len(results)} cable routes with installation method: {installation_method}"
            )
            return results

        except SQLAlchemyError as e:
            logger.error(
                f"Database error retrieving cable routes by installation method {installation_method}: {e}"
            )
            self._handle_db_exception(e, "cable_route")
            raise

    def get_routes_with_high_voltage_drop(
        self, project_id: int, max_voltage_drop_percent: float = 5.0
    ) -> List[CableRoute]:
        """
        Get cable routes with voltage drop above threshold.

        Args:
            project_id: Project ID to filter by
            max_voltage_drop_percent: Maximum allowed voltage drop percentage

        Returns:
            List[CableRoute]: List of cable routes with high voltage drop

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(
            f"Retrieving cable routes with voltage drop > {max_voltage_drop_percent}% for project {project_id}"
        )

        try:
            # Calculate voltage drop percentage from voltage drop in volts
            # This is a simplified calculation - in practice, you'd need the supply voltage
            stmt = (
                select(self.model)
                .where(
                    and_(
                        self.model.project_id == project_id,
                        self.model.is_deleted == False,
                        self.model.calculated_voltage_drop_v.isnot(None),
                        # Assuming 240V supply for percentage calculation
                        (self.model.calculated_voltage_drop_v / 240.0 * 100)
                        > max_voltage_drop_percent,
                    )
                )
                .order_by(self.model.calculated_voltage_drop_v.desc())
            )
            results = list(self.db_session.scalars(stmt).all())

            logger.debug(f"Found {len(results)} cable routes with high voltage drop")
            return results

        except SQLAlchemyError as e:
            logger.error(
                f"Database error retrieving cable routes with high voltage drop: {e}"
            )
            self._handle_db_exception(e, "cable_route")
            raise

    def count_by_project(self, project_id: int) -> int:
        """
        Count total number of active cable routes in a project.

        Args:
            project_id: Project ID

        Returns:
            int: Number of active cable routes

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Counting cable routes for project {project_id}")

        try:
            stmt = select(func.count(self.model.id)).where(
                and_(
                    self.model.project_id == project_id, self.model.is_deleted == False
                )
            )
            result = self.db_session.scalar(stmt)

            logger.debug(f"Total cable routes in project {project_id}: {result}")
            return result or 0

        except SQLAlchemyError as e:
            logger.error(
                f"Database error counting cable routes for project {project_id}: {e}"
            )
            self._handle_db_exception(e, "cable_route")
            raise

    def update(
        self, route_id: int, update_data: Dict[str, Any]
    ) -> Optional[CableRoute]:
        """
        Update cable route with given data.

        Args:
            route_id: ID of cable route to update
            update_data: Dictionary of fields to update

        Returns:
            Optional[CableRoute]: Updated cable route or None if not found

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(
            f"Updating cable route {route_id} with data: {list(update_data.keys())}"
        )

        try:
            # First check if route exists
            existing_route = self.get_by_id(route_id)
            if existing_route is None:
                logger.debug(f"Cable route {route_id} not found for update")
                return None

            # Update the route
            stmt = (
                update(self.model)
                .where(self.model.id == route_id)
                .values(**update_data)
            )
            self.db_session.execute(stmt)

            # Return updated route
            updated_route = self.get_by_id(route_id)
            logger.debug(f"Cable route {route_id} updated successfully")
            return updated_route

        except SQLAlchemyError as e:
            logger.error(f"Database error updating cable route {route_id}: {e}")
            self._handle_db_exception(e, "cable_route")
            raise

    def soft_delete(
        self, route_id: int, deleted_by_user_id: Optional[int] = None
    ) -> bool:
        """
        Soft delete a cable route.

        Args:
            route_id: ID of cable route to delete
            deleted_by_user_id: ID of user performing the deletion

        Returns:
            bool: True if deletion was successful, False if route not found

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Soft deleting cable route {route_id}")

        try:
            from datetime import datetime, timezone

            update_data = {
                "is_deleted": True,
                "deleted_at": datetime.now(timezone.utc),
                "deleted_by_user_id": deleted_by_user_id,
            }

            stmt = (
                update(self.model)
                .where(and_(self.model.id == route_id, self.model.is_deleted == False))
                .values(**update_data)
            )

            result = self.db_session.execute(stmt)

            if result.rowcount > 0:
                logger.debug(f"Cable route {route_id} soft deleted successfully")
                return True
            else:
                logger.debug(f"Cable route {route_id} not found or already deleted")
                return False

        except SQLAlchemyError as e:
            logger.error(f"Database error soft deleting cable route {route_id}: {e}")
            self._handle_db_exception(e, "cable_route")
            raise


class CableSegmentRepository(BaseRepository[CableSegment]):
    """
    Repository for CableSegment entity data access operations.

    Extends BaseRepository with cable segment-specific query methods and
    enhanced error handling for cable segment operations.
    """

    def __init__(self, db_session: Session):
        """
        Initialize the CableSegment repository.

        Args:
            db_session: SQLAlchemy database session
        """
        super().__init__(db_session, CableSegment)
        logger.debug("CableSegmentRepository initialized")

    def get_by_cable_route_id(
        self, cable_route_id: int, skip: int = 0, limit: int = 100
    ) -> List[CableSegment]:
        """
        Get cable segments by cable route ID, ordered by segment order.

        Args:
            cable_route_id: Cable route ID to filter by
            skip: Number of records to skip (for pagination)
            limit: Maximum number of records to return

        Returns:
            List[CableSegment]: List of cable segments in the route

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(
            f"Retrieving cable segments for route {cable_route_id}: skip={skip}, limit={limit}"
        )

        try:
            stmt = (
                select(self.model)
                .where(
                    and_(
                        self.model.cable_route_id == cable_route_id,
                        self.model.is_deleted == False,
                    )
                )
                .offset(skip)
                .limit(limit)
                .order_by(self.model.segment_order)
            )
            results = list(self.db_session.scalars(stmt).all())

            logger.debug(
                f"Retrieved {len(results)} cable segments for route {cable_route_id}"
            )
            return results

        except SQLAlchemyError as e:
            logger.error(
                f"Database error retrieving cable segments for route {cable_route_id}: {e}"
            )
            self._handle_db_exception(e, "cable_segment")
            raise

    def get_by_project_id(
        self, project_id: int, skip: int = 0, limit: int = 100
    ) -> List[CableSegment]:
        """
        Get cable segments by project ID.

        Args:
            project_id: Project ID to filter by
            skip: Number of records to skip (for pagination)
            limit: Maximum number of records to return

        Returns:
            List[CableSegment]: List of cable segments in the project

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(
            f"Retrieving cable segments for project {project_id}: skip={skip}, limit={limit}"
        )

        try:
            stmt = (
                select(self.model)
                .where(
                    and_(
                        self.model.project_id == project_id,
                        self.model.is_deleted == False,
                    )
                )
                .offset(skip)
                .limit(limit)
                .order_by(self.model.cable_route_id, self.model.segment_order)
            )
            results = list(self.db_session.scalars(stmt).all())

            logger.debug(
                f"Retrieved {len(results)} cable segments for project {project_id}"
            )
            return results

        except SQLAlchemyError as e:
            logger.error(
                f"Database error retrieving cable segments for project {project_id}: {e}"
            )
            self._handle_db_exception(e, "cable_segment")
            raise

    def get_segments_by_installation_method(
        self, project_id: int, installation_method: str
    ) -> List[CableSegment]:
        """
        Get cable segments by installation method.

        Args:
            project_id: Project ID to filter by
            installation_method: Installation method to filter by

        Returns:
            List[CableSegment]: List of cable segments with specified installation method

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(
            f"Retrieving cable segments with installation method: {installation_method} for project {project_id}"
        )

        try:
            stmt = (
                select(self.model)
                .where(
                    and_(
                        self.model.project_id == project_id,
                        self.model.installation_method == installation_method,
                        self.model.is_deleted == False,
                    )
                )
                .order_by(self.model.cable_route_id, self.model.segment_order)
            )
            results = list(self.db_session.scalars(stmt).all())

            logger.debug(
                f"Found {len(results)} cable segments with installation method: {installation_method}"
            )
            return results

        except SQLAlchemyError as e:
            logger.error(
                f"Database error retrieving cable segments by installation method {installation_method}: {e}"
            )
            self._handle_db_exception(e, "cable_segment")
            raise

    def count_by_cable_route(self, cable_route_id: int) -> int:
        """
        Count total number of active cable segments in a cable route.

        Args:
            cable_route_id: Cable route ID

        Returns:
            int: Number of active cable segments

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Counting cable segments for route {cable_route_id}")

        try:
            stmt = select(func.count(self.model.id)).where(
                and_(
                    self.model.cable_route_id == cable_route_id,
                    self.model.is_deleted == False,
                )
            )
            result = self.db_session.scalar(stmt)

            logger.debug(f"Total cable segments in route {cable_route_id}: {result}")
            return result or 0

        except SQLAlchemyError as e:
            logger.error(
                f"Database error counting cable segments for route {cable_route_id}: {e}"
            )
            self._handle_db_exception(e, "cable_segment")
            raise


class LoadCalculationRepository(BaseRepository[LoadCalculation]):
    """
    Repository for LoadCalculation entity data access operations.

    Extends BaseRepository with load calculation-specific query methods and
    enhanced error handling for load calculation operations.
    """

    def __init__(self, db_session: Session):
        """
        Initialize the LoadCalculation repository.

        Args:
            db_session: SQLAlchemy database session
        """
        super().__init__(db_session, LoadCalculation)
        logger.debug("LoadCalculationRepository initialized")

    def get_by_project_id(
        self, project_id: int, skip: int = 0, limit: int = 100
    ) -> List[LoadCalculation]:
        """
        Get load calculations by project ID.

        Args:
            project_id: Project ID to filter by
            skip: Number of records to skip (for pagination)
            limit: Maximum number of records to return

        Returns:
            List[LoadCalculation]: List of load calculations in the project

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(
            f"Retrieving load calculations for project {project_id}: skip={skip}, limit={limit}"
        )

        try:
            stmt = (
                select(self.model)
                .where(
                    and_(
                        self.model.project_id == project_id,
                        self.model.is_deleted == False,
                    )
                )
                .offset(skip)
                .limit(limit)
                .order_by(self.model.name)
            )
            results = list(self.db_session.scalars(stmt).all())

            logger.debug(
                f"Retrieved {len(results)} load calculations for project {project_id}"
            )
            return results

        except SQLAlchemyError as e:
            logger.error(
                f"Database error retrieving load calculations for project {project_id}: {e}"
            )
            self._handle_db_exception(e, "load_calculation")
            raise

    def get_by_electrical_node_id(
        self, electrical_node_id: int, skip: int = 0, limit: int = 100
    ) -> List[LoadCalculation]:
        """
        Get load calculations by electrical node ID.

        Args:
            electrical_node_id: Electrical node ID to filter by
            skip: Number of records to skip (for pagination)
            limit: Maximum number of records to return

        Returns:
            List[LoadCalculation]: List of load calculations for the electrical node

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(
            f"Retrieving load calculations for electrical node {electrical_node_id}: skip={skip}, limit={limit}"
        )

        try:
            stmt = (
                select(self.model)
                .where(
                    and_(
                        self.model.electrical_node_id == electrical_node_id,
                        self.model.is_deleted == False,
                    )
                )
                .offset(skip)
                .limit(limit)
                .order_by(self.model.name)
            )
            results = list(self.db_session.scalars(stmt).all())

            logger.debug(
                f"Retrieved {len(results)} load calculations for electrical node {electrical_node_id}"
            )
            return results

        except SQLAlchemyError as e:
            logger.error(
                f"Database error retrieving load calculations for electrical node {electrical_node_id}: {e}"
            )
            self._handle_db_exception(e, "load_calculation")
            raise

    def get_by_load_type(
        self, project_id: int, load_type: str, skip: int = 0, limit: int = 100
    ) -> List[LoadCalculation]:
        """
        Get load calculations by load type.

        Args:
            project_id: Project ID to filter by
            load_type: Load type to filter by
            skip: Number of records to skip (for pagination)
            limit: Maximum number of records to return

        Returns:
            List[LoadCalculation]: List of load calculations of specified type

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(
            f"Retrieving load calculations with type: {load_type} for project {project_id}"
        )

        try:
            stmt = (
                select(self.model)
                .where(
                    and_(
                        self.model.project_id == project_id,
                        self.model.load_type == load_type,
                        self.model.is_deleted == False,
                    )
                )
                .offset(skip)
                .limit(limit)
                .order_by(self.model.name)
            )
            results = list(self.db_session.scalars(stmt).all())

            logger.debug(
                f"Found {len(results)} load calculations with type: {load_type}"
            )
            return results

        except SQLAlchemyError as e:
            logger.error(
                f"Database error retrieving load calculations by type {load_type}: {e}"
            )
            self._handle_db_exception(e, "load_calculation")
            raise

    def get_total_power_by_node(self, electrical_node_id: int) -> float:
        """
        Get total power consumption for an electrical node.

        Args:
            electrical_node_id: Electrical node ID

        Returns:
            float: Total power consumption in kW

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(
            f"Calculating total power for electrical node {electrical_node_id}"
        )

        try:
            stmt = select(func.sum(self.model.calculated_operating_power_kw)).where(
                and_(
                    self.model.electrical_node_id == electrical_node_id,
                    self.model.is_deleted == False,
                    self.model.calculated_operating_power_kw.isnot(None),
                )
            )
            result = self.db_session.scalar(stmt)

            total_power = result or 0.0
            logger.debug(
                f"Total power for electrical node {electrical_node_id}: {total_power}kW"
            )
            return total_power

        except SQLAlchemyError as e:
            logger.error(
                f"Database error calculating total power for electrical node {electrical_node_id}: {e}"
            )
            self._handle_db_exception(e, "load_calculation")
            raise


class VoltageDropCalculationRepository(BaseRepository[VoltageDropCalculation]):
    """
    Repository for VoltageDropCalculation entity data access operations.

    Extends BaseRepository with voltage drop calculation-specific query methods and
    enhanced error handling for voltage drop calculation operations.
    """

    def __init__(self, db_session: Session):
        """
        Initialize the VoltageDropCalculation repository.

        Args:
            db_session: SQLAlchemy database session
        """
        super().__init__(db_session, VoltageDropCalculation)
        logger.debug("VoltageDropCalculationRepository initialized")

    def get_by_project_id(
        self, project_id: int, skip: int = 0, limit: int = 100
    ) -> List[VoltageDropCalculation]:
        """
        Get voltage drop calculations by project ID.

        Args:
            project_id: Project ID to filter by
            skip: Number of records to skip (for pagination)
            limit: Maximum number of records to return

        Returns:
            List[VoltageDropCalculation]: List of voltage drop calculations in the project

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(
            f"Retrieving voltage drop calculations for project {project_id}: skip={skip}, limit={limit}"
        )

        try:
            stmt = (
                select(self.model)
                .where(
                    and_(
                        self.model.project_id == project_id,
                        self.model.is_deleted == False,
                    )
                )
                .offset(skip)
                .limit(limit)
                .order_by(self.model.name)
            )
            results = list(self.db_session.scalars(stmt).all())

            logger.debug(
                f"Retrieved {len(results)} voltage drop calculations for project {project_id}"
            )
            return results

        except SQLAlchemyError as e:
            logger.error(
                f"Database error retrieving voltage drop calculations for project {project_id}: {e}"
            )
            self._handle_db_exception(e, "voltage_drop_calculation")
            raise

    def get_by_cable_route_id(
        self, cable_route_id: int, skip: int = 0, limit: int = 100
    ) -> List[VoltageDropCalculation]:
        """
        Get voltage drop calculations by cable route ID.

        Args:
            cable_route_id: Cable route ID to filter by
            skip: Number of records to skip (for pagination)
            limit: Maximum number of records to return

        Returns:
            List[VoltageDropCalculation]: List of voltage drop calculations for the cable route

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(
            f"Retrieving voltage drop calculations for cable route {cable_route_id}: skip={skip}, limit={limit}"
        )

        try:
            stmt = (
                select(self.model)
                .where(
                    and_(
                        self.model.cable_route_id == cable_route_id,
                        self.model.is_deleted == False,
                    )
                )
                .offset(skip)
                .limit(limit)
                .order_by(self.model.name)
            )
            results = list(self.db_session.scalars(stmt).all())

            logger.debug(
                f"Retrieved {len(results)} voltage drop calculations for cable route {cable_route_id}"
            )
            return results

        except SQLAlchemyError as e:
            logger.error(
                f"Database error retrieving voltage drop calculations for cable route {cable_route_id}: {e}"
            )
            self._handle_db_exception(e, "voltage_drop_calculation")
            raise

    def get_non_compliant_calculations(
        self, project_id: int, skip: int = 0, limit: int = 100
    ) -> List[VoltageDropCalculation]:
        """
        Get voltage drop calculations that are not compliant with standards.

        Args:
            project_id: Project ID to filter by
            skip: Number of records to skip (for pagination)
            limit: Maximum number of records to return

        Returns:
            List[VoltageDropCalculation]: List of non-compliant voltage drop calculations

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(
            f"Retrieving non-compliant voltage drop calculations for project {project_id}"
        )

        try:
            stmt = (
                select(self.model)
                .where(
                    and_(
                        self.model.project_id == project_id,
                        self.model.is_deleted == False,
                        self.model.is_compliant == False,
                    )
                )
                .offset(skip)
                .limit(limit)
                .order_by(self.model.calculated_voltage_drop_percent.desc())
            )
            results = list(self.db_session.scalars(stmt).all())

            logger.debug(
                f"Found {len(results)} non-compliant voltage drop calculations for project {project_id}"
            )
            return results

        except SQLAlchemyError as e:
            logger.error(
                f"Database error retrieving non-compliant voltage drop calculations for project {project_id}: {e}"
            )
            self._handle_db_exception(e, "voltage_drop_calculation")
            raise

    def get_calculations_by_method(
        self, project_id: int, calculation_method: str, skip: int = 0, limit: int = 100
    ) -> List[VoltageDropCalculation]:
        """
        Get voltage drop calculations by calculation method.

        Args:
            project_id: Project ID to filter by
            calculation_method: Calculation method to filter by
            skip: Number of records to skip (for pagination)
            limit: Maximum number of records to return

        Returns:
            List[VoltageDropCalculation]: List of voltage drop calculations using specified method

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(
            f"Retrieving voltage drop calculations with method: {calculation_method} for project {project_id}"
        )

        try:
            stmt = (
                select(self.model)
                .where(
                    and_(
                        self.model.project_id == project_id,
                        self.model.calculation_method == calculation_method,
                        self.model.is_deleted == False,
                    )
                )
                .offset(skip)
                .limit(limit)
                .order_by(self.model.name)
            )
            results = list(self.db_session.scalars(stmt).all())

            logger.debug(
                f"Found {len(results)} voltage drop calculations with method: {calculation_method}"
            )
            return results

        except SQLAlchemyError as e:
            logger.error(
                f"Database error retrieving voltage drop calculations by method {calculation_method}: {e}"
            )
            self._handle_db_exception(e, "voltage_drop_calculation")
            raise

    def get_average_voltage_drop_by_project(self, project_id: int) -> float:
        """
        Get average voltage drop percentage for a project.

        Args:
            project_id: Project ID

        Returns:
            float: Average voltage drop percentage

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Calculating average voltage drop for project {project_id}")

        try:
            stmt = select(func.avg(self.model.calculated_voltage_drop_percent)).where(
                and_(
                    self.model.project_id == project_id,
                    self.model.is_deleted == False,
                    self.model.calculated_voltage_drop_percent.isnot(None),
                )
            )
            result = self.db_session.scalar(stmt)

            avg_voltage_drop = result or 0.0
            logger.debug(
                f"Average voltage drop for project {project_id}: {avg_voltage_drop}%"
            )
            return avg_voltage_drop

        except SQLAlchemyError as e:
            logger.error(
                f"Database error calculating average voltage drop for project {project_id}: {e}"
            )
            self._handle_db_exception(e, "voltage_drop_calculation")
            raise

    def count_by_compliance_status(self, project_id: int, is_compliant: bool) -> int:
        """
        Count voltage drop calculations by compliance status.

        Args:
            project_id: Project ID
            is_compliant: Compliance status to count

        Returns:
            int: Number of calculations with specified compliance status

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(
            f"Counting {'compliant' if is_compliant else 'non-compliant'} voltage drop calculations for project {project_id}"
        )

        try:
            stmt = select(func.count(self.model.id)).where(
                and_(
                    self.model.project_id == project_id,
                    self.model.is_deleted == False,
                    self.model.is_compliant == is_compliant,
                )
            )
            result = self.db_session.scalar(stmt)

            count = result or 0
            logger.debug(
                f"{'Compliant' if is_compliant else 'Non-compliant'} voltage drop calculations in project {project_id}: {count}"
            )
            return count

        except SQLAlchemyError as e:
            logger.error(
                f"Database error counting voltage drop calculations by compliance for project {project_id}: {e}"
            )
            self._handle_db_exception(e, "voltage_drop_calculation")
            raise

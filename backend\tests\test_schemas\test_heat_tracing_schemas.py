# backend/tests/test_schemas/test_heat_tracing_schemas.py
"""
Tests for Heat Tracing Schemas

This module contains comprehensive tests for all heat tracing Pydantic schemas including:
- Validation tests for all schema fields
- Business logic validation tests
- Edge case and error condition tests
- Schema serialization/deserialization tests
"""

import json
import os
import sys

import pytest
from pydantic import ValidationError

# Mark all tests in this file
pytestmark = [pytest.mark.unit, pytest.mark.schema, pytest.mark.heat_tracing]

# Add the backend directory to the Python path
sys.path.insert(
    0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
)

from core.schemas.heat_tracing_schemas import (
    ControlCircuitCreateSchema,
    HeatLossCalculationInputSchema,
    HeatLossCalculationResultSchema,
    HeatTracingDesignInputSchema,
    HTCircuitCreateSchema,
    PipeCreateSchema,
    PipeReadSchema,
    PipeUpdateSchema,
    StandardsValidationInputSchema,
    VesselCreateSchema,
)


class TestHeatLossCalculationSchemas:
    """Test heat loss calculation schemas."""

    def test_heat_loss_calculation_input_valid(self):
        """Test valid heat loss calculation input."""
        data = {
            "pipe_diameter": 0.1,  # 100mm
            "pipe_length": 50.0,
            "fluid_temperature": 60.0,
            "ambient_temperature": -10.0,
            "insulation_thickness": 0.05,  # 50mm
            "insulation_type": "mineral_wool",
            "wind_speed": 5.0,
            "pipe_material": "carbon_steel",
        }

        schema = HeatLossCalculationInputSchema(**data)
        assert schema.pipe_diameter == 0.1
        assert schema.fluid_temperature == 60.0
        assert schema.ambient_temperature == -10.0

    def test_heat_loss_calculation_input_fluid_temp_validation(self):
        """Test fluid temperature must be higher than ambient."""
        # Test with valid data first
        valid_data = {
            "pipe_diameter": 0.1,
            "pipe_length": 50.0,
            "ambient_temperature": -10.0,  # Set ambient first
            "fluid_temperature": 60.0,  # Higher than ambient
            "insulation_thickness": 0.05,
            "insulation_type": "mineral_wool",
        }

        # This should work
        schema = HeatLossCalculationInputSchema(**valid_data)
        assert schema.fluid_temperature == 60.0

        # Test with invalid data - fluid temp lower than ambient
        invalid_data = {
            "pipe_diameter": 0.1,
            "pipe_length": 50.0,
            "ambient_temperature": -10.0,  # Set ambient first
            "fluid_temperature": -15.0,  # Lower than ambient
            "insulation_thickness": 0.05,
            "insulation_type": "mineral_wool",
        }

        with pytest.raises(ValidationError) as exc_info:
            HeatLossCalculationInputSchema(**invalid_data)

        assert "Fluid temperature must be higher than ambient temperature" in str(
            exc_info.value
        )

    def test_heat_loss_calculation_result_valid(self):
        """Test valid heat loss calculation result."""
        data = {
            "heat_loss_rate": 25.5,
            "total_heat_loss": 1275.0,
            "surface_temperature": 15.0,
            "required_power": 1530.0,
            "safety_factor": 1.2,
            "calculation_metadata": {"method": "steady_state"},
        }

        schema = HeatLossCalculationResultSchema(**data)
        assert schema.heat_loss_rate == 25.5
        assert schema.safety_factor == 1.2


class TestPipeSchemas:
    """Test pipe schemas."""

    def test_pipe_create_schema_valid(self):
        """Test valid pipe creation schema."""
        data = {
            "name": "Main Process Line 001",
            "project_id": 1,
            "pipe_material_id": 10,
            "insulation_material_id": 15,
            "nominal_diameter_mm": 100.0,
            "wall_thickness_mm": 5.0,
            "outer_diameter_mm": 110.0,
            "length_m": 50.0,
            "insulation_thickness_mm": 50.0,
            "fluid_type": "Process Water",
            "specific_heat_capacity_jkgc": 4180.0,
            "viscosity_cp": 1.0,
            "freezing_point_c": 0.0,
            "safety_margin_percent": 20.0,
            "pid": "P&ID-001",
            "line_tag": "L-001",
            "from_location": "Tank A",
            "to_location": "Tank B",
            "valve_count": 3,
            "support_count": 5,
        }

        schema = PipeCreateSchema(**data)
        assert schema.name == "Main Process Line 001"
        assert schema.project_id == 1
        assert schema.length_m == 50.0

    def test_pipe_create_schema_name_validation(self):
        """Test pipe name validation."""
        data = {
            "name": "  ",  # Empty name after strip
            "project_id": 1,
            "pipe_material_id": 10,
            "insulation_material_id": 15,
            "length_m": 50.0,
            "insulation_thickness_mm": 50.0,
        }

        with pytest.raises(ValidationError) as exc_info:
            PipeCreateSchema(**data)

        assert "String should have at least 3 characters" in str(exc_info.value)

    def test_pipe_create_schema_diameter_validation(self):
        """Test outer diameter validation."""
        data = {
            "name": "Test Pipe",
            "project_id": 1,
            "pipe_material_id": 10,
            "insulation_material_id": 15,
            "nominal_diameter_mm": 100.0,
            "outer_diameter_mm": 95.0,  # Smaller than nominal
            "length_m": 50.0,
            "insulation_thickness_mm": 50.0,
        }

        with pytest.raises(ValidationError) as exc_info:
            PipeCreateSchema(**data)

        assert "Outer diameter must be larger than nominal diameter" in str(
            exc_info.value
        )

    def test_pipe_update_schema_partial(self):
        """Test pipe update schema with partial data."""
        data = {"name": "Updated Pipe Name", "length_m": 75.0}

        schema = PipeUpdateSchema(**data)
        assert schema.name == "Updated Pipe Name"
        assert schema.length_m == 75.0
        assert schema.pipe_material_id is None  # Not provided

    def test_pipe_read_schema_from_dict(self):
        """Test pipe read schema creation from dict."""
        data = {
            "id": 1,
            "name": "Test Pipe",
            "project_id": 1,
            "pipe_material_id": 10,
            "insulation_material_id": 15,
            "length_m": 50.0,
            "insulation_thickness_mm": 50.0,
            "calculated_heat_loss_wm": 25.5,
            "required_heat_output_wm": 30.6,
            "created_at": "2024-01-15T10:30:00Z",
            "updated_at": "2024-01-15T10:30:00Z",
            "is_deleted": False,
            "deleted_at": None,
            "deleted_by_user_id": None,
        }

        schema = PipeReadSchema(**data)
        assert schema.id == 1
        assert schema.calculated_heat_loss_wm == 25.5


class TestVesselSchemas:
    """Test vessel schemas."""

    def test_vessel_create_schema_valid(self):
        """Test valid vessel creation schema."""
        dimensions = {"type": "cylinder", "diameter": 2.0, "height": 3.0}
        data = {
            "name": "Storage Tank T-001",
            "project_id": 1,
            "material_id": 12,
            "insulation_material_id": 15,
            "dimensions_json": json.dumps(dimensions),
            "surface_area_m2": 25.13,
            "insulation_thickness_mm": 75.0,
            "fluid_type": "Process Water",
            "equipment_tag": "T-001",
        }

        schema = VesselCreateSchema(**data)
        assert schema.name == "Storage Tank T-001"
        assert schema.surface_area_m2 == 25.13

    def test_vessel_create_schema_dimensions_validation(self):
        """Test vessel dimensions JSON validation."""
        data = {
            "name": "Test Vessel",
            "project_id": 1,
            "material_id": 12,
            "insulation_material_id": 15,
            "dimensions_json": "invalid json",
            "surface_area_m2": 25.13,
            "insulation_thickness_mm": 75.0,
        }

        with pytest.raises(ValidationError) as exc_info:
            VesselCreateSchema(**data)

        assert "Invalid JSON format for vessel dimensions" in str(exc_info.value)

    def test_vessel_create_schema_dimensions_type_validation(self):
        """Test vessel dimensions must contain type field."""
        dimensions = {"diameter": 2.0, "height": 3.0}  # Missing type
        data = {
            "name": "Test Vessel",
            "project_id": 1,
            "material_id": 12,
            "insulation_material_id": 15,
            "dimensions_json": json.dumps(dimensions),
            "surface_area_m2": 25.13,
            "insulation_thickness_mm": 75.0,
        }

        with pytest.raises(ValidationError) as exc_info:
            VesselCreateSchema(**data)

        assert 'Dimensions JSON must contain "type" field' in str(exc_info.value)


class TestHTCircuitSchemas:
    """Test HT circuit schemas."""

    def test_htcircuit_create_schema_valid_pipe(self):
        """Test valid HT circuit creation with pipe."""
        data = {
            "name": "HTC-001-A",
            "feeder_id": 1,
            "heat_tracing_cable_id": 20,
            "pipe_id": 1,
            "vessel_id": None,
            "control_circuit_id": 1,
            "number_of_circuits": 1,
            "isometric_no": "ISO-001",
            "application_type": "Process Temp",
            "heating_method": "Parallel",
        }

        schema = HTCircuitCreateSchema(**data)
        assert schema.name == "HTC-001-A"
        assert schema.pipe_id == 1
        assert schema.vessel_id is None

    def test_htcircuit_create_schema_valid_vessel(self):
        """Test valid HT circuit creation with vessel."""
        data = {
            "name": "HTC-002-A",
            "feeder_id": 1,
            "heat_tracing_cable_id": 20,
            "vessel_id": 1,
            "number_of_circuits": 2,
        }

        schema = HTCircuitCreateSchema(**data)
        assert schema.vessel_id == 1
        assert schema.pipe_id is None

    def test_htcircuit_create_schema_pipe_or_vessel_validation(self):
        """Test that either pipe_id or vessel_id must be provided."""
        data = {
            "name": "HTC-003-A",
            "feeder_id": 1,
            "heat_tracing_cable_id": 20,
            "pipe_id": None,
            "vessel_id": None,
        }

        with pytest.raises(ValidationError) as exc_info:
            HTCircuitCreateSchema(**data)

        assert "Either pipe_id or vessel_id must be provided" in str(exc_info.value)

    def test_htcircuit_create_schema_both_pipe_and_vessel_validation(self):
        """Test that both pipe_id and vessel_id cannot be provided."""
        data = {
            "name": "HTC-004-A",
            "feeder_id": 1,
            "heat_tracing_cable_id": 20,
            "pipe_id": 1,
            "vessel_id": 1,
        }

        with pytest.raises(ValidationError) as exc_info:
            HTCircuitCreateSchema(**data)

        assert "Cannot specify both pipe_id and vessel_id" in str(exc_info.value)


class TestControlCircuitSchemas:
    """Test control circuit schemas."""

    def test_control_circuit_create_schema_valid(self):
        """Test valid control circuit creation."""
        data = {
            "name": "Temperature Control TC-001",
            "switchboard_id": 1,
            "type": "Temperature Control",
            "sensor_type": "RTD",
            "primary_setpoint_c": 65.0,
            "limiting_setpoint_c": 85.0,
            "has_limiting_function": True,
            "control_philosophy": "Maintain process temperature with high limit protection",
        }

        schema = ControlCircuitCreateSchema(**data)
        assert schema.name == "Temperature Control TC-001"
        assert schema.primary_setpoint_c == 65.0
        assert schema.has_limiting_function is True

    def test_control_circuit_limiting_setpoint_validation(self):
        """Test limiting setpoint validation."""
        data = {
            "name": "Test Control",
            "type": "Temperature Control",
            "sensor_type": "RTD",
            "primary_setpoint_c": 65.0,
            "limiting_setpoint_c": 60.0,  # Lower than primary
            "has_limiting_function": True,
        }

        with pytest.raises(ValidationError) as exc_info:
            ControlCircuitCreateSchema(**data)

        assert "Limiting setpoint must be higher than primary setpoint" in str(
            exc_info.value
        )


class TestDesignWorkflowSchemas:
    """Test design workflow schemas."""

    def test_design_input_schema_valid(self):
        """Test valid design input schema."""
        heat_loss_result = HeatLossCalculationResultSchema(
            heat_loss_rate=25.5,
            total_heat_loss=1275.0,
            surface_temperature=15.0,
            required_power=1530.0,
            safety_factor=1.2,
        )

        standards_context = StandardsValidationInputSchema(
            heat_loss_result=heat_loss_result,
            design_parameters={},
            project_standards=["TR_50410"],
            hazardous_area_zone="Zone_1",
            gas_group="IIA",
            temperature_class="T3",
        )

        data = {
            "project_id": 1,
            "pipe_ids": [1, 2, 3],
            "vessel_ids": None,
            "design_parameters": {"fluid_temperature": 60.0},
            "standards_context": standards_context,
            "auto_assign_circuits": True,
            "optimization_enabled": True,
        }

        schema = HeatTracingDesignInputSchema(**data)
        assert schema.project_id == 1
        assert schema.pipe_ids is not None and len(schema.pipe_ids) == 3
        assert schema.auto_assign_circuits is True

    def test_design_input_schema_pipe_or_vessel_validation(self):
        """Test that either pipe_ids or vessel_ids must be provided."""
        heat_loss_result = HeatLossCalculationResultSchema(
            heat_loss_rate=25.5,
            total_heat_loss=1275.0,
            surface_temperature=15.0,
            required_power=1530.0,
            safety_factor=1.2,
        )

        standards_context = StandardsValidationInputSchema(
            heat_loss_result=heat_loss_result,
            design_parameters={},
            project_standards=["TR_50410"],
            hazardous_area_zone="Zone_1",
            gas_group="IIA",
            temperature_class="T3",
        )

        data = {
            "project_id": 1,
            "pipe_ids": [],  # Empty list
            "vessel_ids": [],  # Empty list
            "standards_context": standards_context,
        }

        with pytest.raises(ValidationError) as exc_info:
            HeatTracingDesignInputSchema(**data)

        assert "Either pipe_ids or vessel_ids must be provided" in str(exc_info.value)

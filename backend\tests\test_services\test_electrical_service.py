# backend/tests/test_electrical_service.py
"""
Tests for Electrical Service

This module contains comprehensive tests for the electrical service layer,
including business logic, calculations integration, and error handling.
"""

import pytest
from unittest.mock import Mock, patch
from sqlalchemy.orm import Session

from backend.core.services.electrical_service import ElectricalService
from backend.core.schemas.electrical_schemas import (
    CableSizingCalculationInputSchema,
    VoltageDropCalculationInputSchema,
    ElectricalStandardsValidationInputSchema,
    CableSizingCalculationResultSchema,
    VoltageDropCalculationResultSchema,
)
from backend.core.errors.exceptions import (
    BaseApplicationException,
    NotFoundError,
    InvalidInputError,
)


class TestElectricalService:
    """Test cases for ElectricalService."""

    @pytest.fixture
    def mock_db_session(self):
        """Create a mock database session."""
        return Mock(spec=Session)

    @pytest.fixture
    def electrical_service(self, mock_db_session):
        """Create an ElectricalService instance with mocked dependencies."""
        return ElectricalService(mock_db_session)

    @pytest.fixture
    def sample_cable_sizing_input(self):
        """Create sample cable sizing input data."""
        return CableSizingCalculationInputSchema(
            required_power_kw=5.0,
            cable_length_m=100.0,
            supply_voltage_v=240.0,
            ambient_temperature_c=25.0,
            installation_method="Direct Buried",
            cable_type="HEATING_CABLE_20W",
            max_voltage_drop_percent=5.0,
        )

    @pytest.fixture
    def sample_voltage_drop_input(self):
        """Create sample voltage drop input data."""
        return VoltageDropCalculationInputSchema(
            load_current_a=20.8,
            cable_length_m=100.0,
            cable_resistance_ohm_per_m=0.001,
            supply_voltage_v=240.0,
            power_factor=1.0,
            cable_reactance_ohm_per_m=0.0,
            max_voltage_drop_percent=5.0,
            derating_factor=1.0,
            ambient_temperature_c=25.0,
        )

    def test_cable_sizing_calculation_success(
        self, electrical_service, sample_cable_sizing_input
    ):
        """Test successful cable sizing calculation."""
        # Mock the calculation service
        with patch.object(
            electrical_service.calculation_service, "calculate_cable_sizing"
        ) as mock_calc:
            # Mock calculation result
            from backend.core.calculations.calculation_service import CableSizingResult

            mock_result = CableSizingResult(
                calculation_type="cable_sizing",
                inputs=sample_cable_sizing_input.model_dump(),
                outputs={},
                recommended_cable_type="HEATING_CABLE_20W",
                cable_power_per_meter=20.0,
                total_cable_length=100.0,
                current_draw=20.8,
                voltage_drop=2.08,
                power_density=200.0,
            )
            mock_calc.return_value = mock_result

            # Perform calculation
            result = electrical_service.perform_cable_sizing_calculation(
                sample_cable_sizing_input
            )

            # Verify result
            assert isinstance(result, CableSizingCalculationResultSchema)
            assert result.recommended_cable_type == "HEATING_CABLE_20W"
            assert result.current_draw == 20.8
            assert result.voltage_drop == 2.08
            assert result.voltage_drop_percent == pytest.approx(0.87, rel=1e-2)
            assert result.is_compliant is True
            assert result.safety_margin_percent > 0

            # Verify calculation service was called
            mock_calc.assert_called_once()

    def test_voltage_drop_calculation_success(
        self, electrical_service, sample_voltage_drop_input
    ):
        """Test successful voltage drop calculation."""
        # Mock voltage drop calculation functions
        with (
            patch(
                "backend.core.services.electrical_service.calculate_voltage_drop"
            ) as mock_vd,
            patch(
                "backend.core.services.electrical_service.calculate_voltage_drop_percentage"
            ) as mock_vdp,
            patch(
                "backend.core.services.electrical_service.validate_voltage_drop_compliance"
            ) as mock_comp,
        ):
            # Set up mocks
            mock_vd.return_value = 2.08  # 2.08V drop
            mock_vdp.return_value = 0.87  # 0.87% drop
            mock_comp.return_value = {"is_compliant": True, "margin": 4.13}

            # Perform calculation
            result = electrical_service.perform_voltage_drop_calculation(
                sample_voltage_drop_input
            )

            # Verify result
            assert isinstance(result, VoltageDropCalculationResultSchema)
            assert result.calculated_voltage_drop_v == 2.08
            assert result.calculated_voltage_drop_percent == 0.87
            assert result.is_compliant is True
            assert result.compliance_margin_percent == pytest.approx(4.13, rel=1e-2)

            # Verify functions were called
            mock_vd.assert_called_once()
            mock_vdp.assert_called_once()
            mock_comp.assert_called_once()

    def test_electrical_standards_validation_success(self, electrical_service):
        """Test successful electrical standards validation."""
        # Create validation input
        validation_input = ElectricalStandardsValidationInputSchema(
            cable_sizing_result=CableSizingCalculationResultSchema(
                recommended_cable_type="HEATING_CABLE_20W",
                cable_power_per_meter=20.0,
                total_cable_length=100.0,
                current_draw=20.8,
                voltage_drop=2.08,
                voltage_drop_percent=0.87,
                power_density=200.0,
                is_compliant=True,
                safety_margin_percent=4.13,
                calculation_metadata={},
            ),
            voltage_drop_result=VoltageDropCalculationResultSchema(
                calculated_voltage_drop_v=2.08,
                calculated_voltage_drop_percent=0.87,
                calculated_power_loss_w=43.3,
                calculated_efficiency_percent=99.1,
                is_compliant=True,
                compliance_margin_percent=4.13,
                calculation_method="IEC",
                calculation_metadata={},
            ),
            design_parameters={"supply_voltage": 240.0},
            project_standards=["IEC_60079_30_1"],
            hazardous_area_zone="Zone_1",
            gas_group="IIA",
            temperature_class="T4",
            installation_environment="Indoor",
        )

        # Mock the standards manager
        with (
            patch.object(
                electrical_service.standards_manager, "set_context"
            ) as mock_context,
            patch.object(
                electrical_service.standards_manager, "validate_cable_selection"
            ) as mock_validate,
            patch.object(
                electrical_service.standards_manager, "apply_safety_factors"
            ) as mock_factors,
        ):
            # Mock validation result
            from backend.core.standards.standards_manager import ValidationResult

            mock_validation = ValidationResult(
                is_compliant=True,
                standard="IEC_60079_30_1",
                violations=[],
                warnings=["Consider additional safety margin"],
                applied_factors={"power_derating_factor": 0.8},
                metadata={"validation_type": "cable_selection"},
            )
            mock_validate.return_value = mock_validation
            mock_factors.return_value = {"adjusted_power": 4.0}

            # Perform validation
            result = electrical_service.validate_electrical_standards(validation_input)

            # Verify result
            assert result.is_compliant is True
            assert result.standard == "IEC_60079_30_1"
            assert len(result.violations) == 0
            assert len(result.warnings) == 1
            assert "safety margin" in result.warnings[0]
            assert len(result.recommendations) > 0

            # Verify standards manager was called
            mock_context.assert_called_once()
            mock_validate.assert_called_once()
            mock_factors.assert_called_once()

    def test_calculate_load_for_electrical_node_success(self, electrical_service):
        """Test successful load calculation for electrical node."""
        node_id = 1

        # Mock repositories
        with (
            patch.object(
                electrical_service.electrical_node_repo, "get_by_id"
            ) as mock_get_node,
            patch.object(
                electrical_service.load_calculation_repo, "get_by_electrical_node_id"
            ) as mock_get_loads,
        ):
            # Mock electrical node
            mock_node = Mock()
            mock_node.id = node_id
            mock_node.name = "Test Node"
            mock_node.power_capacity_kva = 100.0
            mock_get_node.return_value = mock_node

            # Mock load calculations
            mock_load1 = Mock()
            mock_load1.rated_power_kw = 5.0
            mock_load1.load_factor_percent = 80.0
            mock_load1.calculated_operating_power_kw = 4.0
            mock_load1.calculated_operating_current_a = 16.7
            mock_load1.rated_voltage_v = 240.0
            mock_load1.power_factor = 1.0
            mock_load1.diversity_factor = 0.9
            mock_load1.load_type = "heating"

            mock_load2 = Mock()
            mock_load2.rated_power_kw = 3.0
            mock_load2.load_factor_percent = 100.0
            mock_load2.calculated_operating_power_kw = None
            mock_load2.calculated_operating_current_a = None
            mock_load2.rated_voltage_v = 240.0
            mock_load2.power_factor = 1.0
            mock_load2.diversity_factor = 0.8
            mock_load2.load_type = "lighting"

            mock_get_loads.return_value = [mock_load1, mock_load2]

            # Perform calculation
            result = electrical_service.calculate_load_for_electrical_node(node_id)

            # Verify result
            assert result["electrical_node_id"] == node_id
            assert result["electrical_node_name"] == "Test Node"
            assert result["load_count"] == 2
            assert result["total_rated_power_kw"] == 8.0
            assert result["total_operating_power_kw"] == 7.0  # 4.0 + 3.0
            assert result["diversity_factor"] == pytest.approx(
                0.85, rel=1e-2
            )  # (0.9 + 0.8) / 2
            assert result["diversified_power_kw"] == pytest.approx(
                5.95, rel=1e-2
            )  # 7.0 * 0.85
            assert result["capacity_utilization_percent"] == pytest.approx(
                5.95, rel=1e-2
            )
            assert "load_breakdown" in result
            assert "heating" in result["load_breakdown"]
            assert "lighting" in result["load_breakdown"]

            # Verify repositories were called
            mock_get_node.assert_called_once_with(node_id)
            mock_get_loads.assert_called_once_with(node_id)

    def test_calculate_load_for_nonexistent_node(self, electrical_service):
        """Test load calculation for non-existent electrical node."""
        node_id = 999

        # Mock repository to return None
        with patch.object(
            electrical_service.electrical_node_repo, "get_by_id"
        ) as mock_get_node:
            mock_get_node.return_value = None

            # Expect NotFoundError
            with pytest.raises(NotFoundError) as exc_info:
                electrical_service.calculate_load_for_electrical_node(node_id)

            assert "not found" in str(exc_info.value)
            mock_get_node.assert_called_once_with(node_id)

    def test_cable_sizing_calculation_error_handling(
        self, electrical_service, sample_cable_sizing_input
    ):
        """Test error handling in cable sizing calculation."""
        # Mock the calculation service to raise an exception
        with patch.object(
            electrical_service.calculation_service, "calculate_cable_sizing"
        ) as mock_calc:
            mock_calc.side_effect = Exception("Calculation failed")

            # Expect BaseApplicationException
            with pytest.raises(BaseApplicationException) as exc_info:
                electrical_service.perform_cable_sizing_calculation(
                    sample_cable_sizing_input
                )

            assert "Cable sizing calculation failed" in str(exc_info.value)
            mock_calc.assert_called_once()

    def test_voltage_drop_calculation_error_handling(
        self, electrical_service, sample_voltage_drop_input
    ):
        """Test error handling in voltage drop calculation."""
        # Mock voltage drop calculation to raise an exception
        with patch(
            "backend.core.services.electrical_service.calculate_voltage_drop"
        ) as mock_vd:
            mock_vd.side_effect = Exception("Voltage drop calculation failed")

            # Expect BaseApplicationException
            with pytest.raises(BaseApplicationException) as exc_info:
                electrical_service.perform_voltage_drop_calculation(
                    sample_voltage_drop_input
                )

            assert "Voltage drop calculation failed" in str(exc_info.value)
            mock_vd.assert_called_once()

    def test_generate_recommendations(self, electrical_service):
        """Test recommendation generation based on validation results."""
        from backend.core.standards.standards_manager import ValidationResult

        # Test voltage drop violations
        validation_result = ValidationResult(
            is_compliant=False,
            standard="IEC",
            violations=["Voltage drop 6.5% exceeds limit 5%"],
            warnings=["High power density"],
            applied_factors={},
            metadata={},
        )

        recommendations = electrical_service._generate_recommendations(
            validation_result
        )

        assert len(recommendations) > 0
        assert any("cable size" in rec.lower() for rec in recommendations)
        assert any("voltage" in rec.lower() for rec in recommendations)

        # Test temperature violations
        validation_result = ValidationResult(
            is_compliant=False,
            standard="IEC",
            violations=["Temperature 95°C exceeds limit 85°C"],
            warnings=[],
            applied_factors={},
            metadata={},
        )

        recommendations = electrical_service._generate_recommendations(
            validation_result
        )

        assert len(recommendations) > 0
        assert any("temperature" in rec.lower() for rec in recommendations)

        # Test hazardous area violations
        validation_result = ValidationResult(
            is_compliant=False,
            standard="IEC",
            violations=["Hazardous area certification required"],
            warnings=[],
            applied_factors={},
            metadata={},
        )

        recommendations = electrical_service._generate_recommendations(
            validation_result
        )

        assert len(recommendations) > 0
        assert any("hazardous" in rec.lower() for rec in recommendations)

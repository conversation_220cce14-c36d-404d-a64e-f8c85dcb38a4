# backend/tests/test_api/test_document_routes.py
"""
Integration tests for Document API routes.

Tests the complete document management workflow including:
- Calculation standards CRUD operations
- Data import revision tracking
- Document export management
"""

import pytest
from unittest.mock import patch
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from backend.core.models.project import Project
from backend.core.models.users import User


class TestCalculationStandardRoutes:
    """Test calculation standard API endpoints."""

    def test_create_calculation_standard(self, client: TestClient, db_session: Session):
        """Test creating a calculation standard via API."""
        standard_data = {
            "name": "TR 50410 Heat Tracing Standard",
            "standard_code": "TR-50410",
            "description": "Technical Report 50410 for heat tracing calculations",
            "parameters_json": '{"safety_factor": 1.2, "max_temp_rating": 250}',
        }

        response = client.post(
            "/api/v1/documents/calculation-standards", json=standard_data
        )

        assert response.status_code == 201
        data = response.json()
        assert data["name"] == "TR 50410 Heat Tracing Standard"
        assert data["standard_code"] == "TR-50410"
        assert (
            data["description"]
            == "Technical Report 50410 for heat tracing calculations"
        )
        assert (
            data["parameters_json"] == '{"safety_factor": 1.2, "max_temp_rating": 250}'
        )
        assert data["id"] is not None

    def test_get_calculation_standard_by_id(
        self, client: TestClient, db_session: Session
    ):
        """Test retrieving a calculation standard by ID."""
        # First create a standard
        standard_data = {
            "name": "Test Standard",
            "standard_code": "TEST-001",
            "description": "Test description",
        }

        create_response = client.post(
            "/api/v1/documents/calculation-standards", json=standard_data
        )
        assert create_response.status_code == 201
        created_standard = create_response.json()

        # Then retrieve it
        response = client.get(
            f"/api/v1/documents/calculation-standards/{created_standard['id']}"
        )

        assert response.status_code == 200
        data = response.json()
        assert data["id"] == created_standard["id"]
        assert data["name"] == "Test Standard"
        assert data["standard_code"] == "TEST-001"

    def test_get_calculation_standard_by_code(
        self, client: TestClient, db_session: Session
    ):
        """Test retrieving a calculation standard by code."""
        # First create a standard
        standard_data = {
            "name": "Code Test Standard",
            "standard_code": "CTS-001",
            "description": "Test description",
        }

        create_response = client.post(
            "/api/v1/documents/calculation-standards", json=standard_data
        )
        assert create_response.status_code == 201

        # Then retrieve it by code
        response = client.get("/api/v1/documents/calculation-standards/by-code/CTS-001")

        assert response.status_code == 200
        data = response.json()
        assert data["name"] == "Code Test Standard"
        assert data["standard_code"] == "CTS-001"

    def test_list_calculation_standards(self, client: TestClient, db_session: Session):
        """Test listing calculation standards with pagination."""
        # Create multiple standards
        standards_data = [
            {
                "name": "Standard 1",
                "standard_code": "STD-001",
                "description": "First standard",
            },
            {
                "name": "Standard 2",
                "standard_code": "STD-002",
                "description": "Second standard",
            },
        ]

        for standard_data in standards_data:
            response = client.post(
                "/api/v1/documents/calculation-standards", json=standard_data
            )
            assert response.status_code == 201

        # List standards
        response = client.get(
            "/api/v1/documents/calculation-standards?page=1&per_page=10"
        )

        assert response.status_code == 200
        data = response.json()
        assert "standards" in data
        assert "total" in data
        assert "page" in data
        assert "per_page" in data
        assert len(data["standards"]) >= 2

    def test_update_calculation_standard(self, client: TestClient, db_session: Session):
        """Test updating a calculation standard."""
        # First create a standard
        standard_data = {
            "name": "Original Standard",
            "standard_code": "ORIG-001",
            "description": "Original description",
        }

        create_response = client.post(
            "/api/v1/documents/calculation-standards", json=standard_data
        )
        assert create_response.status_code == 201
        created_standard = create_response.json()

        # Then update it
        update_data = {
            "name": "Updated Standard",
            "description": "Updated description",
        }

        response = client.put(
            f"/api/v1/documents/calculation-standards/{created_standard['id']}",
            json=update_data,
        )

        assert response.status_code == 200
        data = response.json()
        assert data["name"] == "Updated Standard"
        assert data["description"] == "Updated description"
        assert data["standard_code"] == "ORIG-001"  # Should remain unchanged

    def test_delete_calculation_standard(self, client: TestClient, db_session: Session):
        """Test deleting a calculation standard."""
        # First create a standard
        standard_data = {
            "name": "To Delete Standard",
            "standard_code": "DEL-001",
            "description": "Will be deleted",
        }

        create_response = client.post(
            "/api/v1/documents/calculation-standards", json=standard_data
        )
        assert create_response.status_code == 201
        created_standard = create_response.json()

        # Then delete it
        response = client.delete(
            f"/api/v1/documents/calculation-standards/{created_standard['id']}"
        )

        assert response.status_code == 204

        # Verify it's deleted (should return 404)
        get_response = client.get(
            f"/api/v1/documents/calculation-standards/{created_standard['id']}"
        )
        assert get_response.status_code == 404

    def test_create_calculation_standard_duplicate_code(
        self, client: TestClient, db_session: Session
    ):
        """Test creating calculation standard with duplicate code fails."""
        standard_data = {
            "name": "First Standard",
            "standard_code": "DUP-001",
            "description": "First standard",
        }

        # Create first standard
        response1 = client.post(
            "/api/v1/documents/calculation-standards", json=standard_data
        )
        assert response1.status_code == 201

        # Try to create second standard with same code
        duplicate_data = {
            "name": "Second Standard",
            "standard_code": "DUP-001",  # Same code
            "description": "Second standard",
        }

        response2 = client.post(
            "/api/v1/documents/calculation-standards", json=duplicate_data
        )
        assert response2.status_code == 409  # Conflict

    def test_get_nonexistent_calculation_standard(
        self, client: TestClient, db_session: Session
    ):
        """Test retrieving non-existent calculation standard returns 404."""
        response = client.get("/api/v1/documents/calculation-standards/99999")
        assert response.status_code == 404

    def test_calculation_standard_validation_errors(
        self, client: TestClient, db_session: Session
    ):
        """Test validation errors for calculation standard creation."""
        # Test empty name
        invalid_data = {
            "name": "",
            "standard_code": "INVALID-001",
        }

        response = client.post(
            "/api/v1/documents/calculation-standards", json=invalid_data
        )
        assert response.status_code == 422  # Validation error

        # Test empty standard code
        invalid_data = {
            "name": "Valid Name",
            "standard_code": "",
        }

        response = client.post(
            "/api/v1/documents/calculation-standards", json=invalid_data
        )
        assert response.status_code == 422  # Validation error

        # Test invalid JSON parameters
        invalid_data = {
            "name": "Valid Name",
            "standard_code": "VALID-001",
            "parameters_json": "invalid json",
        }

        response = client.post(
            "/api/v1/documents/calculation-standards", json=invalid_data
        )
        assert response.status_code == 422  # Validation error


class TestImportedDataRevisionRoutes:
    """Test imported data revision API endpoints."""

    def test_create_imported_data_revision(
        self,
        client: TestClient,
        db_session: Session,
        sample_project: Project,
        sample_user: User,
    ):
        """Test creating an imported data revision via API."""
        revision_data = {
            "project_id": sample_project.id,
            "imported_by_user_id": sample_user.id,
            "source_filename": "test_data.xlsx",
            "revision_identifier": "REV-001",
            "import_type": "pipe_data",
            "is_active_revision": True,
        }

        response = client.post("/api/v1/documents/import-revisions", json=revision_data)

        assert response.status_code == 201
        data = response.json()
        assert data["project_id"] == sample_project.id
        assert data["imported_by_user_id"] == sample_user.id
        assert data["source_filename"] == "test_data.xlsx"
        assert data["revision_identifier"] == "REV-001"
        assert data["import_type"] == "pipe_data"
        assert data["is_active_revision"] is True

    def test_list_imported_data_revisions_by_project(
        self,
        client: TestClient,
        db_session: Session,
        sample_project: Project,
        sample_user: User,
    ):
        """Test listing imported data revisions for a project."""
        # Create test revisions
        revision_data = {
            "project_id": sample_project.id,
            "imported_by_user_id": sample_user.id,
            "source_filename": "test_data.xlsx",
            "import_type": "pipe_data",
            "is_active_revision": True,
        }

        response = client.post("/api/v1/documents/import-revisions", json=revision_data)
        assert response.status_code == 201

        # List revisions
        response = client.get(
            f"/api/v1/documents/import-revisions?project_id={sample_project.id}&page=1&per_page=10"
        )

        assert response.status_code == 200
        data = response.json()
        assert "revisions" in data
        assert "total" in data
        assert len(data["revisions"]) >= 1


class TestExportedDocumentRoutes:
    """Test exported document API endpoints."""

    def test_create_exported_document(
        self,
        client: TestClient,
        db_session: Session,
        sample_project: Project,
        sample_user: User,
    ):
        """Test creating an exported document via API."""
        document_data = {
            "project_id": sample_project.id,
            "generated_by_user_id": sample_user.id,
            "document_type": "heat_tracing_report",
            "filename": "HT_Report_v1.pdf",
            "revision": "v1.0",
            "file_path_or_url": "/documents/ht_report_1.pdf",
            "is_latest_revision": True,
        }

        response = client.post("/api/v1/documents/export-documents", json=document_data)

        assert response.status_code == 201
        data = response.json()
        assert data["project_id"] == sample_project.id
        assert data["generated_by_user_id"] == sample_user.id
        assert data["document_type"] == "heat_tracing_report"
        assert data["filename"] == "HT_Report_v1.pdf"
        assert data["revision"] == "v1.0"
        assert data["is_latest_revision"] is True

    def test_list_exported_documents_by_project(
        self,
        client: TestClient,
        db_session: Session,
        sample_project: Project,
        sample_user: User,
    ):
        """Test listing exported documents for a project."""
        # Create test document
        document_data = {
            "project_id": sample_project.id,
            "generated_by_user_id": sample_user.id,
            "document_type": "heat_tracing_report",
            "filename": "HT_Report_v1.pdf",
            "is_latest_revision": True,
        }

        response = client.post("/api/v1/documents/export-documents", json=document_data)
        assert response.status_code == 201

        # List documents
        response = client.get(
            f"/api/v1/documents/export-documents?project_id={sample_project.id}&page=1&per_page=10"
        )

        assert response.status_code == 200
        data = response.json()
        assert "documents" in data
        assert "total" in data
        assert len(data["documents"]) >= 1

# backend/core/repositories/activity_log_repository.py
"""
Repository for Activity Log entity data access operations.

This module provides data access methods for activity log operations including:
- CRUD operations for activity logs
- Advanced querying for audit trails and reporting
- Time-based filtering and user activity tracking
- Performance-optimized queries for large audit datasets
- Security event monitoring and compliance reporting
"""

import logging
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple

from sqlalchemy import and_, desc, func, or_, select, text
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session

try:
    from core.errors.exceptions import DatabaseError
    from core.models.activity_log import ActivityLog
    from core.repositories.base_repository import BaseRepository
    from core.schemas.activity_log_schemas import (
        ActivityLogFilterSchema,
        EventCategoryEnum,
        EventTypeEnum,
        EntityTypeEnum,
    )
except ImportError:
    from core.errors.exceptions import DatabaseError
    from core.models.activity_log import ActivityLog
    from core.repositories.base_repository import BaseRepository
    from core.schemas.activity_log_schemas import (
        ActivityLogFilterSchema,
        EventCategoryEnum,
        EventTypeEnum,
        EntityTypeEnum,
    )

logger = logging.getLogger(__name__)


class ActivityLogRepository(BaseRepository[ActivityLog]):
    """
    Repository for Activity Log entity data access operations.

    Extends BaseRepository with activity log-specific query methods and
    enhanced error handling for audit trail operations.
    """

    def __init__(self, db_session: Session):
        """
        Initialize the Activity Log repository.

        Args:
            db_session: SQLAlchemy database session
        """
        super().__init__(db_session, ActivityLog)
        logger.debug("ActivityLogRepository initialized")

    def get_by_user_id(
        self, user_id: int, skip: int = 0, limit: int = 100
    ) -> List[ActivityLog]:
        """
        Get activity logs for a specific user.

        Args:
            user_id: User ID to filter by
            skip: Number of records to skip (for pagination)
            limit: Maximum number of records to return

        Returns:
            List[ActivityLog]: List of activity logs for the user

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(
            f"Retrieving activity logs for user {user_id}: skip={skip}, limit={limit}"
        )

        try:
            stmt = (
                select(self.model)
                .where(self.model.user_id == user_id)
                .order_by(desc(self.model.timestamp))
                .offset(skip)
                .limit(limit)
            )
            results = list(self.db_session.scalars(stmt).all())

            logger.debug(f"Retrieved {len(results)} activity logs for user {user_id}")
            return results

        except SQLAlchemyError as e:
            logger.error(
                f"Database error retrieving activity logs for user {user_id}: {e}"
            )
            self._handle_db_exception(e, "activity_log")
            raise

    def get_by_entity(
        self, entity_type: str, entity_id: int, skip: int = 0, limit: int = 100
    ) -> List[ActivityLog]:
        """
        Get activity logs for a specific entity.

        Args:
            entity_type: Type of entity
            entity_id: Entity ID to filter by
            skip: Number of records to skip (for pagination)
            limit: Maximum number of records to return

        Returns:
            List[ActivityLog]: List of activity logs for the entity

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(
            f"Retrieving activity logs for entity {entity_type}:{entity_id}: skip={skip}, limit={limit}"
        )

        try:
            stmt = (
                select(self.model)
                .where(
                    and_(
                        self.model.entity_type == entity_type,
                        self.model.entity_id == entity_id,
                    )
                )
                .order_by(desc(self.model.timestamp))
                .offset(skip)
                .limit(limit)
            )
            results = list(self.db_session.scalars(stmt).all())

            logger.debug(
                f"Retrieved {len(results)} activity logs for entity {entity_type}:{entity_id}"
            )
            return results

        except SQLAlchemyError as e:
            logger.error(
                f"Database error retrieving activity logs for entity {entity_type}:{entity_id}: {e}"
            )
            self._handle_db_exception(e, "activity_log")
            raise

    def get_by_event_type(
        self, event_type: str, skip: int = 0, limit: int = 100
    ) -> List[ActivityLog]:
        """
        Get activity logs by event type.

        Args:
            event_type: Event type to filter by
            skip: Number of records to skip (for pagination)
            limit: Maximum number of records to return

        Returns:
            List[ActivityLog]: List of activity logs for the event type

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(
            f"Retrieving activity logs for event type {event_type}: skip={skip}, limit={limit}"
        )

        try:
            stmt = (
                select(self.model)
                .where(self.model.event_type == event_type)
                .order_by(desc(self.model.timestamp))
                .offset(skip)
                .limit(limit)
            )
            results = list(self.db_session.scalars(stmt).all())

            logger.debug(
                f"Retrieved {len(results)} activity logs for event type {event_type}"
            )
            return results

        except SQLAlchemyError as e:
            logger.error(
                f"Database error retrieving activity logs for event type {event_type}: {e}"
            )
            self._handle_db_exception(e, "activity_log")
            raise

    def get_by_date_range(
        self,
        start_date: datetime,
        end_date: datetime,
        skip: int = 0,
        limit: int = 100,
    ) -> List[ActivityLog]:
        """
        Get activity logs within a date range.

        Args:
            start_date: Start date for filtering
            end_date: End date for filtering
            skip: Number of records to skip (for pagination)
            limit: Maximum number of records to return

        Returns:
            List[ActivityLog]: List of activity logs within the date range

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(
            f"Retrieving activity logs for date range {start_date} to {end_date}: skip={skip}, limit={limit}"
        )

        try:
            stmt = (
                select(self.model)
                .where(
                    and_(
                        self.model.timestamp >= start_date,
                        self.model.timestamp <= end_date,
                    )
                )
                .order_by(desc(self.model.timestamp))
                .offset(skip)
                .limit(limit)
            )
            results = list(self.db_session.scalars(stmt).all())

            logger.debug(
                f"Retrieved {len(results)} activity logs for date range {start_date} to {end_date}"
            )
            return results

        except SQLAlchemyError as e:
            logger.error(
                f"Database error retrieving activity logs for date range {start_date} to {end_date}: {e}"
            )
            self._handle_db_exception(e, "activity_log")
            raise

    def search_by_details(
        self, search_term: str, skip: int = 0, limit: int = 100
    ) -> List[ActivityLog]:
        """
        Search activity logs by details content.

        Args:
            search_term: Search term to match against details
            skip: Number of records to skip (for pagination)
            limit: Maximum number of records to return

        Returns:
            List[ActivityLog]: List of activity logs matching the search term

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Searching activity logs with term: {search_term}")

        try:
            search_pattern = f"%{search_term}%"
            stmt = (
                select(self.model)
                .where(self.model.details.ilike(search_pattern))
                .order_by(desc(self.model.timestamp))
                .offset(skip)
                .limit(limit)
            )
            results = list(self.db_session.scalars(stmt).all())

            logger.debug(
                f"Found {len(results)} activity logs matching search term: {search_term}"
            )
            return results

        except SQLAlchemyError as e:
            logger.error(
                f"Database error searching activity logs with term {search_term}: {e}"
            )
            self._handle_db_exception(e, "activity_log")
            raise

    def filter_activity_logs(
        self, filters: ActivityLogFilterSchema, skip: int = 0, limit: int = 100
    ) -> List[ActivityLog]:
        """
        Filter activity logs using advanced criteria.

        Args:
            filters: Filter criteria
            skip: Number of records to skip (for pagination)
            limit: Maximum number of records to return

        Returns:
            List[ActivityLog]: List of filtered activity logs

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Filtering activity logs with criteria: {filters}")

        try:
            stmt = select(self.model)
            conditions = []

            # Apply filters
            if filters.user_id is not None:
                conditions.append(self.model.user_id == filters.user_id)

            if filters.event_type is not None:
                conditions.append(self.model.event_type == filters.event_type.value)

            if filters.entity_type is not None:
                conditions.append(self.model.entity_type == filters.entity_type.value)

            if filters.entity_id is not None:
                conditions.append(self.model.entity_id == filters.entity_id)

            if filters.start_date is not None:
                conditions.append(self.model.timestamp >= filters.start_date)

            if filters.end_date is not None:
                conditions.append(self.model.timestamp <= filters.end_date)

            if filters.search_details is not None:
                search_pattern = f"%{filters.search_details}%"
                conditions.append(self.model.details.ilike(search_pattern))

            # Apply all conditions
            if conditions:
                stmt = stmt.where(and_(*conditions))

            # Order and paginate
            stmt = stmt.order_by(desc(self.model.timestamp)).offset(skip).limit(limit)

            results = list(self.db_session.scalars(stmt).all())

            logger.debug(f"Retrieved {len(results)} filtered activity logs")
            return results

        except SQLAlchemyError as e:
            logger.error(f"Database error filtering activity logs: {e}")
            self._handle_db_exception(e, "activity_log")
            raise

    def count_filtered_activity_logs(self, filters: ActivityLogFilterSchema) -> int:
        """
        Count activity logs matching filter criteria.

        Args:
            filters: Filter criteria

        Returns:
            int: Number of activity logs matching the criteria

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Counting filtered activity logs with criteria: {filters}")

        try:
            stmt = select(func.count(self.model.id))
            conditions = []

            # Apply same filters as filter_activity_logs
            if filters.user_id is not None:
                conditions.append(self.model.user_id == filters.user_id)

            if filters.event_type is not None:
                conditions.append(self.model.event_type == filters.event_type.value)

            if filters.entity_type is not None:
                conditions.append(self.model.entity_type == filters.entity_type.value)

            if filters.entity_id is not None:
                conditions.append(self.model.entity_id == filters.entity_id)

            if filters.start_date is not None:
                conditions.append(self.model.timestamp >= filters.start_date)

            if filters.end_date is not None:
                conditions.append(self.model.timestamp <= filters.end_date)

            if filters.search_details is not None:
                search_pattern = f"%{filters.search_details}%"
                conditions.append(self.model.details.ilike(search_pattern))

            # Apply all conditions
            if conditions:
                stmt = stmt.where(and_(*conditions))

            result = self.db_session.scalar(stmt)

            logger.debug(f"Total filtered activity logs: {result}")
            return result or 0

        except SQLAlchemyError as e:
            logger.error(f"Database error counting filtered activity logs: {e}")
            self._handle_db_exception(e, "activity_log")
            raise

    def get_security_events(
        self, start_date: datetime, end_date: datetime, skip: int = 0, limit: int = 100
    ) -> List[ActivityLog]:
        """
        Get security-related events within a date range.

        Args:
            start_date: Start date for filtering
            end_date: End date for filtering
            skip: Number of records to skip (for pagination)
            limit: Maximum number of records to return

        Returns:
            List[ActivityLog]: List of security events

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(
            f"Retrieving security events for date range {start_date} to {end_date}: skip={skip}, limit={limit}"
        )

        try:
            security_event_types = [
                EventTypeEnum.LOGIN_FAILED.value,
                EventTypeEnum.UNAUTHORIZED_ACCESS.value,
                EventTypeEnum.PERMISSION_DENIED.value,
                EventTypeEnum.SECURITY_VIOLATION.value,
            ]

            stmt = (
                select(self.model)
                .where(
                    and_(
                        self.model.timestamp >= start_date,
                        self.model.timestamp <= end_date,
                        self.model.event_type.in_(security_event_types),
                    )
                )
                .order_by(desc(self.model.timestamp))
                .offset(skip)
                .limit(limit)
            )
            results = list(self.db_session.scalars(stmt).all())

            logger.debug(
                f"Retrieved {len(results)} security events for date range {start_date} to {end_date}"
            )
            return results

        except SQLAlchemyError as e:
            logger.error(
                f"Database error retrieving security events for date range {start_date} to {end_date}: {e}"
            )
            self._handle_db_exception(e, "activity_log")
            raise

    def get_user_activity_summary(
        self, user_id: int, start_date: datetime, end_date: datetime
    ) -> Dict[str, Any]:
        """
        Get activity summary for a specific user within a date range.

        Args:
            user_id: User ID
            start_date: Start date for filtering
            end_date: End date for filtering

        Returns:
            Dict[str, Any]: Activity summary with event counts and statistics

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(
            f"Retrieving activity summary for user {user_id} from {start_date} to {end_date}"
        )

        try:
            # Get total event count
            total_stmt = select(func.count(self.model.id)).where(
                and_(
                    self.model.user_id == user_id,
                    self.model.timestamp >= start_date,
                    self.model.timestamp <= end_date,
                )
            )
            total_events = self.db_session.scalar(total_stmt) or 0

            # Get event type counts
            event_type_stmt = (
                select(self.model.event_type, func.count(self.model.id))
                .where(
                    and_(
                        self.model.user_id == user_id,
                        self.model.timestamp >= start_date,
                        self.model.timestamp <= end_date,
                    )
                )
                .group_by(self.model.event_type)
            )
            event_type_results = self.db_session.execute(event_type_stmt).all()
            event_type_counts = {
                event_type: count for event_type, count in event_type_results
            }

            # Get entity type counts
            entity_type_stmt = (
                select(self.model.entity_type, func.count(self.model.id))
                .where(
                    and_(
                        self.model.user_id == user_id,
                        self.model.timestamp >= start_date,
                        self.model.timestamp <= end_date,
                        self.model.entity_type.isnot(None),
                    )
                )
                .group_by(self.model.entity_type)
            )
            entity_type_results = self.db_session.execute(entity_type_stmt).all()
            entity_type_counts = {
                entity_type: count for entity_type, count in entity_type_results
            }

            summary = {
                "user_id": user_id,
                "start_date": start_date,
                "end_date": end_date,
                "total_events": total_events,
                "event_type_counts": event_type_counts,
                "entity_type_counts": entity_type_counts,
            }

            logger.debug(
                f"Generated activity summary for user {user_id}: {total_events} events"
            )
            return summary

        except SQLAlchemyError as e:
            logger.error(
                f"Database error generating activity summary for user {user_id}: {e}"
            )
            self._handle_db_exception(e, "activity_log")
            raise

    def get_daily_activity_counts(
        self, start_date: datetime, end_date: datetime
    ) -> Dict[str, int]:
        """
        Get daily activity counts within a date range.

        Args:
            start_date: Start date for filtering
            end_date: End date for filtering

        Returns:
            Dict[str, int]: Daily activity counts with date as key

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(
            f"Retrieving daily activity counts for date range {start_date} to {end_date}"
        )

        try:
            # Use database-specific date function
            stmt = (
                select(
                    func.date(self.model.timestamp).label("activity_date"),
                    func.count(self.model.id).label("count"),
                )
                .where(
                    and_(
                        self.model.timestamp >= start_date,
                        self.model.timestamp <= end_date,
                    )
                )
                .group_by(func.date(self.model.timestamp))
                .order_by(func.date(self.model.timestamp))
            )

            results = self.db_session.execute(stmt).all()
            daily_counts = {str(date): count for date, count in results}

            logger.debug(
                f"Generated daily activity counts for {len(daily_counts)} days"
            )
            return daily_counts

        except SQLAlchemyError as e:
            logger.error(
                f"Database error generating daily activity counts for date range {start_date} to {end_date}: {e}"
            )
            self._handle_db_exception(e, "activity_log")
            raise

    def get_recent_activity(self, limit: int = 50) -> List[ActivityLog]:
        """
        Get most recent activity logs.

        Args:
            limit: Maximum number of records to return

        Returns:
            List[ActivityLog]: List of recent activity logs

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Retrieving {limit} most recent activity logs")

        try:
            stmt = select(self.model).order_by(desc(self.model.timestamp)).limit(limit)
            results = list(self.db_session.scalars(stmt).all())

            logger.debug(f"Retrieved {len(results)} recent activity logs")
            return results

        except SQLAlchemyError as e:
            logger.error(f"Database error retrieving recent activity logs: {e}")
            self._handle_db_exception(e, "activity_log")
            raise

    def delete_old_logs(self, cutoff_date: datetime) -> int:
        """
        Delete activity logs older than the cutoff date.

        Args:
            cutoff_date: Date before which logs should be deleted

        Returns:
            int: Number of logs deleted

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Deleting activity logs older than {cutoff_date}")

        try:
            # First count how many will be deleted
            count_stmt = select(func.count(self.model.id)).where(
                self.model.timestamp < cutoff_date
            )
            count_to_delete = self.db_session.scalar(count_stmt) or 0

            if count_to_delete == 0:
                logger.debug("No old activity logs to delete")
                return 0

            # Delete the old logs
            from sqlalchemy import delete

            delete_stmt = delete(self.model).where(self.model.timestamp < cutoff_date)
            result = self.db_session.execute(delete_stmt)

            logger.debug(f"Deleted {result.rowcount} old activity logs")
            return result.rowcount

        except SQLAlchemyError as e:
            logger.error(f"Database error deleting old activity logs: {e}")
            self._handle_db_exception(e, "activity_log")
            raise

    def get_unique_users_count(self, start_date: datetime, end_date: datetime) -> int:
        """
        Get count of unique users who performed actions within a date range.

        Args:
            start_date: Start date for filtering
            end_date: End date for filtering

        Returns:
            int: Number of unique users

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Counting unique users for date range {start_date} to {end_date}")

        try:
            stmt = select(func.count(func.distinct(self.model.user_id))).where(
                and_(
                    self.model.timestamp >= start_date,
                    self.model.timestamp <= end_date,
                    self.model.user_id.isnot(None),
                )
            )
            result = self.db_session.scalar(stmt)

            logger.debug(f"Found {result} unique users in date range")
            return result or 0

        except SQLAlchemyError as e:
            logger.error(
                f"Database error counting unique users for date range {start_date} to {end_date}: {e}"
            )
            self._handle_db_exception(e, "activity_log")
            raise

    def get_unique_entities_count(
        self, start_date: datetime, end_date: datetime
    ) -> int:
        """
        Get count of unique entities affected within a date range.

        Args:
            start_date: Start date for filtering
            end_date: End date for filtering

        Returns:
            int: Number of unique entities

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(
            f"Counting unique entities for date range {start_date} to {end_date}"
        )

        try:
            stmt = select(
                func.count(
                    func.distinct(
                        func.concat(self.model.entity_type, ":", self.model.entity_id)
                    )
                )
            ).where(
                and_(
                    self.model.timestamp >= start_date,
                    self.model.timestamp <= end_date,
                    self.model.entity_type.isnot(None),
                    self.model.entity_id.isnot(None),
                )
            )
            result = self.db_session.scalar(stmt)

            logger.debug(f"Found {result} unique entities in date range")
            return result or 0

        except SQLAlchemyError as e:
            logger.error(
                f"Database error counting unique entities for date range {start_date} to {end_date}: {e}"
            )
            self._handle_db_exception(e, "activity_log")
            raise

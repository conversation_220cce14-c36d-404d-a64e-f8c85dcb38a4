# backend/core/standards/standards_manager.py
"""
Standards Manager - Central interface for standards validation.

This module provides the main interface for applying and validating
against various engineering standards.
"""

import logging
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from enum import Enum

from core.errors.exceptions import StandardComplianceError, InvalidInputError

logger = logging.getLogger(__name__)


class StandardType(Enum):
    """Enumeration of supported standards."""

    TR_50410 = "tr_50410"
    IEC_60079_30_1 = "iec_60079_30_1"
    GENERAL = "general"


@dataclass
class ValidationResult:
    """Result of standards validation."""

    is_compliant: bool
    standard: str
    violations: List[str]
    warnings: List[str]
    applied_factors: Dict[str, float]
    metadata: Dict[str, Any]


@dataclass
class StandardsContext:
    """Context for standards application."""

    project_standards: List[str]
    hazardous_area_zone: Optional[str] = None
    gas_group: Optional[str] = None
    temperature_class: Optional[str] = None
    application_type: str = "general"
    safety_level: str = "standard"  # standard, high, critical


class StandardsManager:
    """
    Central manager for engineering standards validation and application.

    This class provides a unified interface for applying various engineering
    standards and validating design parameters against their requirements.
    """

    def __init__(self):
        """Initialize the standards manager."""
        self.active_standards: List[str] = []
        self.context: Optional[StandardsContext] = None
        logger.debug("StandardsManager initialized")

    def set_context(self, context: StandardsContext) -> None:
        """
        Set the standards context for validation.

        Args:
            context: Standards context with project requirements
        """
        self.context = context
        self.active_standards = context.project_standards
        logger.info(f"Standards context set: {context.project_standards}")

    def validate_heat_loss_calculation(
        self, heat_loss_result: Dict[str, Any], design_parameters: Dict[str, Any]
    ) -> ValidationResult:
        """
        Validate heat loss calculation results against applicable standards.

        Args:
            heat_loss_result: Results from heat loss calculation
            design_parameters: Design parameters used in calculation

        Returns:
            ValidationResult: Validation results and compliance status

        Raises:
            StandardComplianceError: If critical violations are found
        """
        logger.info("Validating heat loss calculation against standards")

        violations = []
        warnings = []
        applied_factors = {}

        try:
            # Validate against each active standard
            for standard in self.active_standards:
                if standard == StandardType.TR_50410.value:
                    result = self._validate_tr_50410_heat_loss(
                        heat_loss_result, design_parameters
                    )
                    violations.extend(result.violations)
                    warnings.extend(result.warnings)
                    applied_factors.update(result.applied_factors)

                elif standard == StandardType.IEC_60079_30_1.value:
                    result = self._validate_iec_60079_heat_loss(
                        heat_loss_result, design_parameters
                    )
                    violations.extend(result.violations)
                    warnings.extend(result.warnings)
                    applied_factors.update(result.applied_factors)

            # Apply general standards
            general_result = self._validate_general_heat_loss(
                heat_loss_result, design_parameters
            )
            violations.extend(general_result.violations)
            warnings.extend(general_result.warnings)
            applied_factors.update(general_result.applied_factors)

            # Check for critical violations
            critical_violations = [v for v in violations if "CRITICAL" in v]
            if critical_violations:
                raise StandardComplianceError(
                    details=f"Critical standards violations found: {'; '.join(critical_violations)}"
                )

            is_compliant = len(violations) == 0

            result = ValidationResult(
                is_compliant=is_compliant,
                standard=", ".join(self.active_standards),
                violations=violations,
                warnings=warnings,
                applied_factors=applied_factors,
                metadata={
                    "validation_type": "heat_loss",
                    "context": self.context.__dict__ if self.context else None,
                },
            )

            logger.info(
                f"Heat loss validation complete: compliant={is_compliant}, "
                f"violations={len(violations)}, warnings={len(warnings)}"
            )

            return result

        except StandardComplianceError:
            raise
        except Exception as e:
            logger.error(f"Standards validation failed: {e}", exc_info=True)
            raise StandardComplianceError(f"Standards validation failed: {str(e)}")

    def validate_cable_selection(
        self, cable_result: Dict[str, Any], design_parameters: Dict[str, Any]
    ) -> ValidationResult:
        """
        Validate cable selection against applicable standards.

        Args:
            cable_result: Results from cable sizing calculation
            design_parameters: Design parameters used in calculation

        Returns:
            ValidationResult: Validation results and compliance status
        """
        logger.info("Validating cable selection against standards")

        violations = []
        warnings = []
        applied_factors = {}

        try:
            # Validate against each active standard
            for standard in self.active_standards:
                if standard == StandardType.IEC_60079_30_1.value:
                    result = self._validate_iec_60079_cable(
                        cable_result, design_parameters
                    )
                    violations.extend(result.violations)
                    warnings.extend(result.warnings)
                    applied_factors.update(result.applied_factors)

            # Apply general electrical standards
            general_result = self._validate_general_electrical(
                cable_result, design_parameters
            )
            violations.extend(general_result.violations)
            warnings.extend(general_result.warnings)
            applied_factors.update(general_result.applied_factors)

            # Check for critical violations
            critical_violations = [v for v in violations if "CRITICAL" in v]
            if critical_violations:
                raise StandardComplianceError(
                    details=f"Critical cable standards violations: {'; '.join(critical_violations)}"
                )

            is_compliant = len(violations) == 0

            result = ValidationResult(
                is_compliant=is_compliant,
                standard=", ".join(self.active_standards),
                violations=violations,
                warnings=warnings,
                applied_factors=applied_factors,
                metadata={
                    "validation_type": "cable_selection",
                    "context": self.context.__dict__ if self.context else None,
                },
            )

            logger.info(f"Cable validation complete: compliant={is_compliant}")
            return result

        except StandardComplianceError:
            raise
        except Exception as e:
            logger.error(f"Cable standards validation failed: {e}", exc_info=True)
            raise StandardComplianceError(
                f"Cable standards validation failed: {str(e)}"
            )

    def apply_safety_factors(
        self, calculation_result: Dict[str, Any], calculation_type: str
    ) -> Dict[str, Any]:
        """
        Apply appropriate safety factors based on active standards.

        Args:
            calculation_result: Original calculation results
            calculation_type: Type of calculation (heat_loss, cable_sizing, etc.)

        Returns:
            Dict with safety factors applied
        """
        logger.debug(f"Applying safety factors for {calculation_type}")

        result = calculation_result.copy()
        applied_factors = {}

        try:
            # Get safety factors from active standards
            for standard in self.active_standards:
                if standard == StandardType.TR_50410.value:
                    factors = self._get_tr_50410_safety_factors(calculation_type)
                    applied_factors.update(factors)

                elif standard == StandardType.IEC_60079_30_1.value:
                    factors = self._get_iec_60079_safety_factors(calculation_type)
                    applied_factors.update(factors)

            # Apply general safety factors
            general_factors = self._get_general_safety_factors(calculation_type)
            applied_factors.update(general_factors)

            # Apply factors to results
            if calculation_type == "heat_loss":
                if "power_safety_factor" in applied_factors:
                    result["required_power"] *= applied_factors["power_safety_factor"]

                if "heat_loss_safety_factor" in applied_factors:
                    result["total_heat_loss"] *= applied_factors[
                        "heat_loss_safety_factor"
                    ]

            elif calculation_type == "cable_sizing":
                if "current_safety_factor" in applied_factors:
                    result["current_draw"] *= applied_factors["current_safety_factor"]

                if "power_derating_factor" in applied_factors:
                    result["cable_power_per_meter"] *= applied_factors[
                        "power_derating_factor"
                    ]

            result["applied_safety_factors"] = applied_factors

            logger.debug(f"Applied safety factors: {applied_factors}")
            return result

        except Exception as e:
            logger.error(f"Failed to apply safety factors: {e}", exc_info=True)
            raise StandardComplianceError(f"Failed to apply safety factors: {str(e)}")

    def _validate_tr_50410_heat_loss(
        self, heat_loss_result: Dict[str, Any], design_parameters: Dict[str, Any]
    ) -> ValidationResult:
        """Validate against TR 50410 standard for heat loss."""
        from .tr_50410.heat_loss_factors import validate_tr_50410_heat_loss

        return validate_tr_50410_heat_loss(heat_loss_result, design_parameters)

    def _validate_iec_60079_heat_loss(
        self, heat_loss_result: Dict[str, Any], design_parameters: Dict[str, Any]
    ) -> ValidationResult:
        """Validate against IEC 60079-30-1 for heat loss."""
        from .iec_60079_30_1.temperature_class_limits import validate_temperature_class

        violations = []
        warnings = []

        # Check surface temperature against T-class limits
        surface_temp = heat_loss_result.get("surface_temperature", 0)
        if self.context and self.context.temperature_class:
            if not validate_temperature_class(
                surface_temp, self.context.temperature_class
            ):
                violations.append(
                    f"CRITICAL: Surface temperature {surface_temp}°C exceeds T-class {self.context.temperature_class} limits"
                )

        return ValidationResult(
            is_compliant=len(violations) == 0,
            standard="IEC 60079-30-1",
            violations=violations,
            warnings=warnings,
            applied_factors={},
            metadata={},
        )

    def _validate_iec_60079_cable(
        self, cable_result: Dict[str, Any], design_parameters: Dict[str, Any]
    ) -> ValidationResult:
        """Validate cable selection against IEC 60079-30-1."""
        from .iec_60079_30_1.hazardous_area_compliance import (
            validate_hazardous_area_compliance,
        )

        violations = []
        warnings = []

        # Validate hazardous area compliance
        if self.context and self.context.hazardous_area_zone:
            try:
                validate_hazardous_area_compliance(
                    cable_temp_class=self.context.temperature_class or "T4",
                    zone_type=self.context.hazardous_area_zone,
                    gas_group=self.context.gas_group or "IIA",
                )
            except StandardComplianceError as e:
                violations.append(f"CRITICAL: {e.detail}")

        return ValidationResult(
            is_compliant=len(violations) == 0,
            standard="IEC 60079-30-1",
            violations=violations,
            warnings=warnings,
            applied_factors={},
            metadata={},
        )

    def _validate_general_heat_loss(
        self, heat_loss_result: Dict[str, Any], design_parameters: Dict[str, Any]
    ) -> ValidationResult:
        """Apply general heat loss validation."""
        violations = []
        warnings = []

        # Check reasonable heat loss values
        heat_loss_rate = heat_loss_result.get("heat_loss_rate", 0)
        if heat_loss_rate > 1000:  # W/m
            warnings.append(
                f"High heat loss rate: {heat_loss_rate:.1f} W/m - consider additional insulation"
            )

        if heat_loss_rate < 1:  # W/m
            warnings.append(
                f"Very low heat loss rate: {heat_loss_rate:.1f} W/m - verify calculation inputs"
            )

        return ValidationResult(
            is_compliant=True,
            standard="General",
            violations=violations,
            warnings=warnings,
            applied_factors={},
            metadata={},
        )

    def _validate_general_electrical(
        self, cable_result: Dict[str, Any], design_parameters: Dict[str, Any]
    ) -> ValidationResult:
        """Apply general electrical validation."""
        violations = []
        warnings = []

        # Check voltage drop
        voltage_drop = cable_result.get("voltage_drop", 0)
        supply_voltage = design_parameters.get("supply_voltage", 230)
        voltage_drop_percent = (voltage_drop / supply_voltage) * 100

        if voltage_drop_percent > 5:
            violations.append(
                f"Voltage drop {voltage_drop_percent:.1f}% exceeds 5% limit"
            )
        elif voltage_drop_percent > 3:
            warnings.append(f"Voltage drop {voltage_drop_percent:.1f}% is high (>3%)")

        return ValidationResult(
            is_compliant=len(violations) == 0,
            standard="General Electrical",
            violations=violations,
            warnings=warnings,
            applied_factors={},
            metadata={},
        )

    def _get_tr_50410_safety_factors(self, calculation_type: str) -> Dict[str, float]:
        """Get TR 50410 safety factors."""
        if calculation_type == "heat_loss":
            return {"power_safety_factor": 1.2}
        return {}

    def _get_iec_60079_safety_factors(self, calculation_type: str) -> Dict[str, float]:
        """Get IEC 60079 safety factors."""
        if calculation_type == "cable_sizing":
            return {"power_derating_factor": 0.8}  # 20% derating for hazardous areas
        return {}

    def _get_general_safety_factors(self, calculation_type: str) -> Dict[str, float]:
        """Get general safety factors."""
        factors = {}

        if self.context and self.context.safety_level == "high":
            if calculation_type == "heat_loss":
                factors["power_safety_factor"] = 1.3
            elif calculation_type == "cable_sizing":
                factors["current_safety_factor"] = 1.25
        elif self.context and self.context.safety_level == "critical":
            if calculation_type == "heat_loss":
                factors["power_safety_factor"] = 1.5
            elif calculation_type == "cable_sizing":
                factors["current_safety_factor"] = 1.5

        return factors

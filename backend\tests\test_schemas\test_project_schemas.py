# backend/tests/test_schemas/test_project_schemas.py
"""
Unit tests for Project schemas.

Tests validation, serialization, and deserialization of Project-related
Pydantic schemas.
"""

import pytest
from pydantic import ValidationError

from core.models.enums import InstallationEnvironment
from core.schemas.project_schemas import (
    ProjectCreateSchema,
    ProjectListResponseSchema,
    ProjectReadSchema,
    ProjectSummarySchema,
    ProjectUpdateSchema,
)

# Mark all tests in this file as schema tests
pytestmark = [pytest.mark.schema, pytest.mark.unit, pytest.mark.project]


class TestProjectCreateSchema:
    """Test ProjectCreateSchema validation and functionality."""

    def test_valid_project_creation(self, sample_project_create_data):
        """Test creating a valid project schema."""
        schema = ProjectCreateSchema(**sample_project_create_data)

        assert schema.name == "Test Heat Tracing Project"
        assert schema.project_number == "HT-TEST-001"
        assert schema.min_ambient_temp_c == -20.0
        assert schema.max_ambient_temp_c == 40.0
        assert schema.desired_maintenance_temp_c == 65.0
        assert schema.installation_environment == InstallationEnvironment.OUTDOOR

    def test_project_number_normalization(self):
        """Test that project number is normalized to uppercase."""
        data = {
            "name": "Test Project",
            "project_number": "ht-test-001",
            "min_ambient_temp_c": -20.0,
            "max_ambient_temp_c": 40.0,
            "desired_maintenance_temp_c": 65.0,
        }
        schema = ProjectCreateSchema(**data)
        assert schema.project_number == "HT-TEST-001"

    def test_name_validation_empty(self):
        """Test validation fails for empty project name."""
        data = {
            "name": "",
            "project_number": "TEST-001",
            "min_ambient_temp_c": -20.0,
            "max_ambient_temp_c": 40.0,
            "desired_maintenance_temp_c": 65.0,
        }
        with pytest.raises(ValidationError) as exc_info:
            ProjectCreateSchema(**data)

        assert "String should have at least 3 characters" in str(exc_info.value)

    def test_name_validation_whitespace(self):
        """Test validation fails for whitespace-only project name."""
        data = {
            "name": "   ",
            "project_number": "TEST-001",
            "min_ambient_temp_c": -20.0,
            "max_ambient_temp_c": 40.0,
            "desired_maintenance_temp_c": 65.0,
        }
        with pytest.raises(ValidationError) as exc_info:
            ProjectCreateSchema(**data)

        assert "Project name cannot be empty" in str(exc_info.value)

    def test_project_number_validation_empty(self):
        """Test validation fails for empty project number."""
        data = {
            "name": "Test Project",
            "project_number": "",
            "min_ambient_temp_c": -20.0,
            "max_ambient_temp_c": 40.0,
            "desired_maintenance_temp_c": 65.0,
        }
        with pytest.raises(ValidationError) as exc_info:
            ProjectCreateSchema(**data)

        assert "String should have at least 3 characters" in str(exc_info.value)

    def test_project_number_validation_invalid_chars(self):
        """Test validation fails for invalid characters in project number."""
        data = {
            "name": "Test Project",
            "project_number": "TEST@001!",
            "min_ambient_temp_c": -20.0,
            "max_ambient_temp_c": 40.0,
            "desired_maintenance_temp_c": 65.0,
        }
        with pytest.raises(ValidationError) as exc_info:
            ProjectCreateSchema(**data)

        assert (
            "can only contain letters, numbers, dots, hyphens, and underscores"
            in str(exc_info.value)
        )

    def test_temperature_range_validation(self):
        """Test validation fails when max temp <= min temp."""
        data = {
            "name": "Test Project",
            "project_number": "TEST-001",
            "min_ambient_temp_c": 40.0,
            "max_ambient_temp_c": 20.0,  # Invalid: max < min
            "desired_maintenance_temp_c": 65.0,
        }
        with pytest.raises(ValidationError) as exc_info:
            ProjectCreateSchema(**data)

        assert "Maximum ambient temperature must be greater than minimum" in str(
            exc_info.value
        )

    def test_temperature_bounds_validation(self):
        """Test temperature bounds validation."""
        # Test min temp too low
        data = {
            "name": "Test Project",
            "project_number": "TEST-001",
            "min_ambient_temp_c": -60.0,  # Below minimum
            "max_ambient_temp_c": 40.0,
            "desired_maintenance_temp_c": 65.0,
        }
        with pytest.raises(ValidationError):
            ProjectCreateSchema(**data)

        # Test max temp too high
        data["min_ambient_temp_c"] = -20.0
        data["max_ambient_temp_c"] = 90.0  # Above maximum
        with pytest.raises(ValidationError):
            ProjectCreateSchema(**data)

        # Test maintenance temp too high
        data["max_ambient_temp_c"] = 40.0
        data["desired_maintenance_temp_c"] = 350.0  # Above maximum
        with pytest.raises(ValidationError):
            ProjectCreateSchema(**data)

    def test_wind_speed_validation(self):
        """Test wind speed validation."""
        data = {
            "name": "Test Project",
            "project_number": "TEST-001",
            "min_ambient_temp_c": -20.0,
            "max_ambient_temp_c": 40.0,
            "desired_maintenance_temp_c": 65.0,
            "wind_speed_ms": -5.0,  # Invalid: negative
        }
        with pytest.raises(ValidationError):
            ProjectCreateSchema(**data)

    def test_voltages_json_validation_valid(self):
        """Test valid voltages JSON validation."""
        data = {
            "name": "Test Project",
            "project_number": "TEST-001",
            "min_ambient_temp_c": -20.0,
            "max_ambient_temp_c": 40.0,
            "desired_maintenance_temp_c": 65.0,
            "available_voltages_json": "[120, 240, 480]",
        }
        schema = ProjectCreateSchema(**data)
        assert schema.available_voltages_json == "[120, 240, 480]"

    def test_voltages_json_validation_invalid_json(self):
        """Test invalid JSON format validation."""
        data = {
            "name": "Test Project",
            "project_number": "TEST-001",
            "min_ambient_temp_c": -20.0,
            "max_ambient_temp_c": 40.0,
            "desired_maintenance_temp_c": 65.0,
            "available_voltages_json": "invalid json",
        }
        with pytest.raises(ValidationError) as exc_info:
            ProjectCreateSchema(**data)

        assert "Invalid JSON format" in str(exc_info.value)

    def test_voltages_json_validation_not_array(self):
        """Test validation fails when voltages is not an array."""
        data = {
            "name": "Test Project",
            "project_number": "TEST-001",
            "min_ambient_temp_c": -20.0,
            "max_ambient_temp_c": 40.0,
            "desired_maintenance_temp_c": 65.0,
            "available_voltages_json": '{"voltage": 120}',  # Object, not array
        }
        with pytest.raises(ValidationError) as exc_info:
            ProjectCreateSchema(**data)

        assert "Voltages must be a JSON array" in str(exc_info.value)

    def test_voltages_json_validation_negative_voltage(self):
        """Test validation fails for negative voltages."""
        data = {
            "name": "Test Project",
            "project_number": "TEST-001",
            "min_ambient_temp_c": -20.0,
            "max_ambient_temp_c": 40.0,
            "desired_maintenance_temp_c": 65.0,
            "available_voltages_json": "[120, -240, 480]",  # Negative voltage
        }
        with pytest.raises(ValidationError) as exc_info:
            ProjectCreateSchema(**data)

        assert "All voltages must be positive numbers" in str(exc_info.value)


class TestProjectUpdateSchema:
    """Test ProjectUpdateSchema validation and functionality."""

    def test_valid_partial_update(self):
        """Test valid partial update with only some fields."""
        data = {
            "name": "Updated Project Name",
            "max_ambient_temp_c": 45.0,
        }
        schema = ProjectUpdateSchema(**data)

        assert schema.name == "Updated Project Name"
        assert schema.max_ambient_temp_c == 45.0
        assert schema.project_number is None  # Not provided

    def test_empty_update(self):
        """Test empty update (no fields provided)."""
        schema = ProjectUpdateSchema()

        # All fields should be None
        assert schema.name is None
        assert schema.project_number is None
        assert schema.min_ambient_temp_c is None

    def test_update_validation_same_as_create(self):
        """Test that update validation uses same rules as create."""
        # Test invalid name
        with pytest.raises(ValidationError):
            ProjectUpdateSchema(name="")

        # Test invalid project number
        with pytest.raises(ValidationError):
            ProjectUpdateSchema(project_number="invalid@chars")

        # Test invalid voltages JSON
        with pytest.raises(ValidationError):
            ProjectUpdateSchema(available_voltages_json="invalid json")


class TestProjectReadSchema:
    """Test ProjectReadSchema functionality."""

    def test_from_orm_conversion(self, sample_project_orm):
        """Test conversion from ORM model to read schema."""
        schema = ProjectReadSchema.model_validate(sample_project_orm)

        assert schema.id == sample_project_orm.id
        assert schema.name == sample_project_orm.name
        assert schema.project_number == sample_project_orm.project_number
        assert schema.created_at == sample_project_orm.created_at
        assert schema.updated_at == sample_project_orm.updated_at
        assert schema.is_deleted == sample_project_orm.is_deleted

    def test_read_schema_includes_all_fields(self, sample_project_orm):
        """Test that read schema includes all expected fields."""
        schema = ProjectReadSchema.model_validate(sample_project_orm)

        # Check that all important fields are present
        required_fields = [
            "id",
            "name",
            "project_number",
            "description",
            "designer",
            "notes",
            "min_ambient_temp_c",
            "max_ambient_temp_c",
            "desired_maintenance_temp_c",
            "wind_speed_ms",
            "installation_environment",
            "available_voltages_json",
            "default_cable_manufacturer",
            "default_control_device_manufacturer",
            "created_at",
            "updated_at",
            "is_deleted",
            "deleted_at",
            "deleted_by_user_id",
        ]

        for field in required_fields:
            assert hasattr(schema, field), f"Missing field: {field}"


class TestProjectSummarySchema:
    """Test ProjectSummarySchema functionality."""

    def test_summary_includes_essential_fields(self, sample_project_orm):
        """Test that summary schema includes only essential fields."""
        schema = ProjectSummarySchema.model_validate(sample_project_orm)

        # Check essential fields are present
        assert schema.id == sample_project_orm.id
        assert schema.name == sample_project_orm.name
        assert schema.project_number == sample_project_orm.project_number
        assert schema.description == sample_project_orm.description
        assert schema.designer == sample_project_orm.designer
        assert schema.created_at == sample_project_orm.created_at
        assert schema.updated_at == sample_project_orm.updated_at

    def test_summary_excludes_detailed_fields(self, sample_project_orm):
        """Test that summary schema excludes detailed fields."""
        schema = ProjectSummarySchema.model_validate(sample_project_orm)

        # These fields should not be in summary
        excluded_fields = [
            "min_ambient_temp_c",
            "max_ambient_temp_c",
            "desired_maintenance_temp_c",
            "wind_speed_ms",
            "installation_environment",
            "available_voltages_json",
            "default_cable_manufacturer",
            "default_control_device_manufacturer",
            "is_deleted",
            "deleted_at",
            "deleted_by_user_id",
        ]

        for field in excluded_fields:
            assert not hasattr(schema, field), (
                f"Summary should not include field: {field}"
            )


class TestProjectListResponseSchema:
    """Test ProjectListResponseSchema functionality."""

    def test_list_response_structure(self, multiple_projects_orm):
        """Test list response schema structure."""
        # Create summary schemas from ORM objects
        project_summaries = [
            ProjectSummarySchema.model_validate(p) for p in multiple_projects_orm[:5]
        ]

        response = ProjectListResponseSchema(
            projects=project_summaries, total=15, page=1, per_page=5, total_pages=3
        )

        assert len(response.projects) == 5
        assert response.total == 15
        assert response.page == 1
        assert response.per_page == 5
        assert response.total_pages == 3

    def test_empty_list_response(self):
        """Test list response with no projects."""
        response = ProjectListResponseSchema(
            projects=[], total=0, page=1, per_page=10, total_pages=1
        )

        assert len(response.projects) == 0
        assert response.total == 0
        assert response.total_pages == 1

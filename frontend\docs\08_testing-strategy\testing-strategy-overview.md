## **Testing Strategy Overview**

This document outlines the comprehensive testing strategy for the Ultimate Electrical Designer application, covering both frontend and backend testing approaches.

---

## **Frontend Testing Strategy**

### **Unit Testing**

* **Tool:** Vitest
* **Focus:** Individual functions, pure components, custom hooks, and utility modules.
* **Coverage:** Aim for high unit test coverage to ensure the correctness of isolated logic.

### **Integration Testing**

* **Tools:** Vitest and React Testing Library
* **Focus:** Interactions between multiple components, integration with API mocks, and complex user flows within a limited scope.
* **Methodology:** Test components from the user's perspective, interacting with elements as a user would.

### **End-to-End (E2E) Testing**

* **Tool:** Cypress
* **Focus:** Full user journeys through the application, simulating real browser interactions.
* **Purpose:** Catch regressions, validate critical paths, and ensure the entire system works as expected.
* **Scope:** Limited to critical user flows due to higher maintenance cost.

### **Mocking**

* **MSW (Mock Service Worker):** For mocking API requests in integration and E2E tests, allowing for consistent and reliable testing without relying on a live backend.

---

## **Backend Testing Strategy**

The backend uses a comprehensive testing framework with multiple test categories and convenient development tools.

### **Testing Framework**

* **Tool:** pytest with comprehensive configuration
* **Coverage:** pytest-cov with >90% coverage requirement
* **Database:** SQLite in-memory for test isolation
* **Configuration:** Defined in `backend/pyproject.toml`

### **Test Categories**

#### **By Test Type**
* **Unit Tests:** Individual component testing (`-m unit`)
* **Integration Tests:** Component interaction testing (`-m integration`)
* **API Tests:** Endpoint testing (`-m api`)
* **Database Tests:** Repository and database layer testing (`-m database`)
* **Security Tests:** Authentication and security validation (`-m security`)
* **Performance Tests:** Load and benchmark testing (`-m performance`)
* **Smoke Tests:** Quick functionality validation (`-m smoke`)

#### **By Layer**
* **Schema Tests:** Pydantic validation testing (`-m schema`)
* **Repository Tests:** Data access layer testing (`-m repository`)
* **Service Tests:** Business logic testing (`-m service`)
* **Calculations Tests:** Engineering calculations testing (`-m calculations`)
* **Standards Tests:** Compliance validation testing (`-m standards`)

#### **By Entity**
* **Project Tests:** Project entity testing (`-m project`)
* **Component Tests:** Component entity testing (`-m component`)
* **Heat Tracing Tests:** Heat tracing entity testing (`-m heat_tracing`)
* **Electrical Tests:** Electrical entity testing (`-m electrical`)
* **Switchboard Tests:** Switchboard entity testing (`-m switchboard`)
* **User Tests:** User entity testing (`-m user`)
* **Document Tests:** Document entity testing (`-m document`)

### **Development Tools**

#### **Test Runner Script**
Location: `backend/scripts/test_runner.py`

```bash
# Run specific test categories
python scripts/test_runner.py unit --coverage
python scripts/test_runner.py integration --coverage
python scripts/test_runner.py api --coverage

# Run entity-specific tests
python scripts/test_runner.py project --coverage
python scripts/test_runner.py heat_tracing --coverage

# Run layer-specific tests
python scripts/test_runner.py schema --coverage
python scripts/test_runner.py service --coverage

# Run all tests
python scripts/test_runner.py all --coverage

# Run code quality checks
python scripts/test_runner.py quality
```

#### **Makefile Commands**
Location: `backend/Makefile`

```bash
# Quick testing commands
make test              # Run all tests with coverage
make test-unit         # Run unit tests
make test-integration  # Run integration tests
make test-api          # Run API tests

# Layer-specific testing
make test-schemas      # Run schema validation tests
make test-services     # Run service layer tests
make test-repositories # Run repository layer tests

# Entity-specific testing
make test-project      # Run project entity tests
make test-heat-tracing # Run heat tracing entity tests
make test-electrical   # Run electrical entity tests

# Code quality
make quality           # Run all quality checks
make lint              # Run Ruff linting
make format            # Run Ruff formatting
make type-check        # Run MyPy type checking
make security-check    # Run Bandit security analysis

# Development workflows
make pre-commit        # Quick pre-commit validation
make pre-merge         # Full pre-merge validation
make test-improvement  # Test Suite Quality Improvement workflow
```

### **Code Quality Tools**

* **Ruff:** Fast Python linter and formatter with security-focused rules
* **MyPy:** Static type checking
* **Bandit:** Security vulnerability scanning
* **pytest-cov:** Code coverage analysis

---

## **Test Environment**

### **Frontend**
* **CI/CD Integration:** All tests (unit, integration, E2E) will be run automatically as part of the CI/CD pipeline.
* **Local Development:** Developers should be able to run tests locally with ease.

### **Backend**
* **Environment Variables:** Configured in `pyproject.toml` for consistent testing
* **Database:** SQLite in-memory for test isolation
* **Coverage Requirements:** >90% overall coverage, 100% test pass rate
* **CI/CD Integration:** Automated testing with quality gates

### **Development Workflow**

#### **Daily Development**
```bash
# Frontend
npm test              # Run frontend tests
npm run test:coverage # Run with coverage

# Backend
make test-smoke       # Quick backend validation
make pre-commit       # Pre-commit checks
```

#### **Pre-commit Validation**
```bash
# Frontend
npm run lint
npm run type-check
npm test

# Backend
make quality          # Lint, format, type-check, security
make test-smoke       # Quick test validation
```

#### **Pre-merge Validation**
```bash
# Frontend
npm run test:all      # All frontend tests
npm run build         # Build validation

# Backend
make pre-merge        # Full backend validation
make test             # Complete test suite
```

---

## **Test Suite Quality Improvement**

The backend includes a comprehensive Test Suite Quality Improvement Action Plan with specific tools and workflows:

### **Current Status**
* **481 tests** with 76.7% pass rate (target: 100%)
* **58% coverage** (target: >90%)
* **Structured 4-phase improvement plan**

### **Improvement Workflow**
```bash
# Run the complete improvement workflow
make test-improvement

# Phase-specific validation
make test-schemas      # Phase 1: Schema validation
make test-database     # Phase 1: Database initialization
make test-services     # Phase 1: Service layer methods
make test-api          # Phase 2: API layer improvements
```

### **Documentation**
* **Detailed Guide:** `backend/docs/test-suite-improvement-prompt.md`
* **Progress Tracking:** `backend/docs/implementation-progress.md`
* **Configuration:** `backend/pyproject.toml`

This comprehensive testing strategy ensures high code quality, reliability, and maintainability across both frontend and backend components of the Ultimate Electrical Designer application.

# backend/tests/test_switchboard_api.py
"""
Tests for Switchboard API Routes

This module tests the REST API endpoints for switchboard operations.
"""

import pytest
from fastapi.testclient import TestClient
from unittest.mock import Mock, patch

# Mark all tests in this file
pytestmark = [pytest.mark.unit, pytest.mark.api, pytest.mark.integration, pytest.mark.switchboard]

from api.v1.switchboard_routes import router
from core.errors.exceptions import NotFoundError, BusinessLogicError


@pytest.fixture
def client():
    """Create a test client for the switchboard router."""
    from fastapi import FastAPI

    app = FastAPI()
    app.include_router(router)
    return TestClient(app)


@pytest.fixture
def mock_switchboard_service():
    """Create a mock switchboard service."""
    return Mock()


class TestSwitchboardAPI:
    """Test switchboard API endpoints."""

    @patch("api.v1.switchboard_routes.SwitchboardService")
    @patch("api.v1.switchboard_routes.get_db_session")
    def test_create_switchboard_success(self, mock_get_db, mock_service_class, client):
        """Test successful switchboard creation via API."""
        # Arrange
        mock_service = Mock()
        mock_service_class.return_value = mock_service

        mock_switchboard = Mock()
        mock_switchboard.id = 1
        mock_switchboard.name = "Test Switchboard"
        mock_switchboard.project_id = 1
        mock_switchboard.voltage_level_v = 415
        mock_switchboard.number_of_phases = 3

        mock_service.create_switchboard.return_value = mock_switchboard

        switchboard_data = {
            "name": "Test Switchboard",
            "project_id": 1,
            "voltage_level_v": 415,
            "number_of_phases": 3,
        }

        # Act
        response = client.post("/switchboards", json=switchboard_data)

        # Assert
        assert response.status_code == 201
        data = response.json()
        assert data["name"] == "Test Switchboard"
        assert data["voltage_level_v"] == 415

    @patch("api.v1.switchboard_routes.SwitchboardService")
    @patch("api.v1.switchboard_routes.get_db_session")
    def test_create_switchboard_invalid_data(
        self, mock_get_db, mock_service_class, client
    ):
        """Test switchboard creation with invalid data."""
        # Arrange
        switchboard_data = {
            "name": "",  # Invalid empty name
            "project_id": 1,
            "voltage_level_v": 415,
            "number_of_phases": 3,
        }

        # Act
        response = client.post("/switchboards", json=switchboard_data)

        # Assert
        assert response.status_code == 422  # Validation error

    @patch("api.v1.switchboard_routes.SwitchboardService")
    @patch("api.v1.switchboard_routes.get_db_session")
    def test_get_switchboard_success(self, mock_get_db, mock_service_class, client):
        """Test successful switchboard retrieval via API."""
        # Arrange
        mock_service = Mock()
        mock_service_class.return_value = mock_service

        mock_switchboard = Mock()
        mock_switchboard.id = 1
        mock_switchboard.name = "Test Switchboard"

        mock_service.get_switchboard.return_value = mock_switchboard

        # Act
        response = client.get("/switchboards/1")

        # Assert
        assert response.status_code == 200
        data = response.json()
        assert data["name"] == "Test Switchboard"

    @patch("api.v1.switchboard_routes.SwitchboardService")
    @patch("api.v1.switchboard_routes.get_db_session")
    def test_get_switchboard_not_found(self, mock_get_db, mock_service_class, client):
        """Test switchboard retrieval when not found."""
        # Arrange
        mock_service = Mock()
        mock_service_class.return_value = mock_service

        mock_service.get_switchboard.side_effect = NotFoundError(
            code="SWITCHBOARD_NOT_FOUND", detail="Switchboard 999 not found"
        )

        # Act
        response = client.get("/switchboards/999")

        # Assert
        assert response.status_code == 404
        data = response.json()
        assert "not found" in data["detail"]

    @patch("api.v1.switchboard_routes.SwitchboardService")
    @patch("api.v1.switchboard_routes.get_db_session")
    def test_list_switchboards_success(self, mock_get_db, mock_service_class, client):
        """Test successful switchboard listing via API."""
        # Arrange
        mock_service = Mock()
        mock_service_class.return_value = mock_service

        mock_switchboards = [Mock(), Mock()]
        mock_service.switchboard_repo.get_by_project_id.return_value = mock_switchboards
        mock_service.switchboard_repo.count_by_project.return_value = 2

        # Act
        response = client.get("/switchboards?project_id=1")

        # Assert
        assert response.status_code == 200
        data = response.json()
        assert "switchboards" in data
        assert data["total"] == 2

    @patch("api.v1.switchboard_routes.SwitchboardService")
    @patch("api.v1.switchboard_routes.get_db_session")
    def test_update_switchboard_success(self, mock_get_db, mock_service_class, client):
        """Test successful switchboard update via API."""
        # Arrange
        mock_service = Mock()
        mock_service_class.return_value = mock_service

        mock_updated = Mock()
        mock_updated.id = 1
        mock_updated.name = "Updated Switchboard"

        mock_service.update_switchboard.return_value = mock_updated

        update_data = {
            "name": "Updated Switchboard",
        }

        # Act
        response = client.put("/switchboards/1", json=update_data)

        # Assert
        assert response.status_code == 200
        data = response.json()
        assert data["name"] == "Updated Switchboard"

    @patch("api.v1.switchboard_routes.SwitchboardService")
    @patch("api.v1.switchboard_routes.get_db_session")
    def test_delete_switchboard_success(self, mock_get_db, mock_service_class, client):
        """Test successful switchboard deletion via API."""
        # Arrange
        mock_service = Mock()
        mock_service_class.return_value = mock_service

        mock_service.delete_switchboard.return_value = True

        # Act
        response = client.delete("/switchboards/1")

        # Assert
        assert response.status_code == 204

    @patch("api.v1.switchboard_routes.SwitchboardService")
    @patch("api.v1.switchboard_routes.get_db_session")
    def test_create_feeder_success(self, mock_get_db, mock_service_class, client):
        """Test successful feeder creation via API."""
        # Arrange
        mock_service = Mock()
        mock_service_class.return_value = mock_service

        mock_feeder = Mock()
        mock_feeder.id = 1
        mock_feeder.name = "Test Feeder"
        mock_feeder.switchboard_id = 1

        mock_service.create_feeder.return_value = mock_feeder

        feeder_data = {
            "name": "Test Feeder",
            "switchboard_id": 1,
        }

        # Act
        response = client.post("/switchboards/feeders", json=feeder_data)

        # Assert
        assert response.status_code == 201
        data = response.json()
        assert data["name"] == "Test Feeder"

    @patch("api.v1.switchboard_routes.SwitchboardService")
    @patch("api.v1.switchboard_routes.get_db_session")
    def test_get_feeder_success(self, mock_get_db, mock_service_class, client):
        """Test successful feeder retrieval via API."""
        # Arrange
        mock_service = Mock()
        mock_service_class.return_value = mock_service

        mock_feeder = Mock()
        mock_feeder.id = 1
        mock_feeder.name = "Test Feeder"

        mock_service.get_feeder.return_value = mock_feeder

        # Act
        response = client.get("/switchboards/feeders/1")

        # Assert
        assert response.status_code == 200
        data = response.json()
        assert data["name"] == "Test Feeder"

    @patch("api.v1.switchboard_routes.SwitchboardService")
    @patch("api.v1.switchboard_routes.get_db_session")
    def test_add_switchboard_component_success(
        self, mock_get_db, mock_service_class, client
    ):
        """Test successful component addition via API."""
        # Arrange
        mock_service = Mock()
        mock_service_class.return_value = mock_service

        mock_component = Mock()
        mock_component.id = 1
        mock_component.switchboard_id = 1
        mock_component.component_id = 15

        mock_service.add_switchboard_component.return_value = mock_component

        component_data = {
            "switchboard_id": 1,
            "component_id": 15,
            "quantity": 2,
        }

        # Act
        response = client.post("/switchboards/components", json=component_data)

        # Assert
        assert response.status_code == 201
        data = response.json()
        assert data["switchboard_id"] == 1
        assert data["component_id"] == 15

    @patch("api.v1.switchboard_routes.SwitchboardService")
    @patch("api.v1.switchboard_routes.get_db_session")
    def test_get_load_summary_success(self, mock_get_db, mock_service_class, client):
        """Test successful load summary retrieval via API."""
        # Arrange
        mock_service = Mock()
        mock_service_class.return_value = mock_service

        mock_summary = Mock()
        mock_summary.switchboard_id = 1
        mock_summary.total_connected_load_kw = 50.0
        mock_summary.feeder_count = 5

        mock_service.get_switchboard_load_summary.return_value = mock_summary

        # Act
        response = client.get("/switchboards/1/load-summary")

        # Assert
        assert response.status_code == 200
        data = response.json()
        assert data["switchboard_id"] == 1
        assert data["total_connected_load_kw"] == 50.0

    @patch("api.v1.switchboard_routes.SwitchboardService")
    @patch("api.v1.switchboard_routes.get_db_session")
    def test_get_capacity_analysis_success(
        self, mock_get_db, mock_service_class, client
    ):
        """Test successful capacity analysis retrieval via API."""
        # Arrange
        mock_service = Mock()
        mock_service_class.return_value = mock_service

        mock_analysis = Mock()
        mock_analysis.switchboard_id = 1
        mock_analysis.rated_capacity_kva = 200.0
        mock_analysis.is_overloaded = False

        mock_service.get_switchboard_capacity_analysis.return_value = mock_analysis

        # Act
        response = client.get("/switchboards/1/capacity-analysis")

        # Assert
        assert response.status_code == 200
        data = response.json()
        assert data["switchboard_id"] == 1
        assert data["rated_capacity_kva"] == 200.0

    @patch("api.v1.switchboard_routes.SwitchboardService")
    @patch("api.v1.switchboard_routes.get_db_session")
    def test_business_logic_error_handling(
        self, mock_get_db, mock_service_class, client
    ):
        """Test business logic error handling in API."""
        # Arrange
        mock_service = Mock()
        mock_service_class.return_value = mock_service

        mock_service.create_switchboard.side_effect = BusinessLogicError(
            code="INVALID_VOLTAGE_LEVEL", detail="Voltage level exceeds maximum allowed"
        )

        switchboard_data = {
            "name": "Test Switchboard",
            "project_id": 1,
            "voltage_level_v": 60000,  # Invalid
            "number_of_phases": 3,
        }

        # Act
        response = client.post("/switchboards", json=switchboard_data)

        # Assert
        assert response.status_code == 400
        data = response.json()
        assert "voltage level" in data["detail"].lower()

<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_718ce007.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">58%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button current">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.0">coverage.py v7.8.0</a>,
            created at 2025-06-01 22:35 +0300
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">class<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_bfd9059a16e3fa4e_main_router_py.html">backend\api\main_router.py</a></td>
                <td class="name left"><a href="z_bfd9059a16e3fa4e_main_router_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_activity_log_routes_py.html">backend\api\v1\activity_log_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_activity_log_routes_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>165</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="119 165">72%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_document_routes_py.html">backend\api\v1\document_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_document_routes_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>173</td>
                <td>124</td>
                <td>0</td>
                <td class="right" data-ratio="49 173">28%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_heat_tracing_routes_py.html">backend\api\v1\heat_tracing_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_heat_tracing_routes_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>209</td>
                <td>170</td>
                <td>0</td>
                <td class="right" data-ratio="39 209">19%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_project_routes_py.html">backend\api\v1\project_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_project_routes_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>68</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="22 68">32%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_switchboard_routes_py.html">backend\api\v1\switchboard_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_switchboard_routes_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>157</td>
                <td>52</td>
                <td>0</td>
                <td class="right" data-ratio="105 157">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_user_routes_py.html">backend\api\v1\user_routes.py</a></td>
                <td class="name left"><a href="z_599edd1a5c19dad3_user_routes_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>169</td>
                <td>49</td>
                <td>0</td>
                <td class="right" data-ratio="120 169">71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f96405ab037cc57a___init___py.html">backend\core\calculations\__init__.py</a></td>
                <td class="name left"><a href="z_f96405ab037cc57a___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f96405ab037cc57a_calculation_service_py.html#t24">backend\core\calculations\calculation_service.py</a></td>
                <td class="name left"><a href="z_f96405ab037cc57a_calculation_service_py.html#t24"><data value='CalculationInput'>CalculationInput</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f96405ab037cc57a_calculation_service_py.html#t31">backend\core\calculations\calculation_service.py</a></td>
                <td class="name left"><a href="z_f96405ab037cc57a_calculation_service_py.html#t31"><data value='CalculationResult'>CalculationResult</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f96405ab037cc57a_calculation_service_py.html#t48">backend\core\calculations\calculation_service.py</a></td>
                <td class="name left"><a href="z_f96405ab037cc57a_calculation_service_py.html#t48"><data value='HeatLossInput'>HeatLossInput</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f96405ab037cc57a_calculation_service_py.html#t62">backend\core\calculations\calculation_service.py</a></td>
                <td class="name left"><a href="z_f96405ab037cc57a_calculation_service_py.html#t62"><data value='HeatLossResult'>HeatLossResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f96405ab037cc57a_calculation_service_py.html#t72">backend\core\calculations\calculation_service.py</a></td>
                <td class="name left"><a href="z_f96405ab037cc57a_calculation_service_py.html#t72"><data value='CableSizingInput'>CableSizingInput</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f96405ab037cc57a_calculation_service_py.html#t84">backend\core\calculations\calculation_service.py</a></td>
                <td class="name left"><a href="z_f96405ab037cc57a_calculation_service_py.html#t84"><data value='CableSizingResult'>CableSizingResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f96405ab037cc57a_calculation_service_py.html#t95">backend\core\calculations\calculation_service.py</a></td>
                <td class="name left"><a href="z_f96405ab037cc57a_calculation_service_py.html#t95"><data value='CalculationService'>CalculationService</data></a></td>
                <td>61</td>
                <td>60</td>
                <td>0</td>
                <td class="right" data-ratio="1 61">2%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f96405ab037cc57a_calculation_service_py.html">backend\core\calculations\calculation_service.py</a></td>
                <td class="name left"><a href="z_f96405ab037cc57a_calculation_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>55</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="55 55">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_da9c56813bb6ca47___init___py.html">backend\core\calculations\electrical_sizing\__init__.py</a></td>
                <td class="name left"><a href="z_da9c56813bb6ca47___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_da9c56813bb6ca47_cable_sizing_py.html">backend\core\calculations\electrical_sizing\cable_sizing.py</a></td>
                <td class="name left"><a href="z_da9c56813bb6ca47_cable_sizing_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>82</td>
                <td>71</td>
                <td>0</td>
                <td class="right" data-ratio="11 82">13%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_da9c56813bb6ca47_voltage_drop_py.html">backend\core\calculations\electrical_sizing\voltage_drop.py</a></td>
                <td class="name left"><a href="z_da9c56813bb6ca47_voltage_drop_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>82</td>
                <td>72</td>
                <td>0</td>
                <td class="right" data-ratio="10 82">12%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee7a23c30801740___init___py.html">backend\core\calculations\heat_loss\__init__.py</a></td>
                <td class="name left"><a href="z_aee7a23c30801740___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee7a23c30801740_insulation_properties_py.html">backend\core\calculations\heat_loss\insulation_properties.py</a></td>
                <td class="name left"><a href="z_aee7a23c30801740_insulation_properties_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>76</td>
                <td>76</td>
                <td>0</td>
                <td class="right" data-ratio="0 76">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee7a23c30801740_pipe_heat_loss_py.html">backend\core\calculations\heat_loss\pipe_heat_loss.py</a></td>
                <td class="name left"><a href="z_aee7a23c30801740_pipe_heat_loss_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>99</td>
                <td>99</td>
                <td>0</td>
                <td class="right" data-ratio="0 99">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee7a23c30801740_vessel_heat_loss_py.html">backend\core\calculations\heat_loss\vessel_heat_loss.py</a></td>
                <td class="name left"><a href="z_aee7a23c30801740_vessel_heat_loss_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>64</td>
                <td>64</td>
                <td>0</td>
                <td class="right" data-ratio="0 64">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ab7802d47b18b7f9___init___py.html">backend\core\database\__init__.py</a></td>
                <td class="name left"><a href="z_ab7802d47b18b7f9___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ab7802d47b18b7f9_engine_py.html">backend\core\database\engine.py</a></td>
                <td class="name left"><a href="z_ab7802d47b18b7f9_engine_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>83</td>
                <td>63</td>
                <td>0</td>
                <td class="right" data-ratio="20 83">24%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ab7802d47b18b7f9_initialization_py.html">backend\core\database\initialization.py</a></td>
                <td class="name left"><a href="z_ab7802d47b18b7f9_initialization_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>106</td>
                <td>85</td>
                <td>0</td>
                <td class="right" data-ratio="21 106">20%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ab7802d47b18b7f9_session_py.html">backend\core\database\session.py</a></td>
                <td class="name left"><a href="z_ab7802d47b18b7f9_session_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>78</td>
                <td>52</td>
                <td>0</td>
                <td class="right" data-ratio="26 78">33%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_71ce108dda7d12d3_error_factory_py.html#t6">backend\core\errors\error_factory.py</a></td>
                <td class="name left"><a href="z_71ce108dda7d12d3_error_factory_py.html#t6"><data value='ErrorFactory'>ErrorFactory</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_71ce108dda7d12d3_error_factory_py.html">backend\core\errors\error_factory.py</a></td>
                <td class="name left"><a href="z_71ce108dda7d12d3_error_factory_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_71ce108dda7d12d3_error_registry_py.html">backend\core\errors\error_registry.py</a></td>
                <td class="name left"><a href="z_71ce108dda7d12d3_error_registry_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_71ce108dda7d12d3_error_templates_py.html">backend\core\errors\error_templates.py</a></td>
                <td class="name left"><a href="z_71ce108dda7d12d3_error_templates_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_71ce108dda7d12d3_exceptions_py.html#t5">backend\core\errors\exceptions.py</a></td>
                <td class="name left"><a href="z_71ce108dda7d12d3_exceptions_py.html#t5"><data value='BaseApplicationException'>BaseApplicationException</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_71ce108dda7d12d3_exceptions_py.html#t25">backend\core\errors\exceptions.py</a></td>
                <td class="name left"><a href="z_71ce108dda7d12d3_exceptions_py.html#t25"><data value='NotFoundError'>NotFoundError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_71ce108dda7d12d3_exceptions_py.html#t29">backend\core\errors\exceptions.py</a></td>
                <td class="name left"><a href="z_71ce108dda7d12d3_exceptions_py.html#t29"><data value='ProjectNotFoundError'>ProjectNotFoundError</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_71ce108dda7d12d3_exceptions_py.html#t40">backend\core\errors\exceptions.py</a></td>
                <td class="name left"><a href="z_71ce108dda7d12d3_exceptions_py.html#t40"><data value='DataValidationError'>DataValidationError</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_71ce108dda7d12d3_exceptions_py.html#t51">backend\core\errors\exceptions.py</a></td>
                <td class="name left"><a href="z_71ce108dda7d12d3_exceptions_py.html#t51"><data value='InvalidInputError'>InvalidInputError</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_71ce108dda7d12d3_exceptions_py.html#t62">backend\core\errors\exceptions.py</a></td>
                <td class="name left"><a href="z_71ce108dda7d12d3_exceptions_py.html#t62"><data value='DuplicateEntryError'>DuplicateEntryError</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_71ce108dda7d12d3_exceptions_py.html#t78">backend\core\errors\exceptions.py</a></td>
                <td class="name left"><a href="z_71ce108dda7d12d3_exceptions_py.html#t78"><data value='DatabaseError'>DatabaseError</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_71ce108dda7d12d3_exceptions_py.html#t94">backend\core\errors\exceptions.py</a></td>
                <td class="name left"><a href="z_71ce108dda7d12d3_exceptions_py.html#t94"><data value='ComponentNotFoundError'>ComponentNotFoundError</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_71ce108dda7d12d3_exceptions_py.html#t105">backend\core\errors\exceptions.py</a></td>
                <td class="name left"><a href="z_71ce108dda7d12d3_exceptions_py.html#t105"><data value='CalculationError'>CalculationError</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_71ce108dda7d12d3_exceptions_py.html#t116">backend\core\errors\exceptions.py</a></td>
                <td class="name left"><a href="z_71ce108dda7d12d3_exceptions_py.html#t116"><data value='StandardComplianceError'>StandardComplianceError</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_71ce108dda7d12d3_exceptions_py.html#t123">backend\core\errors\exceptions.py</a></td>
                <td class="name left"><a href="z_71ce108dda7d12d3_exceptions_py.html#t123"><data value='BusinessLogicError'>BusinessLogicError</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_71ce108dda7d12d3_exceptions_py.html">backend\core\errors\exceptions.py</a></td>
                <td class="name left"><a href="z_71ce108dda7d12d3_exceptions_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>23</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="23 23">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24___init___py.html">backend\core\models\__init__.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_activity_log_py.html#t11">backend\core\models\activity_log.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_activity_log_py.html#t11"><data value='ActivityLog'>ActivityLog</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_activity_log_py.html">backend\core\models\activity_log.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_activity_log_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_base_py.html#t12">backend\core\models\base.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_base_py.html#t12"><data value='CommonColumns'>CommonColumns</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_base_py.html#t37">backend\core\models\base.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_base_py.html#t37"><data value='SoftDeleteColumns'>SoftDeleteColumns</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_base_py.html#t62">backend\core\models\base.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_base_py.html#t62"><data value='EnumType'>EnumType</data></a></td>
                <td>16</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="12 16">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_base_py.html">backend\core\models\base.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_base_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>32</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="32 32">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_components_py.html#t14">backend\core\models\components.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_components_py.html#t14"><data value='ComponentCategory'>ComponentCategory</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_components_py.html#t43">backend\core\models\components.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_components_py.html#t43"><data value='Component'>Component</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_components_py.html">backend\core\models\components.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_components_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>22</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="22 22">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_documents_py.html#t10">backend\core\models\documents.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_documents_py.html#t10"><data value='ImportedDataRevision'>ImportedDataRevision</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_documents_py.html#t47">backend\core\models\documents.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_documents_py.html#t47"><data value='ExportedDocument'>ExportedDocument</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_documents_py.html#t88">backend\core\models\documents.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_documents_py.html#t88"><data value='CalculationStandard'>CalculationStandard</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_documents_py.html">backend\core\models\documents.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_documents_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>39</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="39 39">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_electrical_py.html#t13">backend\core\models\electrical.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_electrical_py.html#t13"><data value='ElectricalNode'>ElectricalNode</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_electrical_py.html#t85">backend\core\models\electrical.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_electrical_py.html#t85"><data value='CableRoute'>CableRoute</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_electrical_py.html#t147">backend\core\models\electrical.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_electrical_py.html#t147"><data value='CableSegment'>CableSegment</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_electrical_py.html#t204">backend\core\models\electrical.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_electrical_py.html#t204"><data value='LoadCalculation'>LoadCalculation</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_electrical_py.html#t301">backend\core\models\electrical.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_electrical_py.html#t301"><data value='VoltageDropCalculation'>VoltageDropCalculation</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_electrical_py.html">backend\core\models\electrical.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_electrical_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>134</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="134 134">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_enums_py.html#t3">backend\core\models\enums.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_enums_py.html#t3"><data value='InstallationEnvironment'>InstallationEnvironment</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_enums_py.html#t7">backend\core\models\enums.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_enums_py.html#t7"><data value='HTCircuitApplicationType'>HTCircuitApplicationType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_enums_py.html#t11">backend\core\models\enums.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_enums_py.html#t11"><data value='HeatingMethodType'>HeatingMethodType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_enums_py.html#t18">backend\core\models\enums.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_enums_py.html#t18"><data value='SwitchboardType'>SwitchboardType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_enums_py.html#t22">backend\core\models\enums.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_enums_py.html#t22"><data value='PipeMaterialType'>PipeMaterialType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_enums_py.html#t27">backend\core\models\enums.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_enums_py.html#t27"><data value='ControlCircuitType'>ControlCircuitType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_enums_py.html#t31">backend\core\models\enums.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_enums_py.html#t31"><data value='SensorType'>SensorType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_enums_py.html#t37">backend\core\models\enums.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_enums_py.html#t37"><data value='HeatTracingCableType'>HeatTracingCableType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_enums_py.html#t44">backend\core\models\enums.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_enums_py.html#t44"><data value='ElectricalComponentType'>ElectricalComponentType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_enums_py.html#t68">backend\core\models\enums.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_enums_py.html#t68"><data value='ElectricalNodeType'>ElectricalNodeType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_enums_py.html#t80">backend\core\models\enums.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_enums_py.html#t80"><data value='CableInstallationMethod'>CableInstallationMethod</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_enums_py.html">backend\core\models\enums.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_enums_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>75</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="75 75">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_heat_tracing_py.html#t19">backend\core\models\heat_tracing.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_heat_tracing_py.html#t19"><data value='Pipe'>Pipe</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_heat_tracing_py.html#t80">backend\core\models\heat_tracing.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_heat_tracing_py.html#t80"><data value='Vessel'>Vessel</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_heat_tracing_py.html#t135">backend\core\models\heat_tracing.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_heat_tracing_py.html#t135"><data value='ControlCircuit'>ControlCircuit</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_heat_tracing_py.html#t179">backend\core\models\heat_tracing.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_heat_tracing_py.html#t179"><data value='HTCircuit'>HTCircuit</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_heat_tracing_py.html#t240">backend\core\models\heat_tracing.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_heat_tracing_py.html#t240"><data value='ControlCircuitComponent'>ControlCircuitComponent</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_heat_tracing_py.html#t268">backend\core\models\heat_tracing.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_heat_tracing_py.html#t268"><data value='HTCircuitComponent'>HTCircuitComponent</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_heat_tracing_py.html">backend\core\models\heat_tracing.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_heat_tracing_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>128</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="128 128">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_project_py.html#t14">backend\core\models\project.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_project_py.html#t14"><data value='Project'>Project</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_project_py.html">backend\core\models\project.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_project_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>73</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="58 73">79%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_switchboard_py.html#t12">backend\core\models\switchboard.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_switchboard_py.html#t12"><data value='Switchboard'>Switchboard</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_switchboard_py.html#t43">backend\core\models\switchboard.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_switchboard_py.html#t43"><data value='Feeder'>Feeder</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_switchboard_py.html#t71">backend\core\models\switchboard.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_switchboard_py.html#t71"><data value='SwitchboardComponent'>SwitchboardComponent</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_switchboard_py.html#t99">backend\core\models\switchboard.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_switchboard_py.html#t99"><data value='FeederComponent'>FeederComponent</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_switchboard_py.html">backend\core\models\switchboard.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_switchboard_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>49</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="49 49">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_users_py.html#t16">backend\core\models\users.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_users_py.html#t16"><data value='User'>User</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_users_py.html#t56">backend\core\models\users.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_users_py.html#t56"><data value='UserPreference'>UserPreference</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_users_py.html">backend\core\models\users.py</a></td>
                <td class="name left"><a href="z_7ac94d1f4eebeb24_users_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>28</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="27 28">96%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_activity_log_repository_py.html#t45">backend\core\repositories\activity_log_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_activity_log_repository_py.html#t45"><data value='ActivityLogRepository'>ActivityLogRepository</data></a></td>
                <td>195</td>
                <td>61</td>
                <td>0</td>
                <td class="right" data-ratio="134 195">69%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_activity_log_repository_py.html">backend\core\repositories\activity_log_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_activity_log_repository_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>33</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="28 33">85%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_base_repository_py.html#t29">backend\core\repositories\base_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_base_repository_py.html#t29"><data value='BaseRepository'>BaseRepository</data></a></td>
                <td>46</td>
                <td>31</td>
                <td>0</td>
                <td class="right" data-ratio="15 46">33%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_base_repository_py.html">backend\core\repositories\base_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_base_repository_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>21</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="17 21">81%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_component_repository_py.html#t21">backend\core\repositories\component_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_component_repository_py.html#t21"><data value='ComponentRepository'>ComponentRepository</data></a></td>
                <td>63</td>
                <td>63</td>
                <td>0</td>
                <td class="right" data-ratio="0 63">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_component_repository_py.html#t239">backend\core\repositories\component_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_component_repository_py.html#t239"><data value='ComponentCategoryRepository'>ComponentCategoryRepository</data></a></td>
                <td>34</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="0 34">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_component_repository_py.html">backend\core\repositories\component_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_component_repository_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>20</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="20 20">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_document_repository_py.html#t30">backend\core\repositories\document_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_document_repository_py.html#t30"><data value='ImportedDataRevisionRepository'>ImportedDataRevisionRepository</data></a></td>
                <td>73</td>
                <td>71</td>
                <td>0</td>
                <td class="right" data-ratio="2 73">3%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_document_repository_py.html#t313">backend\core\repositories\document_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_document_repository_py.html#t313"><data value='ExportedDocumentRepository'>ExportedDocumentRepository</data></a></td>
                <td>73</td>
                <td>71</td>
                <td>0</td>
                <td class="right" data-ratio="2 73">3%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_document_repository_py.html#t594">backend\core\repositories\document_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_document_repository_py.html#t594"><data value='CalculationStandardRepository'>CalculationStandardRepository</data></a></td>
                <td>57</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="25 57">44%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_document_repository_py.html">backend\core\repositories\document_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_document_repository_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>32</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="32 32">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t47">backend\core\repositories\electrical_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t47"><data value='ElectricalNodeRepository'>ElectricalNodeRepository</data></a></td>
                <td>82</td>
                <td>53</td>
                <td>0</td>
                <td class="right" data-ratio="29 82">35%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t376">backend\core\repositories\electrical_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t376"><data value='CableRouteRepository'>CableRouteRepository</data></a></td>
                <td>89</td>
                <td>64</td>
                <td>0</td>
                <td class="right" data-ratio="25 89">28%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t714">backend\core\repositories\electrical_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t714"><data value='CableSegmentRepository'>CableSegmentRepository</data></a></td>
                <td>42</td>
                <td>40</td>
                <td>0</td>
                <td class="right" data-ratio="2 42">5%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t909">backend\core\repositories\electrical_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t909"><data value='LoadCalculationRepository'>LoadCalculationRepository</data></a></td>
                <td>43</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="21 43">49%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t1114">backend\core\repositories\electrical_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html#t1114"><data value='VoltageDropCalculationRepository'>VoltageDropCalculationRepository</data></a></td>
                <td>64</td>
                <td>36</td>
                <td>0</td>
                <td class="right" data-ratio="28 64">44%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html">backend\core\repositories\electrical_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>54</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="47 54">87%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html#t45">backend\core\repositories\heat_tracing_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html#t45"><data value='PipeRepository'>PipeRepository</data></a></td>
                <td>68</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="45 68">66%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html#t305">backend\core\repositories\heat_tracing_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html#t305"><data value='VesselRepository'>VesselRepository</data></a></td>
                <td>58</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="30 58">52%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html#t534">backend\core\repositories\heat_tracing_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html#t534"><data value='HTCircuitRepository'>HTCircuitRepository</data></a></td>
                <td>71</td>
                <td>40</td>
                <td>0</td>
                <td class="right" data-ratio="31 71">44%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html#t798">backend\core\repositories\heat_tracing_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html#t798"><data value='ControlCircuitRepository'>ControlCircuitRepository</data></a></td>
                <td>45</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="17 45">38%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html#t996">backend\core\repositories\heat_tracing_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html#t996"><data value='HeatTracingRepository'>HeatTracingRepository</data></a></td>
                <td>31</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="21 31">68%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html">backend\core\repositories\heat_tracing_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>49</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="42 49">86%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_project_repository_py.html#t24">backend\core\repositories\project_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_project_repository_py.html#t24"><data value='ProjectRepository'>ProjectRepository</data></a></td>
                <td>101</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="71 101">70%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_project_repository_py.html">backend\core\repositories\project_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_project_repository_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>19</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="19 19">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_switchboard_repository_py.html#t45">backend\core\repositories\switchboard_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_switchboard_repository_py.html#t45"><data value='SwitchboardRepository'>SwitchboardRepository</data></a></td>
                <td>72</td>
                <td>72</td>
                <td>0</td>
                <td class="right" data-ratio="0 72">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_switchboard_repository_py.html#t345">backend\core\repositories\switchboard_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_switchboard_repository_py.html#t345"><data value='FeederRepository'>FeederRepository</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_switchboard_repository_py.html#t446">backend\core\repositories\switchboard_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_switchboard_repository_py.html#t446"><data value='SwitchboardComponentRepository'>SwitchboardComponentRepository</data></a></td>
                <td>32</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="0 32">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_switchboard_repository_py.html#t582">backend\core\repositories\switchboard_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_switchboard_repository_py.html#t582"><data value='FeederComponentRepository'>FeederComponentRepository</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_switchboard_repository_py.html">backend\core\repositories\switchboard_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_switchboard_repository_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>37</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="30 37">81%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_user_repository_py.html#t35">backend\core\repositories\user_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_user_repository_py.html#t35"><data value='UserRepository'>UserRepository</data></a></td>
                <td>83</td>
                <td>81</td>
                <td>0</td>
                <td class="right" data-ratio="2 83">2%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_user_repository_py.html#t305">backend\core\repositories\user_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_user_repository_py.html#t305"><data value='UserPreferenceRepository'>UserPreferenceRepository</data></a></td>
                <td>47</td>
                <td>47</td>
                <td>0</td>
                <td class="right" data-ratio="0 47">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_user_repository_py.html">backend\core\repositories\user_repository.py</a></td>
                <td class="name left"><a href="z_6805001cdfee4172_user_repository_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>30</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="23 30">77%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_activity_log_schemas_py.html#t33">backend\core\schemas\activity_log_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_activity_log_schemas_py.html#t33"><data value='EventTypeEnum'>EventTypeEnum</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_activity_log_schemas_py.html#t71">backend\core\schemas\activity_log_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_activity_log_schemas_py.html#t71"><data value='EntityTypeEnum'>EntityTypeEnum</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_activity_log_schemas_py.html#t88">backend\core\schemas\activity_log_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_activity_log_schemas_py.html#t88"><data value='EventCategoryEnum'>EventCategoryEnum</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_activity_log_schemas_py.html#t106">backend\core\schemas\activity_log_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_activity_log_schemas_py.html#t106"><data value='ActivityLogBaseSchema'>ActivityLogBaseSchema</data></a></td>
                <td>13</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="12 13">92%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_activity_log_schemas_py.html#t148">backend\core\schemas\activity_log_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_activity_log_schemas_py.html#t148"><data value='ActivityLogCreateSchema'>ActivityLogCreateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_activity_log_schemas_py.html#t168">backend\core\schemas\activity_log_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_activity_log_schemas_py.html#t168"><data value='ActivityLogUpdateSchema'>ActivityLogUpdateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_activity_log_schemas_py.html#t181">backend\core\schemas\activity_log_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_activity_log_schemas_py.html#t181"><data value='ActivityLogReadSchema'>ActivityLogReadSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_activity_log_schemas_py.html#t208">backend\core\schemas\activity_log_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_activity_log_schemas_py.html#t208"><data value='ActivityLogSummarySchema'>ActivityLogSummarySchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_activity_log_schemas_py.html#t226">backend\core\schemas\activity_log_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_activity_log_schemas_py.html#t226"><data value='ActivityLogFilterSchema'>ActivityLogFilterSchema</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_activity_log_schemas_py.html#t282">backend\core\schemas\activity_log_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_activity_log_schemas_py.html#t282"><data value='AuditReportRequestSchema'>AuditReportRequestSchema</data></a></td>
                <td>11</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="10 11">91%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_activity_log_schemas_py.html#t356">backend\core\schemas\activity_log_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_activity_log_schemas_py.html#t356"><data value='AuditReportSummarySchema'>AuditReportSummarySchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_activity_log_schemas_py.html#t386">backend\core\schemas\activity_log_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_activity_log_schemas_py.html#t386"><data value='AuditReportResponseSchema'>AuditReportResponseSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_activity_log_schemas_py.html#t414">backend\core\schemas\activity_log_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_activity_log_schemas_py.html#t414"><data value='SecurityEventSchema'>SecurityEventSchema</data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_activity_log_schemas_py.html#t467">backend\core\schemas\activity_log_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_activity_log_schemas_py.html#t467"><data value='ActivityLogPaginatedResponseSchema'>ActivityLogPaginatedResponseSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_activity_log_schemas_py.html#t501">backend\core\schemas\activity_log_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_activity_log_schemas_py.html#t501"><data value='EventCategoryMappingSchema'>EventCategoryMappingSchema</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_activity_log_schemas_py.html">backend\core\schemas\activity_log_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_activity_log_schemas_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>146</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="146 146">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_base_py.html#t14">backend\core\schemas\base.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_base_py.html#t14"><data value='BaseSchema'>BaseSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_base_py.html#t24">backend\core\schemas\base.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_base_py.html#t24"><data value='BaseReadSchema'>BaseReadSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_base_py.html#t32">backend\core\schemas\base.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_base_py.html#t32"><data value='BaseSoftDeleteSchema'>BaseSoftDeleteSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_base_py.html#t40">backend\core\schemas\base.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_base_py.html#t40"><data value='BaseNamedSchema'>BaseNamedSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_base_py.html#t47">backend\core\schemas\base.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_base_py.html#t47"><data value='PaginationSchema'>PaginationSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_base_py.html#t54">backend\core\schemas\base.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_base_py.html#t54"><data value='PaginatedResponseSchema'>PaginatedResponseSchema</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_base_py.html#t77">backend\core\schemas\base.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_base_py.html#t77"><data value='TimestampMixin'>TimestampMixin</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_base_py.html#t84">backend\core\schemas\base.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_base_py.html#t84"><data value='SoftDeleteMixin'>SoftDeleteMixin</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_base_py.html#t92">backend\core\schemas\base.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_base_py.html#t92"><data value='NamedEntityMixin'>NamedEntityMixin</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_base_py.html">backend\core\schemas\base.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_base_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>39</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="39 39">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_component_schemas_py.html#t17">backend\core\schemas\component_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_component_schemas_py.html#t17"><data value='ComponentCategoryBaseSchema'>ComponentCategoryBaseSchema</data></a></td>
                <td>6</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="5 6">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_component_schemas_py.html#t41">backend\core\schemas\component_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_component_schemas_py.html#t41"><data value='ComponentCategoryCreateSchema'>ComponentCategoryCreateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_component_schemas_py.html#t59">backend\core\schemas\component_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_component_schemas_py.html#t59"><data value='ComponentCategoryUpdateSchema'>ComponentCategoryUpdateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_component_schemas_py.html#t83">backend\core\schemas\component_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_component_schemas_py.html#t83"><data value='ComponentCategoryReadSchema'>ComponentCategoryReadSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_component_schemas_py.html#t106">backend\core\schemas\component_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_component_schemas_py.html#t106"><data value='ComponentBaseSchema'>ComponentBaseSchema</data></a></td>
                <td>13</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="12 13">92%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_component_schemas_py.html#t153">backend\core\schemas\component_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_component_schemas_py.html#t153"><data value='ComponentCreateSchema'>ComponentCreateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_component_schemas_py.html#t172">backend\core\schemas\component_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_component_schemas_py.html#t172"><data value='ComponentUpdateSchema'>ComponentUpdateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_component_schemas_py.html#t210">backend\core\schemas\component_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_component_schemas_py.html#t210"><data value='ComponentReadSchema'>ComponentReadSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_component_schemas_py.html#t236">backend\core\schemas\component_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_component_schemas_py.html#t236"><data value='ComponentSummarySchema'>ComponentSummarySchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_component_schemas_py.html#t248">backend\core\schemas\component_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_component_schemas_py.html#t248"><data value='ComponentListResponseSchema'>ComponentListResponseSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_component_schemas_py.html#t276">backend\core\schemas\component_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_component_schemas_py.html#t276"><data value='ComponentCategoryListResponseSchema'>ComponentCategoryListResponseSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_component_schemas_py.html">backend\core\schemas\component_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_component_schemas_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>63</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="63 63">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_document_schemas_py.html#t23">backend\core\schemas\document_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_document_schemas_py.html#t23"><data value='ImportType'>ImportType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_document_schemas_py.html#t33">backend\core\schemas\document_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_document_schemas_py.html#t33"><data value='DocumentType'>DocumentType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_document_schemas_py.html#t44">backend\core\schemas\document_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_document_schemas_py.html#t44"><data value='FileFormat'>FileFormat</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_document_schemas_py.html#t58">backend\core\schemas\document_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_document_schemas_py.html#t58"><data value='ImportedDataRevisionBaseSchema'>ImportedDataRevisionBaseSchema</data></a></td>
                <td>13</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="11 13">85%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_document_schemas_py.html#t107">backend\core\schemas\document_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_document_schemas_py.html#t107"><data value='ImportedDataRevisionCreateSchema'>ImportedDataRevisionCreateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_document_schemas_py.html#t131">backend\core\schemas\document_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_document_schemas_py.html#t131"><data value='ImportedDataRevisionUpdateSchema'>ImportedDataRevisionUpdateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_document_schemas_py.html#t147">backend\core\schemas\document_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_document_schemas_py.html#t147"><data value='ImportedDataRevisionReadSchema'>ImportedDataRevisionReadSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_document_schemas_py.html#t178">backend\core\schemas\document_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_document_schemas_py.html#t178"><data value='ImportedDataRevisionSummarySchema'>ImportedDataRevisionSummarySchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_document_schemas_py.html#t198">backend\core\schemas\document_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_document_schemas_py.html#t198"><data value='ExportedDocumentBaseSchema'>ExportedDocumentBaseSchema</data></a></td>
                <td>6</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="5 6">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_document_schemas_py.html#t237">backend\core\schemas\document_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_document_schemas_py.html#t237"><data value='ExportedDocumentCreateSchema'>ExportedDocumentCreateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_document_schemas_py.html#t262">backend\core\schemas\document_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_document_schemas_py.html#t262"><data value='ExportedDocumentUpdateSchema'>ExportedDocumentUpdateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_document_schemas_py.html#t276">backend\core\schemas\document_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_document_schemas_py.html#t276"><data value='ExportedDocumentReadSchema'>ExportedDocumentReadSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_document_schemas_py.html#t308">backend\core\schemas\document_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_document_schemas_py.html#t308"><data value='ExportedDocumentSummarySchema'>ExportedDocumentSummarySchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_document_schemas_py.html#t328">backend\core\schemas\document_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_document_schemas_py.html#t328"><data value='CalculationStandardBaseSchema'>CalculationStandardBaseSchema</data></a></td>
                <td>18</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="16 18">89%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_document_schemas_py.html#t382">backend\core\schemas\document_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_document_schemas_py.html#t382"><data value='CalculationStandardCreateSchema'>CalculationStandardCreateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_document_schemas_py.html#t397">backend\core\schemas\document_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_document_schemas_py.html#t397"><data value='CalculationStandardUpdateSchema'>CalculationStandardUpdateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_document_schemas_py.html#t419">backend\core\schemas\document_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_document_schemas_py.html#t419"><data value='CalculationStandardReadSchema'>CalculationStandardReadSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_document_schemas_py.html#t443">backend\core\schemas\document_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_document_schemas_py.html#t443"><data value='CalculationStandardSummarySchema'>CalculationStandardSummarySchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_document_schemas_py.html#t460">backend\core\schemas\document_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_document_schemas_py.html#t460"><data value='FileUploadSchema'>FileUploadSchema</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_document_schemas_py.html#t495">backend\core\schemas\document_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_document_schemas_py.html#t495"><data value='FileDownloadSchema'>FileDownloadSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_document_schemas_py.html#t504">backend\core\schemas\document_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_document_schemas_py.html#t504"><data value='DocumentGenerationRequestSchema'>DocumentGenerationRequestSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_document_schemas_py.html#t540">backend\core\schemas\document_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_document_schemas_py.html#t540"><data value='ImportedDataRevisionListResponseSchema'>ImportedDataRevisionListResponseSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_document_schemas_py.html#t569">backend\core\schemas\document_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_document_schemas_py.html#t569"><data value='ExportedDocumentListResponseSchema'>ExportedDocumentListResponseSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_document_schemas_py.html#t598">backend\core\schemas\document_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_document_schemas_py.html#t598"><data value='CalculationStandardListResponseSchema'>CalculationStandardListResponseSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_document_schemas_py.html">backend\core\schemas\document_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_document_schemas_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>146</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="146 146">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t31">backend\core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t31"><data value='CableSizingCalculationInputSchema'>CableSizingCalculationInputSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t73">backend\core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t73"><data value='CableSizingCalculationResultSchema'>CableSizingCalculationResultSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t92">backend\core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t92"><data value='VoltageDropCalculationInputSchema'>VoltageDropCalculationInputSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t134">backend\core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t134"><data value='VoltageDropCalculationResultSchema'>VoltageDropCalculationResultSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t155">backend\core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t155"><data value='ElectricalStandardsValidationInputSchema'>ElectricalStandardsValidationInputSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t170">backend\core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t170"><data value='ElectricalStandardsValidationResultSchema'>ElectricalStandardsValidationResultSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t195">backend\core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t195"><data value='ElectricalNodeBaseSchema'>ElectricalNodeBaseSchema</data></a></td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t219">backend\core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t219"><data value='ElectricalNodeCreateSchema'>ElectricalNodeCreateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t251">backend\core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t251"><data value='ElectricalNodeUpdateSchema'>ElectricalNodeUpdateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t287">backend\core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t287"><data value='ElectricalNodeReadSchema'>ElectricalNodeReadSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t326">backend\core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t326"><data value='ElectricalNodeSummarySchema'>ElectricalNodeSummarySchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t346">backend\core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t346"><data value='CableRouteBaseSchema'>CableRouteBaseSchema</data></a></td>
                <td>6</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="5 6">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t386">backend\core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t386"><data value='CableRouteCreateSchema'>CableRouteCreateSchema</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t419">backend\core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t419"><data value='CableRouteUpdateSchema'>CableRouteUpdateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t448">backend\core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t448"><data value='CableRouteReadSchema'>CableRouteReadSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t487">backend\core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t487"><data value='CableRouteSummarySchema'>CableRouteSummarySchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t511">backend\core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t511"><data value='CableSegmentBaseSchema'>CableSegmentBaseSchema</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t549">backend\core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t549"><data value='CableSegmentCreateSchema'>CableSegmentCreateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t578">backend\core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t578"><data value='CableSegmentUpdateSchema'>CableSegmentUpdateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t616">backend\core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t616"><data value='CableSegmentReadSchema'>CableSegmentReadSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t663">backend\core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t663"><data value='CableSegmentSummarySchema'>CableSegmentSummarySchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t686">backend\core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t686"><data value='LoadCalculationBaseSchema'>LoadCalculationBaseSchema</data></a></td>
                <td>8</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="7 8">88%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t746">backend\core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t746"><data value='LoadCalculationCreateSchema'>LoadCalculationCreateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t781">backend\core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t781"><data value='LoadCalculationUpdateSchema'>LoadCalculationUpdateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t829">backend\core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t829"><data value='LoadCalculationReadSchema'>LoadCalculationReadSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t878">backend\core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t878"><data value='LoadCalculationSummarySchema'>LoadCalculationSummarySchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t898">backend\core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t898"><data value='VoltageDropCalculationBaseSchema'>VoltageDropCalculationBaseSchema</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t942">backend\core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t942"><data value='VoltageDropCalculationCreateSchema'>VoltageDropCalculationCreateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t972">backend\core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t972"><data value='VoltageDropCalculationUpdateSchema'>VoltageDropCalculationUpdateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t1023">backend\core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t1023"><data value='VoltageDropCalculationReadSchema'>VoltageDropCalculationReadSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t1078">backend\core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t1078"><data value='VoltageDropCalculationSummarySchema'>VoltageDropCalculationSummarySchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t1102">backend\core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t1102"><data value='ElectricalDesignInputSchema'>ElectricalDesignInputSchema</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t1146">backend\core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t1146"><data value='ElectricalDesignResultSchema'>ElectricalDesignResultSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t1183">backend\core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t1183"><data value='ElectricalNodeListResponseSchema'>ElectricalNodeListResponseSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t1212">backend\core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t1212"><data value='CableRouteListResponseSchema'>CableRouteListResponseSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t1243">backend\core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t1243"><data value='CableSegmentListResponseSchema'>CableSegmentListResponseSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t1273">backend\core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t1273"><data value='LoadCalculationListResponseSchema'>LoadCalculationListResponseSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t1302">backend\core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html#t1302"><data value='VoltageDropCalculationListResponseSchema'>VoltageDropCalculationListResponseSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html">backend\core\schemas\electrical_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>379</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="379 379">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_error_py.html#t5">backend\core\schemas\error.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_error_py.html#t5"><data value='ErrorResponseSchema'>ErrorResponseSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_error_py.html#t12">backend\core\schemas\error.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_error_py.html#t12"><data value='Config'>ErrorResponseSchema.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_error_py.html#t24">backend\core\schemas\error.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_error_py.html#t24"><data value='ValidationErrorDetail'>ValidationErrorDetail</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_error_py.html#t29">backend\core\schemas\error.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_error_py.html#t29"><data value='ValidationErrorsResponseSchema'>ValidationErrorsResponseSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_error_py.html#t32">backend\core\schemas\error.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_error_py.html#t32"><data value='Config'>ValidationErrorsResponseSchema.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_error_py.html">backend\core\schemas\error.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_error_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>18</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="18 18">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t32">backend\core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t32"><data value='HeatLossCalculationInputSchema'>HeatLossCalculationInputSchema</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t64">backend\core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t64"><data value='HeatLossCalculationResultSchema'>HeatLossCalculationResultSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t79">backend\core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t79"><data value='StandardsValidationInputSchema'>StandardsValidationInputSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t90">backend\core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t90"><data value='StandardsValidationResultSchema'>StandardsValidationResultSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t112">backend\core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t112"><data value='PipeBaseSchema'>PipeBaseSchema</data></a></td>
                <td>8</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="7 8">88%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t172">backend\core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t172"><data value='PipeCreateSchema'>PipeCreateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t209">backend\core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t209"><data value='PipeUpdateSchema'>PipeUpdateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t258">backend\core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t258"><data value='PipeReadSchema'>PipeReadSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t304">backend\core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t304"><data value='PipeSummarySchema'>PipeSummarySchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t324">backend\core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t324"><data value='VesselBaseSchema'>VesselBaseSchema</data></a></td>
                <td>14</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="12 14">86%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t381">backend\core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t381"><data value='VesselCreateSchema'>VesselCreateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t412">backend\core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t412"><data value='VesselUpdateSchema'>VesselUpdateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t454">backend\core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t454"><data value='VesselReadSchema'>VesselReadSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t497">backend\core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t497"><data value='VesselSummarySchema'>VesselSummarySchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t517">backend\core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t517"><data value='ControlCircuitBaseSchema'>ControlCircuitBaseSchema</data></a></td>
                <td>8</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="7 8">88%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t557">backend\core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t557"><data value='ControlCircuitCreateSchema'>ControlCircuitCreateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t578">backend\core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t578"><data value='ControlCircuitUpdateSchema'>ControlCircuitUpdateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t607">backend\core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t607"><data value='ControlCircuitReadSchema'>ControlCircuitReadSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t635">backend\core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t635"><data value='ControlCircuitSummarySchema'>ControlCircuitSummarySchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t652">backend\core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t652"><data value='HTCircuitBaseSchema'>HTCircuitBaseSchema</data></a></td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t689">backend\core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t689"><data value='HTCircuitCreateSchema'>HTCircuitCreateSchema</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t735">backend\core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t735"><data value='HTCircuitUpdateSchema'>HTCircuitUpdateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t776">backend\core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t776"><data value='HTCircuitReadSchema'>HTCircuitReadSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t827">backend\core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t827"><data value='HTCircuitSummarySchema'>HTCircuitSummarySchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t850">backend\core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t850"><data value='HeatTracingDesignInputSchema'>HeatTracingDesignInputSchema</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t883">backend\core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t883"><data value='HeatTracingDesignResultSchema'>HeatTracingDesignResultSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t914">backend\core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t914"><data value='PipeListResponseSchema'>PipeListResponseSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t941">backend\core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t941"><data value='VesselListResponseSchema'>VesselListResponseSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t968">backend\core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t968"><data value='HTCircuitListResponseSchema'>HTCircuitListResponseSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t998">backend\core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html#t998"><data value='ControlCircuitListResponseSchema'>ControlCircuitListResponseSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html">backend\core\schemas\heat_tracing_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>283</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="283 283">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_project_schemas_py.html#t18">backend\core\schemas\project_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_project_schemas_py.html#t18"><data value='ProjectBaseSchema'>ProjectBaseSchema</data></a></td>
                <td>9</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="8 9">89%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_project_schemas_py.html#t70">backend\core\schemas\project_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_project_schemas_py.html#t70"><data value='ProjectEnvironmentalSchema'>ProjectEnvironmentalSchema</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_project_schemas_py.html#t113">backend\core\schemas\project_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_project_schemas_py.html#t113"><data value='ProjectDefaultsSchema'>ProjectDefaultsSchema</data></a></td>
                <td>13</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="12 13">92%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_project_schemas_py.html#t154">backend\core\schemas\project_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_project_schemas_py.html#t154"><data value='ProjectCreateSchema'>ProjectCreateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_project_schemas_py.html#t178">backend\core\schemas\project_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_project_schemas_py.html#t178"><data value='ProjectUpdateSchema'>ProjectUpdateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_project_schemas_py.html#t270">backend\core\schemas\project_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_project_schemas_py.html#t270"><data value='ProjectReadSchema'>ProjectReadSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_project_schemas_py.html#t308">backend\core\schemas\project_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_project_schemas_py.html#t308"><data value='ProjectSummarySchema'>ProjectSummarySchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_project_schemas_py.html#t322">backend\core\schemas\project_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_project_schemas_py.html#t322"><data value='ProjectListResponseSchema'>ProjectListResponseSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_project_schemas_py.html">backend\core\schemas\project_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_project_schemas_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>76</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="76 76">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_switchboard_schemas_py.html#t26">backend\core\schemas\switchboard_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_switchboard_schemas_py.html#t26"><data value='SwitchboardBaseSchema'>SwitchboardBaseSchema</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_switchboard_schemas_py.html#t62">backend\core\schemas\switchboard_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_switchboard_schemas_py.html#t62"><data value='SwitchboardCreateSchema'>SwitchboardCreateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_switchboard_schemas_py.html#t81">backend\core\schemas\switchboard_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_switchboard_schemas_py.html#t81"><data value='SwitchboardUpdateSchema'>SwitchboardUpdateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_switchboard_schemas_py.html#t107">backend\core\schemas\switchboard_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_switchboard_schemas_py.html#t107"><data value='SwitchboardReadSchema'>SwitchboardReadSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_switchboard_schemas_py.html#t133">backend\core\schemas\switchboard_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_switchboard_schemas_py.html#t133"><data value='SwitchboardSummarySchema'>SwitchboardSummarySchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_switchboard_schemas_py.html#t152">backend\core\schemas\switchboard_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_switchboard_schemas_py.html#t152"><data value='FeederBaseSchema'>FeederBaseSchema</data></a></td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_switchboard_schemas_py.html#t168">backend\core\schemas\switchboard_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_switchboard_schemas_py.html#t168"><data value='FeederCreateSchema'>FeederCreateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_switchboard_schemas_py.html#t183">backend\core\schemas\switchboard_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_switchboard_schemas_py.html#t183"><data value='FeederUpdateSchema'>FeederUpdateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_switchboard_schemas_py.html#t194">backend\core\schemas\switchboard_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_switchboard_schemas_py.html#t194"><data value='FeederReadSchema'>FeederReadSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_switchboard_schemas_py.html#t216">backend\core\schemas\switchboard_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_switchboard_schemas_py.html#t216"><data value='FeederSummarySchema'>FeederSummarySchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_switchboard_schemas_py.html#t231">backend\core\schemas\switchboard_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_switchboard_schemas_py.html#t231"><data value='SwitchboardComponentBaseSchema'>SwitchboardComponentBaseSchema</data></a></td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_switchboard_schemas_py.html#t248">backend\core\schemas\switchboard_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_switchboard_schemas_py.html#t248"><data value='SwitchboardComponentCreateSchema'>SwitchboardComponentCreateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_switchboard_schemas_py.html#t266">backend\core\schemas\switchboard_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_switchboard_schemas_py.html#t266"><data value='SwitchboardComponentUpdateSchema'>SwitchboardComponentUpdateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_switchboard_schemas_py.html#t282">backend\core\schemas\switchboard_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_switchboard_schemas_py.html#t282"><data value='SwitchboardComponentReadSchema'>SwitchboardComponentReadSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_switchboard_schemas_py.html#t303">backend\core\schemas\switchboard_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_switchboard_schemas_py.html#t303"><data value='SwitchboardComponentSummarySchema'>SwitchboardComponentSummarySchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_switchboard_schemas_py.html#t320">backend\core\schemas\switchboard_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_switchboard_schemas_py.html#t320"><data value='FeederComponentBaseSchema'>FeederComponentBaseSchema</data></a></td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_switchboard_schemas_py.html#t337">backend\core\schemas\switchboard_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_switchboard_schemas_py.html#t337"><data value='FeederComponentCreateSchema'>FeederComponentCreateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_switchboard_schemas_py.html#t355">backend\core\schemas\switchboard_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_switchboard_schemas_py.html#t355"><data value='FeederComponentUpdateSchema'>FeederComponentUpdateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_switchboard_schemas_py.html#t371">backend\core\schemas\switchboard_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_switchboard_schemas_py.html#t371"><data value='FeederComponentReadSchema'>FeederComponentReadSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_switchboard_schemas_py.html#t392">backend\core\schemas\switchboard_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_switchboard_schemas_py.html#t392"><data value='FeederComponentSummarySchema'>FeederComponentSummarySchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_switchboard_schemas_py.html#t409">backend\core\schemas\switchboard_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_switchboard_schemas_py.html#t409"><data value='SwitchboardLoadSummarySchema'>SwitchboardLoadSummarySchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_switchboard_schemas_py.html#t426">backend\core\schemas\switchboard_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_switchboard_schemas_py.html#t426"><data value='SwitchboardCapacityAnalysisSchema'>SwitchboardCapacityAnalysisSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_switchboard_schemas_py.html#t449">backend\core\schemas\switchboard_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_switchboard_schemas_py.html#t449"><data value='SwitchboardPaginatedResponseSchema'>SwitchboardPaginatedResponseSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_switchboard_schemas_py.html#t479">backend\core\schemas\switchboard_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_switchboard_schemas_py.html#t479"><data value='FeederPaginatedResponseSchema'>FeederPaginatedResponseSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_switchboard_schemas_py.html#t503">backend\core\schemas\switchboard_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_switchboard_schemas_py.html#t503"><data value='SwitchboardComponentPaginatedResponseSchema'>SwitchboardComponentPaginatedResponseSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_switchboard_schemas_py.html#t531">backend\core\schemas\switchboard_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_switchboard_schemas_py.html#t531"><data value='FeederComponentPaginatedResponseSchema'>FeederComponentPaginatedResponseSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_switchboard_schemas_py.html">backend\core\schemas\switchboard_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_switchboard_schemas_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>141</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="141 141">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_user_schemas_py.html#t31">backend\core\schemas\user_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_user_schemas_py.html#t31"><data value='UserBaseSchema'>UserBaseSchema</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_user_schemas_py.html#t47">backend\core\schemas\user_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_user_schemas_py.html#t47"><data value='UserCreateSchema'>UserCreateSchema</data></a></td>
                <td>8</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="7 8">88%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_user_schemas_py.html#t86">backend\core\schemas\user_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_user_schemas_py.html#t86"><data value='UserUpdateSchema'>UserUpdateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_user_schemas_py.html#t99">backend\core\schemas\user_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_user_schemas_py.html#t99"><data value='UserReadSchema'>UserReadSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_user_schemas_py.html#t119">backend\core\schemas\user_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_user_schemas_py.html#t119"><data value='UserSummarySchema'>UserSummarySchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_user_schemas_py.html#t135">backend\core\schemas\user_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_user_schemas_py.html#t135"><data value='UserPreferenceBaseSchema'>UserPreferenceBaseSchema</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_user_schemas_py.html#t190">backend\core\schemas\user_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_user_schemas_py.html#t190"><data value='UserPreferenceCreateSchema'>UserPreferenceCreateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_user_schemas_py.html#t209">backend\core\schemas\user_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_user_schemas_py.html#t209"><data value='UserPreferenceUpdateSchema'>UserPreferenceUpdateSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_user_schemas_py.html#t247">backend\core\schemas\user_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_user_schemas_py.html#t247"><data value='UserPreferenceReadSchema'>UserPreferenceReadSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_user_schemas_py.html#t278">backend\core\schemas\user_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_user_schemas_py.html#t278"><data value='LoginRequestSchema'>LoginRequestSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_user_schemas_py.html#t294">backend\core\schemas\user_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_user_schemas_py.html#t294"><data value='LoginResponseSchema'>LoginResponseSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_user_schemas_py.html#t319">backend\core\schemas\user_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_user_schemas_py.html#t319"><data value='LogoutResponseSchema'>LogoutResponseSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_user_schemas_py.html#t333">backend\core\schemas\user_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_user_schemas_py.html#t333"><data value='PasswordChangeRequestSchema'>PasswordChangeRequestSchema</data></a></td>
                <td>8</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="6 8">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_user_schemas_py.html#t371">backend\core\schemas\user_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_user_schemas_py.html#t371"><data value='PasswordResetRequestSchema'>PasswordResetRequestSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_user_schemas_py.html#t385">backend\core\schemas\user_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_user_schemas_py.html#t385"><data value='PasswordResetConfirmSchema'>PasswordResetConfirmSchema</data></a></td>
                <td>8</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="6 8">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_user_schemas_py.html#t428">backend\core\schemas\user_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_user_schemas_py.html#t428"><data value='UserSessionSchema'>UserSessionSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_user_schemas_py.html#t456">backend\core\schemas\user_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_user_schemas_py.html#t456"><data value='UserPaginatedResponseSchema'>UserPaginatedResponseSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_user_schemas_py.html">backend\core\schemas\user_schemas.py</a></td>
                <td class="name left"><a href="z_e64413ff4bf96b58_user_schemas_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>98</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="98 98">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_activity_log_service_py.html#t77">backend\core\services\activity_log_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_activity_log_service_py.html#t77"><data value='ActivityLogService'>ActivityLogService</data></a></td>
                <td>189</td>
                <td>54</td>
                <td>0</td>
                <td class="right" data-ratio="135 189">71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_activity_log_service_py.html">backend\core\services\activity_log_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_activity_log_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>39</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="33 39">85%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_component_service_py.html#t39">backend\core\services\component_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_component_service_py.html#t39"><data value='ComponentService'>ComponentService</data></a></td>
                <td>129</td>
                <td>31</td>
                <td>0</td>
                <td class="right" data-ratio="98 129">76%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_component_service_py.html#t486">backend\core\services\component_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_component_service_py.html#t486"><data value='ComponentCategoryService'>ComponentCategoryService</data></a></td>
                <td>65</td>
                <td>65</td>
                <td>0</td>
                <td class="right" data-ratio="0 65">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_component_service_py.html">backend\core\services\component_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_component_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>23</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="23 23">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_document_service_py.html#t57">backend\core\services\document_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_document_service_py.html#t57"><data value='DocumentService'>DocumentService</data></a></td>
                <td>272</td>
                <td>253</td>
                <td>0</td>
                <td class="right" data-ratio="19 272">7%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_document_service_py.html">backend\core\services\document_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_document_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>36</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="36 36">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_electrical_service_py.html#t124">backend\core\services\electrical_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_electrical_service_py.html#t124"><data value='ElectricalService'>ElectricalService</data></a></td>
                <td>160</td>
                <td>70</td>
                <td>0</td>
                <td class="right" data-ratio="90 160">56%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_electrical_service_py.html">backend\core\services\electrical_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_electrical_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>33</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="21 33">64%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_heat_tracing_service_py.html#t96">backend\core\services\heat_tracing_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_heat_tracing_service_py.html#t96"><data value='HeatTracingService'>HeatTracingService</data></a></td>
                <td>282</td>
                <td>128</td>
                <td>0</td>
                <td class="right" data-ratio="154 282">55%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_heat_tracing_service_py.html">backend\core\services\heat_tracing_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_heat_tracing_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>44</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="34 44">77%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_project_service_py.html#t37">backend\core\services\project_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_project_service_py.html#t37"><data value='ProjectService'>ProjectService</data></a></td>
                <td>140</td>
                <td>56</td>
                <td>0</td>
                <td class="right" data-ratio="84 140">60%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_project_service_py.html">backend\core\services\project_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_project_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>18</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="18 18">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_switchboard_service_py.html#t87">backend\core\services\switchboard_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_switchboard_service_py.html#t87"><data value='SwitchboardService'>SwitchboardService</data></a></td>
                <td>153</td>
                <td>33</td>
                <td>0</td>
                <td class="right" data-ratio="120 153">78%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_switchboard_service_py.html">backend\core\services\switchboard_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_switchboard_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>33</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="23 33">70%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_user_service_py.html#t88">backend\core\services\user_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_user_service_py.html#t88"><data value='UserService'>UserService</data></a></td>
                <td>181</td>
                <td>90</td>
                <td>0</td>
                <td class="right" data-ratio="91 181">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_user_service_py.html">backend\core\services\user_service.py</a></td>
                <td class="name left"><a href="z_ff90eff64d9c93aa_user_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>41</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="32 41">78%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3d2460180cf335ae___init___py.html">backend\core\standards\__init__.py</a></td>
                <td class="name left"><a href="z_3d2460180cf335ae___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b97b1182853e7f12___init___py.html">backend\core\standards\iec_60079_30_1\__init__.py</a></td>
                <td class="name left"><a href="z_b97b1182853e7f12___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b97b1182853e7f12_hazardous_area_compliance_py.html">backend\core\standards\iec_60079_30_1\hazardous_area_compliance.py</a></td>
                <td class="name left"><a href="z_b97b1182853e7f12_hazardous_area_compliance_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>69</td>
                <td>69</td>
                <td>0</td>
                <td class="right" data-ratio="0 69">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b97b1182853e7f12_temperature_class_limits_py.html">backend\core\standards\iec_60079_30_1\temperature_class_limits.py</a></td>
                <td class="name left"><a href="z_b97b1182853e7f12_temperature_class_limits_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>40</td>
                <td>40</td>
                <td>0</td>
                <td class="right" data-ratio="0 40">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3d2460180cf335ae_standards_manager_py.html#t19">backend\core\standards\standards_manager.py</a></td>
                <td class="name left"><a href="z_3d2460180cf335ae_standards_manager_py.html#t19"><data value='StandardType'>StandardType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3d2460180cf335ae_standards_manager_py.html#t27">backend\core\standards\standards_manager.py</a></td>
                <td class="name left"><a href="z_3d2460180cf335ae_standards_manager_py.html#t27"><data value='ValidationResult'>ValidationResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3d2460180cf335ae_standards_manager_py.html#t38">backend\core\standards\standards_manager.py</a></td>
                <td class="name left"><a href="z_3d2460180cf335ae_standards_manager_py.html#t38"><data value='StandardsContext'>StandardsContext</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3d2460180cf335ae_standards_manager_py.html#t48">backend\core\standards\standards_manager.py</a></td>
                <td class="name left"><a href="z_3d2460180cf335ae_standards_manager_py.html#t48"><data value='StandardsManager'>StandardsManager</data></a></td>
                <td>149</td>
                <td>146</td>
                <td>0</td>
                <td class="right" data-ratio="3 149">2%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3d2460180cf335ae_standards_manager_py.html">backend\core\standards\standards_manager.py</a></td>
                <td class="name left"><a href="z_3d2460180cf335ae_standards_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>40</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="40 40">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e6723df25685444a___init___py.html">backend\core\standards\tr_50410\__init__.py</a></td>
                <td class="name left"><a href="z_e6723df25685444a___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e6723df25685444a_heat_loss_factors_py.html">backend\core\standards\tr_50410\heat_loss_factors.py</a></td>
                <td class="name left"><a href="z_e6723df25685444a_heat_loss_factors_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>8077</td>
                <td>3399</td>
                <td>0</td>
                <td class="right" data-ratio="4678 8077">58%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.0">coverage.py v7.8.0</a>,
            created at 2025-06-01 22:35 +0300
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>

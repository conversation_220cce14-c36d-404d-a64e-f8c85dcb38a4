# backend/api/v1/activity_log_routes.py
"""
Activity Log API Routes

This module provides REST API endpoints for activity log operations including:
- Activity log retrieval and filtering
- Audit trail management and reporting
- Security event monitoring and alerting
- User activity tracking and analysis
- Compliance reporting and audit analysis
"""

from datetime import datetime
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session

try:
    from backend.config.logging_config import get_logger
    from backend.core.database.session import get_db_session
    from backend.core.errors.exceptions import (
        BaseApplicationException,
        DatabaseError,
        InvalidInputError,
        NotFoundError,
    )
    from backend.core.schemas.activity_log_schemas import (
        ActivityLogCreateSchema,
        ActivityLogFilterSchema,
        ActivityLogPaginatedResponseSchema,
        ActivityLogReadSchema,
        ActivityLogSummarySchema,
        ActivityLogUpdateSchema,
        AuditReportRequestSchema,
        AuditReportResponseSchema,
        EventTypeEnum,
        EntityTypeEnum,
        EventCategoryEnum,
    )
    from backend.core.services.activity_log_service import ActivityLogService
except ImportError:
    from config.logging_config import get_logger
    from core.database.session import get_db_session
    from core.errors.exceptions import (
        BaseApplicationException,
        DatabaseError,
        InvalidInputError,
        NotFoundError,
    )
    from core.schemas.activity_log_schemas import (
        ActivityLogCreateSchema,
        ActivityLogFilterSchema,
        ActivityLogPaginatedResponseSchema,
        ActivityLogReadSchema,
        ActivityLogSummarySchema,
        ActivityLogUpdateSchema,
        AuditReportRequestSchema,
        AuditReportResponseSchema,
        EventTypeEnum,
        EntityTypeEnum,
        EventCategoryEnum,
    )
    from core.services.activity_log_service import ActivityLogService

# Initialize logger for this module
logger = get_logger(__name__)

# Create router
router = APIRouter(prefix="/activity-logs", tags=["activity-logs"])


def handle_activity_log_exceptions(e: Exception) -> HTTPException:
    """
    Handle activity log-specific exceptions and convert to HTTP responses.

    Args:
        e: Exception to handle

    Returns:
        HTTPException: Appropriate HTTP exception
    """
    if isinstance(e, NotFoundError):
        logger.warning(f"Not found error: {e.detail}")
        return HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=e.detail,
        )
    elif isinstance(e, InvalidInputError):
        logger.warning(f"Invalid input error: {e.detail}")
        return HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=e.detail,
        )
    elif isinstance(e, DatabaseError):
        logger.error(f"Database error: {e.detail}")
        return HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )
    elif isinstance(e, BaseApplicationException):
        logger.error(f"Application error: {e.detail}")
        return HTTPException(
            status_code=e.status_code,
            detail=e.detail,
        )
    else:
        logger.error(f"Unexpected error: {str(e)}", exc_info=True)
        return HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


# ============================================================================
# ACTIVITY LOG CRUD ENDPOINTS
# ============================================================================


@router.post(
    "",
    response_model=ActivityLogReadSchema,
    status_code=status.HTTP_201_CREATED,
    summary="Create activity log",
    description="Create a new activity log entry",
)
async def create_activity_log(
    log_data: ActivityLogCreateSchema,
    db: Session = Depends(get_db_session),
) -> ActivityLogReadSchema:
    """Create a new activity log entry."""
    logger.info(f"Creating activity log: {log_data.event_type}")

    try:
        activity_log_service = ActivityLogService(db)
        activity_log = activity_log_service.log_event(
            event_type=log_data.event_type,
            user_id=log_data.user_id,
            entity_type=log_data.entity_type,
            entity_id=log_data.entity_id,
            details=log_data.details,
        )

        logger.info(f"Created activity log {activity_log.id}: {log_data.event_type}")
        return activity_log

    except Exception as e:
        raise handle_activity_log_exceptions(e)


@router.get(
    "",
    response_model=ActivityLogPaginatedResponseSchema,
    summary="List activity logs",
    description="Retrieve a paginated list of activity logs with optional filtering",
)
async def list_activity_logs(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(
        10, ge=1, le=100, description="Maximum number of records to return"
    ),
    user_id: Optional[int] = Query(None, description="Filter by user ID"),
    event_type: Optional[EventTypeEnum] = Query(
        None, description="Filter by event type"
    ),
    entity_type: Optional[EntityTypeEnum] = Query(
        None, description="Filter by entity type"
    ),
    entity_id: Optional[int] = Query(None, description="Filter by entity ID"),
    start_date: Optional[datetime] = Query(
        None, description="Filter events after this date"
    ),
    end_date: Optional[datetime] = Query(
        None, description="Filter events before this date"
    ),
    search_details: Optional[str] = Query(None, description="Search in event details"),
    db: Session = Depends(get_db_session),
) -> ActivityLogPaginatedResponseSchema:
    """List activity logs with optional filtering."""
    logger.debug(f"Listing activity logs: skip={skip}, limit={limit}")

    try:
        activity_log_service = ActivityLogService(db)

        # Create filter if any filter parameters are provided
        filters = None
        if any(
            [
                user_id,
                event_type,
                entity_type,
                entity_id,
                start_date,
                end_date,
                search_details,
            ]
        ):
            filters = ActivityLogFilterSchema(
                user_id=user_id,
                event_type=event_type,
                entity_type=entity_type,
                entity_id=entity_id,
                start_date=start_date,
                end_date=end_date,
                event_category=None,  # Not exposed in this endpoint
                search_details=search_details,
            )

        activity_logs = activity_log_service.get_activity_logs(filters, skip, limit)
        return activity_logs

    except Exception as e:
        raise handle_activity_log_exceptions(e)


@router.put(
    "/{log_id}",
    response_model=ActivityLogReadSchema,
    summary="Update activity log",
    description="Update an existing activity log entry",
)
async def update_activity_log(
    log_id: int,
    log_data: ActivityLogUpdateSchema,
    db: Session = Depends(get_db_session),
) -> ActivityLogReadSchema:
    """Update an existing activity log entry."""
    logger.info(f"Updating activity log {log_id}")

    try:
        activity_log_service = ActivityLogService(db)
        activity_log = activity_log_service.update_activity_log(log_id, log_data)

        logger.info(f"Updated activity log {log_id}")
        return activity_log

    except Exception as e:
        raise handle_activity_log_exceptions(e)


# ============================================================================
# USER ACTIVITY ENDPOINTS
# ============================================================================


@router.get(
    "/users/{user_id}",
    response_model=List[ActivityLogSummarySchema],
    summary="Get user activity logs",
    description="Retrieve activity logs for a specific user",
)
async def get_user_activity_logs(
    user_id: int,
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(
        50, ge=1, le=100, description="Maximum number of records to return"
    ),
    db: Session = Depends(get_db_session),
) -> List[ActivityLogSummarySchema]:
    """Get activity logs for a specific user."""
    logger.debug(
        f"Retrieving activity logs for user {user_id}: skip={skip}, limit={limit}"
    )

    try:
        activity_log_service = ActivityLogService(db)
        activity_logs = activity_log_service.get_user_activity_logs(
            user_id, skip, limit
        )

        return activity_logs

    except Exception as e:
        raise handle_activity_log_exceptions(e)


@router.get(
    "/users/{user_id}/summary",
    response_model=dict,
    summary="Get user activity summary",
    description="Get activity summary for a specific user within a date range",
)
async def get_user_activity_summary(
    user_id: int,
    start_date: datetime = Query(..., description="Start date for summary"),
    end_date: datetime = Query(..., description="End date for summary"),
    db: Session = Depends(get_db_session),
) -> dict:
    """Get activity summary for a specific user."""
    logger.debug(
        f"Generating activity summary for user {user_id} from {start_date} to {end_date}"
    )

    try:
        activity_log_service = ActivityLogService(db)
        summary = activity_log_service.get_user_activity_summary(
            user_id, start_date, end_date
        )

        return summary

    except Exception as e:
        raise handle_activity_log_exceptions(e)


# ============================================================================
# ENTITY ACTIVITY ENDPOINTS
# ============================================================================


@router.get(
    "/entities/{entity_type}/{entity_id}",
    response_model=List[ActivityLogSummarySchema],
    summary="Get entity activity logs",
    description="Retrieve activity logs for a specific entity",
)
async def get_entity_activity_logs(
    entity_type: str,
    entity_id: int,
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(
        50, ge=1, le=100, description="Maximum number of records to return"
    ),
    db: Session = Depends(get_db_session),
) -> List[ActivityLogSummarySchema]:
    """Get activity logs for a specific entity."""
    logger.debug(
        f"Retrieving activity logs for entity {entity_type}:{entity_id}: skip={skip}, limit={limit}"
    )

    try:
        activity_log_service = ActivityLogService(db)
        activity_logs = activity_log_service.get_entity_activity_logs(
            entity_type, entity_id, skip, limit
        )

        return activity_logs

    except Exception as e:
        raise handle_activity_log_exceptions(e)


# ============================================================================
# SECURITY EVENT ENDPOINTS
# ============================================================================


@router.get(
    "/security-events",
    response_model=List[ActivityLogSummarySchema],
    summary="Get security events",
    description="Retrieve security-related events within a date range",
)
async def get_security_events(
    start_date: datetime = Query(..., description="Start date for filtering"),
    end_date: datetime = Query(..., description="End date for filtering"),
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(
        50, ge=1, le=100, description="Maximum number of records to return"
    ),
    db: Session = Depends(get_db_session),
) -> List[ActivityLogSummarySchema]:
    """Get security-related events within a date range."""
    logger.debug(
        f"Retrieving security events for date range {start_date} to {end_date}: skip={skip}, limit={limit}"
    )

    try:
        activity_log_service = ActivityLogService(db)
        security_events = activity_log_service.get_security_events(
            start_date, end_date, skip, limit
        )

        return security_events

    except Exception as e:
        raise handle_activity_log_exceptions(e)


@router.post(
    "/security-events",
    response_model=ActivityLogReadSchema,
    status_code=status.HTTP_201_CREATED,
    summary="Log security event",
    description="Log a security event with enhanced security context",
)
async def log_security_event(
    event_type: str = Query(..., description="Type of security event"),
    user_id: Optional[int] = Query(None, description="ID of the user involved"),
    severity: str = Query("MEDIUM", description="Security event severity"),
    threat_level: str = Query("LOW", description="Assessed threat level"),
    details: Optional[str] = Query(
        None, description="Additional security event details"
    ),
    db: Session = Depends(get_db_session),
) -> ActivityLogReadSchema:
    """Log a security event with enhanced security context."""
    logger.info(
        f"Logging security event: event_type={event_type}, user_id={user_id}, "
        f"severity={severity}, threat_level={threat_level}"
    )

    try:
        activity_log_service = ActivityLogService(db)

        # Parse details if provided
        details_dict = None
        if details:
            try:
                import json

                details_dict = json.loads(details)
            except json.JSONDecodeError:
                details_dict = {"raw_details": details}

        security_event = activity_log_service.log_security_event(
            event_type=event_type,
            user_id=user_id,
            severity=severity,
            threat_level=threat_level,
            details=details_dict,
        )

        logger.info(f"Security event logged successfully: {security_event.id}")
        return security_event

    except Exception as e:
        raise handle_activity_log_exceptions(e)


# ============================================================================
# AUDIT REPORTING ENDPOINTS
# ============================================================================


@router.post(
    "/reports",
    response_model=AuditReportResponseSchema,
    status_code=status.HTTP_201_CREATED,
    summary="Generate audit report",
    description="Generate an audit report based on the request criteria",
)
async def generate_audit_report(
    report_request: AuditReportRequestSchema,
    generated_by_user_id: Optional[int] = Query(
        None, description="ID of the user generating the report"
    ),
    db: Session = Depends(get_db_session),
) -> AuditReportResponseSchema:
    """Generate an audit report based on the request criteria."""
    logger.info(
        f"Generating audit report: type={report_request.report_type}, "
        f"date_range={report_request.start_date} to {report_request.end_date}"
    )

    try:
        activity_log_service = ActivityLogService(db)
        report = activity_log_service.generate_audit_report(
            report_request, generated_by_user_id
        )

        logger.info(
            f"Audit report generated successfully: {report.report_id}, "
            f"total_events={report.summary.total_events}"
        )
        return report

    except Exception as e:
        raise handle_activity_log_exceptions(e)


# ============================================================================
# UTILITY ENDPOINTS
# ============================================================================


@router.get(
    "/recent",
    response_model=List[ActivityLogSummarySchema],
    summary="Get recent activity",
    description="Get most recent activity logs",
)
async def get_recent_activity(
    limit: int = Query(
        50, ge=1, le=100, description="Maximum number of records to return"
    ),
    db: Session = Depends(get_db_session),
) -> List[ActivityLogSummarySchema]:
    """Get most recent activity logs."""
    logger.debug(f"Retrieving {limit} most recent activity logs")

    try:
        activity_log_service = ActivityLogService(db)
        recent_activity = activity_log_service.get_recent_activity(limit)

        return recent_activity

    except Exception as e:
        raise handle_activity_log_exceptions(e)


@router.get(
    "/search",
    response_model=List[ActivityLogSummarySchema],
    summary="Search activity logs",
    description="Search activity logs by details content",
)
async def search_activity_logs(
    search_term: str = Query(..., description="Search term to match against details"),
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(
        50, ge=1, le=100, description="Maximum number of records to return"
    ),
    db: Session = Depends(get_db_session),
) -> List[ActivityLogSummarySchema]:
    """Search activity logs by details content."""
    logger.debug(f"Searching activity logs with term: {search_term}")

    try:
        activity_log_service = ActivityLogService(db)
        activity_logs = activity_log_service.search_activity_logs(
            search_term, skip, limit
        )

        return activity_logs

    except Exception as e:
        raise handle_activity_log_exceptions(e)


@router.delete(
    "/cleanup",
    status_code=status.HTTP_200_OK,
    summary="Delete old activity logs",
    description="Delete activity logs older than the specified number of days",
)
async def delete_old_activity_logs(
    days_to_keep: int = Query(
        365, ge=1, description="Number of days to keep logs (default: 365)"
    ),
    db: Session = Depends(get_db_session),
) -> dict:
    """Delete activity logs older than the specified number of days."""
    logger.info(f"Deleting activity logs older than {days_to_keep} days")

    try:
        activity_log_service = ActivityLogService(db)
        deleted_count = activity_log_service.delete_old_activity_logs(days_to_keep)

        logger.info(f"Deleted {deleted_count} old activity logs")
        return {
            "message": f"Deleted {deleted_count} old activity logs",
            "deleted_count": deleted_count,
            "days_to_keep": days_to_keep,
        }

    except Exception as e:
        raise handle_activity_log_exceptions(e)


# ============================================================================
# INDIVIDUAL ACTIVITY LOG ENDPOINTS (Must be last due to path parameter)
# ============================================================================


@router.get(
    "/{log_id}",
    response_model=ActivityLogReadSchema,
    summary="Get activity log",
    description="Retrieve a specific activity log by ID",
)
async def get_activity_log(
    log_id: int,
    db: Session = Depends(get_db_session),
) -> ActivityLogReadSchema:
    """Get a specific activity log by ID."""
    logger.debug(f"Retrieving activity log {log_id}")

    try:
        activity_log_service = ActivityLogService(db)
        activity_log = activity_log_service.get_activity_log(log_id)

        return activity_log

    except Exception as e:
        raise handle_activity_log_exceptions(e)

# backend/tests/test_activity_log_service.py
"""
Tests for Activity Log Service.

This module tests the business logic for activity log operations including
event logging, audit trail management, security monitoring, and reporting.
"""

import json
import pytest
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

from backend.core.errors.exceptions import (
    BaseApplicationException,
    InvalidInputError,
    NotFoundError,
)
from backend.core.models.activity_log import ActivityLog
from backend.core.models.users import User
from backend.core.schemas.activity_log_schemas import (
    ActivityLogCreateSchema,
    ActivityLogFilterSchema,
    ActivityLogReadSchema,
    ActivityLogUpdateSchema,
    AuditReportRequestSchema,
    EventTypeEnum,
    EntityTypeEnum,
)
from backend.core.services.activity_log_service import ActivityLogService


class TestActivityLogService:
    """Test ActivityLogService functionality."""

    @pytest.fixture
    def mock_db_session(self):
        """Create a mock database session."""
        session = Mock()
        session.commit = Mock()
        session.rollback = Mock()
        session.refresh = Mock()
        return session

    @pytest.fixture
    def mock_activity_log_repo(self):
        """Create a mock activity log repository."""
        return Mock()

    @pytest.fixture
    def mock_user_repo(self):
        """Create a mock user repository."""
        return Mock()

    @pytest.fixture
    def activity_log_service(self, mock_db_session):
        """Create an ActivityLogService instance with mocked dependencies."""
        with (
            patch(
                "core.services.activity_log_service.ActivityLogRepository"
            ) as mock_repo_class,
            patch(
                "core.services.activity_log_service.UserRepository"
            ) as mock_user_repo_class,
        ):
            service = ActivityLogService(mock_db_session)
            service.activity_log_repo = Mock()
            service.user_repo = Mock()
            return service

    @pytest.fixture
    def sample_user(self):
        """Create a sample user for testing."""
        user = User()
        user.id = 1
        user.username = "testuser"
        user.email = "<EMAIL>"
        user.is_active = True
        user.created_at = datetime.now()
        user.updated_at = datetime.now()
        return user

    @pytest.fixture
    def sample_activity_log(self):
        """Create a sample activity log for testing."""
        return ActivityLog(
            id=1,
            timestamp=datetime.now(),
            user_id=1,
            event_type=EventTypeEnum.CREATE.value,
            entity_type=EntityTypeEnum.PROJECT.value,
            entity_id=123,
            details="Created new project",
        )

    def test_service_initialization(self, mock_db_session):
        """Test service initialization."""
        with (
            patch("core.services.activity_log_service.ActivityLogRepository"),
            patch("core.services.activity_log_service.UserRepository"),
        ):
            service = ActivityLogService(mock_db_session)
            assert service.db_session == mock_db_session

    def test_log_event_success(
        self, activity_log_service, sample_user, sample_activity_log
    ):
        """Test successful event logging."""
        # Mock user repository
        activity_log_service.user_repo.get_by_id.return_value = sample_user

        # Mock activity log repository
        activity_log_service.activity_log_repo.create.return_value = sample_activity_log

        # Test the method
        result = activity_log_service.log_event(
            event_type=EventTypeEnum.CREATE,
            user_id=1,
            entity_type=EntityTypeEnum.PROJECT,
            entity_id=123,
            details="Created new project",
        )

        # Verify results
        assert isinstance(result, ActivityLogReadSchema)
        assert result.event_type == EventTypeEnum.CREATE
        assert result.user_id == 1
        assert result.entity_type == EntityTypeEnum.PROJECT
        assert result.entity_id == 123

        # Verify repository calls
        activity_log_service.user_repo.get_by_id.assert_called_once_with(1)
        activity_log_service.activity_log_repo.create.assert_called_once()

    def test_log_event_system_event(self, activity_log_service):
        """Test logging system event without user."""
        # Create system event
        system_event = ActivityLog(
            id=2,
            timestamp=datetime.now(),
            user_id=None,
            event_type=EventTypeEnum.SYSTEM_START.value,
            entity_type=None,
            entity_id=None,
            details="System started",
        )

        # Mock activity log repository
        activity_log_service.activity_log_repo.create.return_value = system_event

        # Test the method
        result = activity_log_service.log_event(
            event_type=EventTypeEnum.SYSTEM_START,
            user_id=None,
            details="System started",
        )

        # Verify results
        assert isinstance(result, ActivityLogReadSchema)
        assert result.event_type == EventTypeEnum.SYSTEM_START
        assert result.user_id is None

        # Verify user repository was not called
        activity_log_service.user_repo.get_by_id.assert_not_called()

    def test_log_event_invalid_user(self, activity_log_service):
        """Test logging event with invalid user."""
        # Mock user repository to return None
        activity_log_service.user_repo.get_by_id.return_value = None

        # Test the method
        with pytest.raises(InvalidInputError) as exc_info:
            activity_log_service.log_event(
                event_type=EventTypeEnum.CREATE,
                user_id=999,
                entity_type=EntityTypeEnum.PROJECT,
                entity_id=123,
            )

        assert "User 999 not found or inactive" in str(exc_info.value)

    def test_log_user_action_success(
        self, activity_log_service, sample_user, sample_activity_log
    ):
        """Test successful user action logging."""
        # Mock dependencies
        activity_log_service.user_repo.get_by_id.return_value = sample_user
        activity_log_service.activity_log_repo.create.return_value = sample_activity_log

        # Test the method
        result = activity_log_service.log_user_action(
            user_id=1,
            action="CREATE",
            entity_type="Project",
            entity_id=123,
            details={"project_name": "Test Project"},
        )

        # Verify results
        assert isinstance(result, ActivityLogReadSchema)
        assert result.event_type == EventTypeEnum.CREATE

    def test_log_user_action_invalid_action(self, activity_log_service):
        """Test user action logging with invalid action."""
        with pytest.raises(InvalidInputError) as exc_info:
            activity_log_service.log_user_action(
                user_id=1, action="INVALID_ACTION", entity_type="Project", entity_id=123
            )

        assert "Invalid action type: INVALID_ACTION" in str(exc_info.value)

    def test_log_user_action_invalid_entity_type(self, activity_log_service):
        """Test user action logging with invalid entity type."""
        with pytest.raises(InvalidInputError) as exc_info:
            activity_log_service.log_user_action(
                user_id=1, action="CREATE", entity_type="InvalidEntity", entity_id=123
            )

        assert "Invalid entity type: InvalidEntity" in str(exc_info.value)

    def test_log_authentication_event_success(
        self, activity_log_service, sample_user, sample_activity_log
    ):
        """Test successful authentication event logging."""
        # Mock dependencies
        activity_log_service.user_repo.get_by_id.return_value = sample_user
        activity_log_service.activity_log_repo.create.return_value = sample_activity_log

        # Test successful login
        result = activity_log_service.log_authentication_event(
            user_id=1,
            event_type="LOGIN",
            success=True,
            details={"ip_address": "*************"},
        )

        # Verify results
        assert isinstance(result, ActivityLogReadSchema)

    def test_log_authentication_event_failed_login(self, activity_log_service):
        """Test failed login authentication event."""
        # Create failed login event
        failed_login_event = ActivityLog(
            id=3,
            timestamp=datetime.now(),
            user_id=None,
            event_type=EventTypeEnum.LOGIN_FAILED.value,
            entity_type=EntityTypeEnum.USER.value,
            entity_id=None,
            details='{"success": false, "ip_address": "*************"}',
        )

        # Mock activity log repository
        activity_log_service.activity_log_repo.create.return_value = failed_login_event

        # Test failed login
        result = activity_log_service.log_authentication_event(
            user_id=None,
            event_type="LOGIN",
            success=False,
            details={"ip_address": "*************"},
        )

        # Verify results
        assert isinstance(result, ActivityLogReadSchema)
        assert result.event_type == EventTypeEnum.LOGIN_FAILED

    def test_log_system_event_success(self, activity_log_service, sample_activity_log):
        """Test successful system event logging."""
        # Mock activity log repository
        activity_log_service.activity_log_repo.create.return_value = sample_activity_log

        # Test the method
        result = activity_log_service.log_system_event(
            event_type="SYSTEM_START",
            details={"version": "1.0.0", "environment": "production"},
        )

        # Verify results
        assert isinstance(result, ActivityLogReadSchema)

    def test_get_activity_log_success(self, activity_log_service, sample_activity_log):
        """Test successful activity log retrieval."""
        # Mock activity log repository
        activity_log_service.activity_log_repo.get_by_id.return_value = (
            sample_activity_log
        )

        # Test the method
        result = activity_log_service.get_activity_log(1)

        # Verify results
        assert isinstance(result, ActivityLogReadSchema)
        assert result.id == 1

    def test_get_activity_log_not_found(self, activity_log_service):
        """Test activity log retrieval when not found."""
        # Mock activity log repository to return None
        activity_log_service.activity_log_repo.get_by_id.return_value = None

        # Test the method
        with pytest.raises(NotFoundError) as exc_info:
            activity_log_service.get_activity_log(999)

        assert "Activity log 999 not found" in str(exc_info.value)

    def test_get_user_activity_logs_success(
        self, activity_log_service, sample_user, sample_activity_log
    ):
        """Test successful user activity logs retrieval."""
        # Mock dependencies
        activity_log_service.user_repo.get_by_id.return_value = sample_user
        activity_log_service.activity_log_repo.get_by_user_id.return_value = [
            sample_activity_log
        ]

        # Test the method
        result = activity_log_service.get_user_activity_logs(
            user_id=1, skip=0, limit=10
        )

        # Verify results
        assert len(result) == 1
        assert all(isinstance(log, type(result[0])) for log in result)

    def test_get_user_activity_logs_user_not_found(self, activity_log_service):
        """Test user activity logs retrieval when user not found."""
        # Mock user repository to return None
        activity_log_service.user_repo.get_by_id.return_value = None

        # Test the method
        with pytest.raises(NotFoundError) as exc_info:
            activity_log_service.get_user_activity_logs(user_id=999)

        assert "User 999 not found or inactive" in str(exc_info.value)

    def test_log_security_event_success(
        self, activity_log_service, sample_activity_log
    ):
        """Test successful security event logging."""
        # Mock activity log repository
        activity_log_service.activity_log_repo.create.return_value = sample_activity_log

        # Test the method
        result = activity_log_service.log_security_event(
            event_type="UNAUTHORIZED_ACCESS",
            user_id=1,
            severity="HIGH",
            threat_level="MEDIUM",
            details={"ip_address": "*************", "attempted_resource": "/admin"},
        )

        # Verify results
        assert isinstance(result, ActivityLogReadSchema)

    def test_log_security_event_invalid_severity(self, activity_log_service):
        """Test security event logging with invalid severity."""
        with pytest.raises(InvalidInputError) as exc_info:
            activity_log_service.log_security_event(
                event_type="UNAUTHORIZED_ACCESS", severity="INVALID_SEVERITY"
            )

        assert "Invalid severity: INVALID_SEVERITY" in str(exc_info.value)

    def test_log_security_event_invalid_threat_level(self, activity_log_service):
        """Test security event logging with invalid threat level."""
        with pytest.raises(InvalidInputError) as exc_info:
            activity_log_service.log_security_event(
                event_type="UNAUTHORIZED_ACCESS", threat_level="INVALID_LEVEL"
            )

        assert "Invalid threat level: INVALID_LEVEL" in str(exc_info.value)

    def test_update_activity_log_success(
        self, activity_log_service, sample_activity_log
    ):
        """Test successful activity log update."""
        # Mock dependencies
        activity_log_service.activity_log_repo.get_by_id.return_value = (
            sample_activity_log
        )
        activity_log_service.activity_log_repo.update.return_value = sample_activity_log

        # Test the method
        update_data = ActivityLogUpdateSchema(details="Updated details")
        result = activity_log_service.update_activity_log(1, update_data)

        # Verify results
        assert isinstance(result, ActivityLogReadSchema)
        activity_log_service.activity_log_repo.update.assert_called_once()

    def test_update_activity_log_not_found(self, activity_log_service):
        """Test activity log update when not found."""
        # Mock activity log repository to return None
        activity_log_service.activity_log_repo.get_by_id.return_value = None

        # Test the method
        update_data = ActivityLogUpdateSchema(details="Updated details")
        with pytest.raises(NotFoundError) as exc_info:
            activity_log_service.update_activity_log(999, update_data)

        assert "Activity log 999 not found" in str(exc_info.value)

    def test_delete_old_activity_logs_success(self, activity_log_service):
        """Test successful deletion of old activity logs."""
        # Mock activity log repository
        activity_log_service.activity_log_repo.delete_old_logs.return_value = 10

        # Test the method
        result = activity_log_service.delete_old_activity_logs(days_to_keep=365)

        # Verify results
        assert result == 10
        activity_log_service.activity_log_repo.delete_old_logs.assert_called_once()

    def test_delete_old_activity_logs_invalid_days(self, activity_log_service):
        """Test deletion of old activity logs with invalid days."""
        with pytest.raises(InvalidInputError) as exc_info:
            activity_log_service.delete_old_activity_logs(days_to_keep=0)

        assert "days_to_keep must be at least 1" in str(exc_info.value)

    def test_generate_audit_report_success(
        self, activity_log_service, sample_activity_log
    ):
        """Test successful audit report generation."""
        # Mock activity log repository
        activity_log_service.activity_log_repo.filter_activity_logs.return_value = [
            sample_activity_log
        ]

        # Create report request
        report_request = AuditReportRequestSchema(
            report_type="user_activity",
            start_date=datetime.now() - timedelta(days=30),
            end_date=datetime.now(),
            user_ids=[1],
            entity_types=None,
            event_types=None,
            event_categories=None,
            include_details=True,
            group_by=None,
        )

        # Test the method
        result = activity_log_service.generate_audit_report(
            report_request, generated_by_user_id=1
        )

        # Verify results
        assert result.report_type == "user_activity"
        assert result.generated_by_user_id == 1
        assert len(result.events) == 1
        assert result.summary.total_events == 1

    def test_get_user_activity_summary_success(self, activity_log_service, sample_user):
        """Test successful user activity summary generation."""
        # Mock dependencies
        activity_log_service.user_repo.get_by_id.return_value = sample_user
        activity_log_service.activity_log_repo.get_user_activity_summary.return_value = {
            "user_id": 1,
            "total_events": 10,
            "event_type_counts": {"CREATE": 5, "UPDATE": 3, "DELETE": 2},
            "entity_type_counts": {"Project": 7, "Component": 3},
        }

        # Test the method
        start_date = datetime.now() - timedelta(days=30)
        end_date = datetime.now()
        result = activity_log_service.get_user_activity_summary(1, start_date, end_date)

        # Verify results
        assert result["user_id"] == 1
        assert result["total_events"] == 10
        assert "event_type_counts" in result
        assert "entity_type_counts" in result

    def test_get_user_activity_summary_invalid_date_range(self, activity_log_service):
        """Test user activity summary with invalid date range."""
        start_date = datetime.now()
        end_date = datetime.now() - timedelta(days=1)  # End before start

        with pytest.raises(InvalidInputError) as exc_info:
            activity_log_service.get_user_activity_summary(1, start_date, end_date)

        assert "start_date must be before end_date" in str(exc_info.value)

    def test_database_error_handling(self, activity_log_service):
        """Test database error handling in service methods."""
        from sqlalchemy.exc import SQLAlchemyError

        # Mock database error
        activity_log_service.activity_log_repo.create.side_effect = SQLAlchemyError(
            "Database error"
        )

        # Test that error is handled and converted to application exception
        with pytest.raises(BaseApplicationException) as exc_info:
            activity_log_service.log_event(
                event_type=EventTypeEnum.CREATE, user_id=None, details="Test event"
            )

        assert "Failed to log event" in str(exc_info.value)

    def test_transaction_rollback_on_error(self, activity_log_service, mock_db_session):
        """Test that database transactions are rolled back on errors."""
        # Mock repository error
        activity_log_service.activity_log_repo.create.side_effect = Exception(
            "Repository error"
        )

        # Test that rollback is called on error
        with pytest.raises(BaseApplicationException):
            activity_log_service.log_event(
                event_type=EventTypeEnum.CREATE, user_id=None, details="Test event"
            )

        # Verify rollback was called
        mock_db_session.rollback.assert_called()

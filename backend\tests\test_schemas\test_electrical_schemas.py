# backend/tests/test_schemas/test_electrical_schemas.py
"""
Tests for Electrical schemas validation and serialization.

This module tests the Pydantic schemas for Electrical entities including:
- ElectricalNode schemas
- CableRoute schemas
- CableSegment schemas
- LoadCalculation schemas
- VoltageDropCalculation schemas
- Calculation integration schemas
- Design workflow schemas
"""

import pytest
from pydantic import ValidationError

# Mark all tests in this file
pytestmark = [pytest.mark.unit, pytest.mark.schema, pytest.mark.electrical]

from core.schemas.electrical_schemas import (
    CableSizingCalculationInputSchema,
    CableSizingCalculationResultSchema,
    VoltageDropCalculationInputSchema,
    VoltageDropCalculationResultSchema,
    ElectricalNodeCreateSchema,
    ElectricalNodeUpdateSchema,
    ElectricalNodeReadSchema,
    CableRouteCreateSchema,
    CableRouteUpdateSchema,
    CableRouteReadSchema,
    CableSegmentCreateSchema,
    CableSegmentUpdateSchema,
    CableSegmentReadSchema,
    LoadCalculationCreateSchema,
    LoadCalculationUpdateSchema,
    LoadCalculationReadSchema,
    VoltageDropCalculationCreateSchema,
    VoltageDropCalculationUpdateSchema,
    VoltageDropCalculationReadSchema,
    ElectricalDesignInputSchema,
    ElectricalDesignResultSchema,
)
from core.models.enums import ElectricalNodeType, CableInstallationMethod


class TestCalculationIntegrationSchemas:
    """Test calculation integration schemas."""

    def test_cable_sizing_calculation_input_valid(self):
        """Test valid cable sizing calculation input."""
        data = {
            "required_power_kw": 2.5,
            "cable_length_m": 100.0,
            "supply_voltage_v": 240.0,
            "ambient_temperature_c": 25.0,
            "installation_method": CableInstallationMethod.CABLE_TRAY,
            "max_voltage_drop_percent": 5.0,
            "power_factor": 1.0,
            "safety_factor": 1.2,
        }
        schema = CableSizingCalculationInputSchema(**data)
        assert schema.required_power_kw == 2.5
        assert schema.cable_length_m == 100.0
        assert schema.supply_voltage_v == 240.0
        assert schema.installation_method == CableInstallationMethod.CABLE_TRAY

    def test_cable_sizing_calculation_input_invalid_power(self):
        """Test cable sizing calculation input with invalid power."""
        data = {
            "required_power_kw": -1.0,  # Invalid: negative power
            "cable_length_m": 100.0,
            "supply_voltage_v": 240.0,
            "installation_method": CableInstallationMethod.CABLE_TRAY,
        }
        with pytest.raises(ValidationError) as exc_info:
            CableSizingCalculationInputSchema(**data)
        assert "greater than 0" in str(exc_info.value)

    def test_voltage_drop_calculation_input_valid(self):
        """Test valid voltage drop calculation input."""
        data = {
            "supply_voltage_v": 240.0,
            "load_current_a": 10.4,
            "cable_length_m": 100.0,
            "cable_resistance_ohm_per_m": 0.0184,
            "power_factor": 1.0,
            "ambient_temperature_c": 25.0,
            "max_voltage_drop_percent": 5.0,
        }
        schema = VoltageDropCalculationInputSchema(**data)
        assert schema.supply_voltage_v == 240.0
        assert schema.load_current_a == 10.4
        assert schema.cable_resistance_ohm_per_m == 0.0184

    def test_cable_sizing_calculation_result_valid(self):
        """Test valid cable sizing calculation result."""
        data = {
            "recommended_cable_type": "self-regulating",
            "cable_power_per_meter": 25.0,
            "total_cable_length": 100.0,
            "current_draw": 10.4,
            "voltage_drop": 8.5,
            "voltage_drop_percent": 3.5,
            "power_density": 250.0,
            "is_compliant": True,
            "safety_margin_percent": 30.0,
        }
        schema = CableSizingCalculationResultSchema(**data)
        assert schema.recommended_cable_type == "self-regulating"
        assert schema.is_compliant is True
        assert schema.voltage_drop_percent == 3.5


class TestElectricalNodeSchemas:
    """Test electrical node schemas."""

    def test_electrical_node_create_schema_valid(self):
        """Test valid electrical node creation."""
        data = {
            "name": "Main Switchboard Incoming",
            "project_id": 1,
            "node_type": ElectricalNodeType.SWITCHBOARD_INCOMING,
            "location_description": "Main electrical room",
            "voltage_v": 415.0,
            "power_capacity_kva": 100.0,
        }
        schema = ElectricalNodeCreateSchema(**data)
        assert schema.name == "Main Switchboard Incoming"
        assert schema.node_type == ElectricalNodeType.SWITCHBOARD_INCOMING
        assert schema.voltage_v == 415.0

    def test_electrical_node_create_schema_name_validation(self):
        """Test electrical node name validation."""
        data = {
            "name": "  ",  # Invalid: empty name
            "project_id": 1,
            "node_type": ElectricalNodeType.SWITCHBOARD_INCOMING,
        }
        with pytest.raises(ValidationError) as exc_info:
            ElectricalNodeCreateSchema(**data)
        assert "at least 3 characters" in str(exc_info.value)

    def test_electrical_node_update_schema_partial(self):
        """Test electrical node partial update."""
        data = {
            "name": "Updated Node Name",
            "voltage_v": 240.0,
        }
        schema = ElectricalNodeUpdateSchema(**data)
        assert schema.name == "Updated Node Name"
        assert schema.voltage_v == 240.0
        assert schema.node_type is None  # Not provided

    def test_electrical_node_read_schema_from_dict(self):
        """Test electrical node read schema from dictionary."""
        data = {
            "id": 1,
            "name": "Main Switchboard Incoming",
            "project_id": 1,
            "node_type": ElectricalNodeType.SWITCHBOARD_INCOMING,
            "voltage_v": 415.0,
            "power_capacity_kva": 100.0,
            "created_at": "2024-01-15T10:30:00Z",
            "updated_at": "2024-01-15T10:30:00Z",
            "is_deleted": False,
            "deleted_at": None,
            "deleted_by_user_id": None,
        }
        schema = ElectricalNodeReadSchema(**data)
        assert schema.id == 1
        assert schema.name == "Main Switchboard Incoming"
        assert schema.is_deleted is False


class TestCableRouteSchemas:
    """Test cable route schemas."""

    def test_cable_route_create_schema_valid(self):
        """Test valid cable route creation."""
        data = {
            "name": "Main Feed to HT Panel",
            "project_id": 1,
            "from_node_id": 1,
            "to_node_id": 2,
            "cable_component_id": 25,
            "length_m": 150.0,
            "number_of_runs": 1,
            "installation_method": CableInstallationMethod.CABLE_TRAY,
            "max_ambient_temp_c": 40.0,
            "min_ambient_temp_c": -10.0,
        }
        schema = CableRouteCreateSchema(**data)
        assert schema.name == "Main Feed to HT Panel"
        assert schema.from_node_id == 1
        assert schema.to_node_id == 2
        assert schema.length_m == 150.0

    def test_cable_route_create_schema_same_nodes_validation(self):
        """Test cable route validation for same from/to nodes."""
        data = {
            "name": "Invalid Route",
            "project_id": 1,
            "from_node_id": 1,
            "to_node_id": 1,  # Invalid: same as from_node_id
            "cable_component_id": 25,
            "length_m": 150.0,
            "installation_method": CableInstallationMethod.CABLE_TRAY,
        }
        with pytest.raises(ValidationError) as exc_info:
            CableRouteCreateSchema(**data)
        assert "must be different" in str(exc_info.value)

    def test_cable_route_temperature_range_validation(self):
        """Test cable route temperature range validation."""
        data = {
            "name": "Test Route",
            "project_id": 1,
            "from_node_id": 1,
            "to_node_id": 2,
            "cable_component_id": 25,
            "length_m": 150.0,
            "installation_method": CableInstallationMethod.CABLE_TRAY,
            "max_ambient_temp_c": -20.0,  # Invalid: lower than min
            "min_ambient_temp_c": -10.0,
        }
        with pytest.raises(ValidationError) as exc_info:
            CableRouteCreateSchema(**data)
        assert "higher than minimum" in str(exc_info.value)


class TestLoadCalculationSchemas:
    """Test load calculation schemas."""

    def test_load_calculation_create_schema_valid(self):
        """Test valid load calculation creation."""
        data = {
            "name": "Heat Tracing Load HTC-001",
            "project_id": 1,
            "electrical_node_id": 2,
            "load_type": "heat_tracing",
            "load_description": "Heat tracing for main process line",
            "rated_power_kw": 2.5,
            "rated_voltage_v": 240.0,
            "rated_current_a": 10.4,
            "power_factor": 1.0,
            "efficiency_percent": 95.0,
            "safety_factor": 1.2,
        }
        schema = LoadCalculationCreateSchema(**data)
        assert schema.name == "Heat Tracing Load HTC-001"
        assert schema.load_type == "heat_tracing"
        assert schema.rated_power_kw == 2.5

    def test_load_calculation_electrical_parameters_validation(self):
        """Test load calculation electrical parameters consistency validation."""
        data = {
            "name": "Test Load",
            "project_id": 1,
            "electrical_node_id": 2,
            "load_type": "test",
            "rated_power_kw": 5.0,  # Inconsistent with voltage and current
            "rated_voltage_v": 240.0,
            "rated_current_a": 10.4,  # Should give ~2.5kW, not 5.0kW
            "power_factor": 1.0,
        }
        with pytest.raises(ValidationError) as exc_info:
            LoadCalculationCreateSchema(**data)
        assert "not consistent" in str(exc_info.value)


class TestDesignWorkflowSchemas:
    """Test design workflow schemas."""

    def test_electrical_design_input_schema_valid(self):
        """Test valid electrical design input."""
        data = {
            "project_id": 1,
            "electrical_node_ids": [1, 2],
            "cable_route_ids": [1],
            "auto_cable_sizing": True,
            "auto_voltage_drop_calc": True,
            "max_voltage_drop_percent": 5.0,
            "standards_context": {
                "cable_sizing_result": {
                    "recommended_cable_type": "test",
                    "cable_power_per_meter": 25.0,
                    "total_cable_length": 100.0,
                    "current_draw": 10.0,
                    "voltage_drop": 8.0,
                    "voltage_drop_percent": 3.3,
                    "power_density": 250.0,
                    "is_compliant": True,
                    "safety_margin_percent": 30.0,
                },
                "voltage_drop_result": {
                    "calculated_voltage_drop_v": 8.0,
                    "calculated_voltage_drop_percent": 3.3,
                    "calculated_power_loss_w": 80.0,
                    "calculated_efficiency_percent": 96.7,
                    "is_compliant": True,
                    "compliance_margin_percent": 1.7,
                    "calculation_method": "IEC",
                },
            },
        }
        schema = ElectricalDesignInputSchema(**data)
        assert schema.project_id == 1
        assert schema.auto_cable_sizing is True
        assert len(schema.electrical_node_ids) == 2

    def test_electrical_design_input_schema_node_or_route_validation(self):
        """Test electrical design input validation for node or route IDs."""
        data = {
            "project_id": 1,
            "electrical_node_ids": [],  # Empty list
            "cable_route_ids": [],  # Empty list
            "standards_context": {
                "cable_sizing_result": {
                    "recommended_cable_type": "test",
                    "cable_power_per_meter": 25.0,
                    "total_cable_length": 100.0,
                    "current_draw": 10.0,
                    "voltage_drop": 8.0,
                    "voltage_drop_percent": 3.3,
                    "power_density": 250.0,
                    "is_compliant": True,
                    "safety_margin_percent": 30.0,
                },
                "voltage_drop_result": {
                    "calculated_voltage_drop_v": 8.0,
                    "calculated_voltage_drop_percent": 3.3,
                    "calculated_power_loss_w": 80.0,
                    "calculated_efficiency_percent": 96.7,
                    "is_compliant": True,
                    "compliance_margin_percent": 1.7,
                    "calculation_method": "IEC",
                },
            },
        }
        with pytest.raises(ValidationError) as exc_info:
            ElectricalDesignInputSchema(**data)
        assert "must be provided" in str(exc_info.value)

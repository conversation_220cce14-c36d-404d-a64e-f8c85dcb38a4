# backend/core/repositories/user_repository.py
"""
User Repository

This module provides data access layer for User entities, extending the base
repository with user-specific query methods and operations.
"""

from typing import Any, Dict, List, Optional

from sqlalchemy import and_, func, select, update
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session

try:
    from config.logging_config import get_logger
    from core.models.users import User, UserPreference
    from core.repositories.base_repository import BaseRepository
except ImportError:
    # For testing and relative imports
    import sys
    import os

    sys.path.insert(
        0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    )
    from config.logging_config import get_logger
    from core.models.users import User, UserPreference
    from core.repositories.base_repository import BaseRepository

# Initialize logger for this module
logger = get_logger(__name__)


class UserRepository(BaseRepository[User]):
    """
    Repository for User entity data access operations.

    Extends BaseRepository with user-specific query methods and
    enhanced error handling for user operations.
    """

    def __init__(self, db_session: Session):
        """
        Initialize the User repository.

        Args:
            db_session: SQLAlchemy database session
        """
        super().__init__(db_session, User)
        logger.debug("UserRepository initialized")

    def get_by_email(self, email: str) -> Optional[User]:
        """
        Get user by email address.

        Args:
            email: User email address

        Returns:
            Optional[User]: User with the specified email or None if not found

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Retrieving user by email: {email}")

        try:
            stmt = select(self.model).where(
                and_(
                    self.model.email == email,
                    self.model.is_active == True,
                )
            )
            result = self.db_session.scalar(stmt)

            if result:
                logger.debug(f"User found for email: {email}")
            else:
                logger.debug(f"No user found for email: {email}")

            return result

        except SQLAlchemyError as e:
            logger.error(f"Database error retrieving user by email {email}: {e}")
            self._handle_db_exception(e, "user")
            raise

    def get_by_name(self, name: str) -> Optional[User]:
        """
        Get user by name.

        Args:
            name: User name

        Returns:
            Optional[User]: User with the specified name or None if not found

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Retrieving user by name: {name}")

        try:
            stmt = select(self.model).where(
                and_(
                    self.model.name == name,
                    self.model.is_active == True,
                )
            )
            result = self.db_session.scalar(stmt)

            if result:
                logger.debug(f"User found for name: {name}")
            else:
                logger.debug(f"No user found for name: {name}")

            return result

        except SQLAlchemyError as e:
            logger.error(f"Database error retrieving user by name {name}: {e}")
            self._handle_db_exception(e, "user")
            raise

    def get_active_users(self, skip: int = 0, limit: int = 100) -> List[User]:
        """
        Get all active users.

        Args:
            skip: Number of records to skip (for pagination)
            limit: Maximum number of records to return

        Returns:
            List[User]: List of active users

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Retrieving active users: skip={skip}, limit={limit}")

        try:
            stmt = (
                select(self.model)
                .where(self.model.is_active == True)
                .offset(skip)
                .limit(limit)
                .order_by(self.model.name)
            )
            results = list(self.db_session.scalars(stmt).all())

            logger.debug(f"Retrieved {len(results)} active users")
            return results

        except SQLAlchemyError as e:
            logger.error(f"Database error retrieving active users: {e}")
            self._handle_db_exception(e, "user")
            raise

    def search_users(
        self, search_term: str, skip: int = 0, limit: int = 100
    ) -> List[User]:
        """
        Search users by name or email.

        Args:
            search_term: Search term to match against name or email
            skip: Number of records to skip (for pagination)
            limit: Maximum number of records to return

        Returns:
            List[User]: List of users matching the search term

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Searching users with term: {search_term}")

        try:
            search_pattern = f"%{search_term}%"
            stmt = (
                select(self.model)
                .where(
                    and_(
                        self.model.is_active == True,
                        (
                            self.model.name.ilike(search_pattern)
                            | self.model.email.ilike(search_pattern)
                        ),
                    )
                )
                .offset(skip)
                .limit(limit)
                .order_by(self.model.name)
            )
            results = list(self.db_session.scalars(stmt).all())

            logger.debug(
                f"Found {len(results)} users matching search term: {search_term}"
            )
            return results

        except SQLAlchemyError as e:
            logger.error(f"Database error searching users with term {search_term}: {e}")
            self._handle_db_exception(e, "user")
            raise

    def count_active_users(self) -> int:
        """
        Count total number of active users.

        Returns:
            int: Number of active users

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug("Counting active users")

        try:
            stmt = select(func.count(self.model.id)).where(self.model.is_active == True)
            result = self.db_session.scalar(stmt)

            logger.debug(f"Total active users: {result}")
            return result or 0

        except SQLAlchemyError as e:
            logger.error(f"Database error counting active users: {e}")
            self._handle_db_exception(e, "user")
            raise

    def update_password(self, user_id: int, password_hash: str) -> bool:
        """
        Update user password hash.

        Args:
            user_id: User ID
            password_hash: New password hash

        Returns:
            bool: True if password was updated, False if user not found

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Updating password for user {user_id}")

        try:
            stmt = (
                update(self.model)
                .where(self.model.id == user_id)
                .values(password_hash=password_hash)
            )

            result = self.db_session.execute(stmt)

            if result.rowcount > 0:
                logger.debug(f"Password updated for user {user_id}")
                return True
            else:
                logger.debug(f"User {user_id} not found for password update")
                return False

        except SQLAlchemyError as e:
            logger.error(f"Database error updating password for user {user_id}: {e}")
            self._handle_db_exception(e, "user")
            raise

    def deactivate_user(self, user_id: int) -> bool:
        """
        Deactivate a user account.

        Args:
            user_id: User ID

        Returns:
            bool: True if user was deactivated, False if user not found

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Deactivating user {user_id}")

        try:
            stmt = (
                update(self.model)
                .where(self.model.id == user_id)
                .values(is_active=False)
            )

            result = self.db_session.execute(stmt)

            if result.rowcount > 0:
                logger.debug(f"User {user_id} deactivated successfully")
                return True
            else:
                logger.debug(f"User {user_id} not found for deactivation")
                return False

        except SQLAlchemyError as e:
            logger.error(f"Database error deactivating user {user_id}: {e}")
            self._handle_db_exception(e, "user")
            raise


class UserPreferenceRepository(BaseRepository[UserPreference]):
    """
    Repository for UserPreference entity data access operations.

    Extends BaseRepository with user preference-specific query methods and
    enhanced error handling for user preference operations.
    """

    def __init__(self, db_session: Session):
        """
        Initialize the UserPreference repository.

        Args:
            db_session: SQLAlchemy database session
        """
        super().__init__(db_session, UserPreference)
        logger.debug("UserPreferenceRepository initialized")

    def get_by_user_id(self, user_id: int) -> Optional[UserPreference]:
        """
        Get user preferences by user ID.

        Args:
            user_id: User ID

        Returns:
            Optional[UserPreference]: User preferences or None if not found

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Retrieving preferences for user {user_id}")

        try:
            stmt = select(self.model).where(
                and_(
                    self.model.user_id == user_id,
                    self.model.is_deleted == False,
                )
            )
            result = self.db_session.scalar(stmt)

            if result:
                logger.debug(f"Preferences found for user {user_id}")
            else:
                logger.debug(f"No preferences found for user {user_id}")

            return result

        except SQLAlchemyError as e:
            logger.error(
                f"Database error retrieving preferences for user {user_id}: {e}"
            )
            self._handle_db_exception(e, "user_preference")
            raise

    def create_or_update_preferences(
        self, user_id: int, preferences_data: Dict[str, Any]
    ) -> UserPreference:
        """
        Create or update user preferences.

        Args:
            user_id: User ID
            preferences_data: Preferences data to create or update

        Returns:
            UserPreference: Created or updated user preferences

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Creating or updating preferences for user {user_id}")

        try:
            # Check if preferences already exist
            existing_preferences = self.get_by_user_id(user_id)

            if existing_preferences:
                # Update existing preferences
                stmt = (
                    update(self.model)
                    .where(self.model.user_id == user_id)
                    .values(**preferences_data)
                )
                self.db_session.execute(stmt)

                # Return updated preferences
                updated_preferences = self.get_by_user_id(user_id)
                logger.debug(f"Preferences updated for user {user_id}")
                return updated_preferences
            else:
                # Create new preferences
                preferences_data["user_id"] = user_id
                new_preferences = self.model(**preferences_data)
                self.db_session.add(new_preferences)

                logger.debug(f"Preferences created for user {user_id}")
                return new_preferences

        except SQLAlchemyError as e:
            logger.error(
                f"Database error creating/updating preferences for user {user_id}: {e}"
            )
            self._handle_db_exception(e, "user_preference")
            raise

    def soft_delete_preferences(
        self, user_id: int, deleted_by_user_id: Optional[int] = None
    ) -> bool:
        """
        Soft delete user preferences.

        Args:
            user_id: User ID
            deleted_by_user_id: ID of user performing the deletion

        Returns:
            bool: True if deletion was successful, False if preferences not found

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Soft deleting preferences for user {user_id}")

        try:
            from datetime import datetime, timezone

            update_data = {
                "is_deleted": True,
                "deleted_at": datetime.now(timezone.utc),
                "deleted_by_user_id": deleted_by_user_id,
            }

            stmt = (
                update(self.model)
                .where(
                    and_(self.model.user_id == user_id, self.model.is_deleted == False)
                )
                .values(**update_data)
            )

            result = self.db_session.execute(stmt)

            if result.rowcount > 0:
                logger.debug(f"Preferences soft deleted for user {user_id}")
                return True
            else:
                logger.debug(
                    f"No preferences found for user {user_id} or already deleted"
                )
                return False

        except SQLAlchemyError as e:
            logger.error(
                f"Database error soft deleting preferences for user {user_id}: {e}"
            )
            self._handle_db_exception(e, "user_preference")
            raise

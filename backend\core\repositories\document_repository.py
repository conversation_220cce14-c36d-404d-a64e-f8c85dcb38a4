# backend/core/repositories/document_repository.py
"""
Document repository for data access operations.

This module provides repository classes for document management including
imported data revisions, exported documents, and calculation standards.
"""

import logging
from typing import List, Optional
from sqlalchemy import and_, desc, select
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session, selectinload

from core.models.documents import (
    ImportedDataRevision,
    ExportedDocument,
    CalculationStandard,
)
from core.repositories.base_repository import BaseRepository
from core.errors.exceptions import (
    NotFoundError,
    DatabaseError,
    DuplicateEntryError,
)

logger = logging.getLogger(__name__)


class ImportedDataRevisionRepository(BaseRepository[ImportedDataRevision]):
    """
    Repository for ImportedDataRevision entity data access operations.

    Extends BaseRepository with imported data revision-specific query methods
    and enhanced error handling for import operations.
    """

    def __init__(self, db_session: Session):
        """
        Initialize the ImportedDataRevision repository.

        Args:
            db_session: SQLAlchemy database session
        """
        super().__init__(db_session, ImportedDataRevision)
        logger.debug("ImportedDataRevisionRepository initialized")

    def get_by_project_id(
        self, project_id: int, skip: int = 0, limit: int = 100
    ) -> List[ImportedDataRevision]:
        """
        Get imported data revisions by project ID.

        Args:
            project_id: Project ID to filter by
            skip: Number of records to skip
            limit: Maximum number of records to return

        Returns:
            List[ImportedDataRevision]: List of imported data revisions

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Retrieving imported data revisions for project {project_id}")

        try:
            stmt = (
                select(self.model)
                .where(
                    and_(
                        self.model.project_id == project_id,
                        self.model.is_deleted == False,
                    )
                )
                .order_by(desc(self.model.created_at))
                .offset(skip)
                .limit(limit)
            )
            result = list(self.db_session.scalars(stmt).all())

            logger.debug(
                f"Retrieved {len(result)} imported data revisions for project {project_id}"
            )
            return result

        except SQLAlchemyError as e:
            logger.error(
                f"Database error retrieving imported data revisions for project {project_id}: {e}"
            )
            self._handle_db_exception(e, "imported data revision")
            raise

    def get_active_revision_by_filename(
        self, project_id: int, filename: str
    ) -> Optional[ImportedDataRevision]:
        """
        Get active revision by project ID and filename.

        Args:
            project_id: Project ID
            filename: Source filename

        Returns:
            Optional[ImportedDataRevision]: Active revision or None if not found

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(
            f"Retrieving active revision for project {project_id}, filename: {filename}"
        )

        try:
            stmt = select(self.model).where(
                and_(
                    self.model.project_id == project_id,
                    self.model.source_filename == filename,
                    self.model.is_active_revision == True,
                    self.model.is_deleted == False,
                )
            )
            result = self.db_session.scalar(stmt)

            if result:
                logger.debug(
                    f"Found active revision: {result.revision_identifier} for {filename}"
                )
            else:
                logger.debug(f"No active revision found for {filename}")

            return result

        except SQLAlchemyError as e:
            logger.error(
                f"Database error retrieving active revision for {filename}: {e}"
            )
            self._handle_db_exception(e, "imported data revision")
            raise

    def get_revisions_by_filename(
        self, project_id: int, filename: str
    ) -> List[ImportedDataRevision]:
        """
        Get all revisions for a specific filename in a project.

        Args:
            project_id: Project ID
            filename: Source filename

        Returns:
            List[ImportedDataRevision]: List of revisions ordered by creation date

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(
            f"Retrieving all revisions for project {project_id}, filename: {filename}"
        )

        try:
            stmt = (
                select(self.model)
                .where(
                    and_(
                        self.model.project_id == project_id,
                        self.model.source_filename == filename,
                        self.model.is_deleted == False,
                    )
                )
                .order_by(desc(self.model.created_at))
            )
            result = list(self.db_session.scalars(stmt).all())

            logger.debug(f"Retrieved {len(result)} revisions for {filename}")
            return result

        except SQLAlchemyError as e:
            logger.error(f"Database error retrieving revisions for {filename}: {e}")
            self._handle_db_exception(e, "imported data revision")
            raise

    def deactivate_other_revisions(
        self, project_id: int, filename: str, exclude_id: Optional[int] = None
    ) -> int:
        """
        Deactivate all other revisions for a filename except the specified one.

        Args:
            project_id: Project ID
            filename: Source filename
            exclude_id: ID of revision to exclude from deactivation

        Returns:
            int: Number of revisions deactivated

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(
            f"Deactivating other revisions for project {project_id}, filename: {filename}"
        )

        try:
            # Build the where clause
            where_conditions = [
                self.model.project_id == project_id,
                self.model.source_filename == filename,
                self.model.is_active_revision == True,
                self.model.is_deleted == False,
            ]

            if exclude_id is not None:
                where_conditions.append(self.model.id != exclude_id)

            # Get revisions to deactivate (use no_autoflush to prevent constraint violations)
            with self.db_session.no_autoflush:
                stmt = select(self.model).where(and_(*where_conditions))
                revisions_to_deactivate = list(self.db_session.scalars(stmt).all())

            # Deactivate them
            count = 0
            for revision in revisions_to_deactivate:
                revision.is_active_revision = False
                count += 1

            logger.debug(f"Deactivated {count} revisions for {filename}")
            return count

        except SQLAlchemyError as e:
            logger.error(f"Database error deactivating revisions for {filename}: {e}")
            self._handle_db_exception(e, "imported data revision")
            raise

    def get_with_related_data(self, revision_id: int) -> Optional[ImportedDataRevision]:
        """
        Get imported data revision with related project and user data.

        Args:
            revision_id: Revision ID

        Returns:
            Optional[ImportedDataRevision]: Revision with related data or None

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Retrieving revision {revision_id} with related data")

        try:
            stmt = (
                select(self.model)
                .where(self.model.id == revision_id)
                .options(
                    selectinload(self.model.project),
                    selectinload(self.model.imported_by_user),
                    selectinload(self.model.pipes),
                    selectinload(self.model.vessels),
                )
            )
            result = self.db_session.scalar(stmt)

            if result:
                logger.debug(
                    f"Retrieved revision with related data: {result.source_filename}"
                )
            else:
                logger.debug(f"Revision {revision_id} not found")

            return result

        except SQLAlchemyError as e:
            logger.error(
                f"Database error retrieving revision {revision_id} with related data: {e}"
            )
            self._handle_db_exception(e, "imported data revision")
            raise

    def count_by_project_id(self, project_id: int) -> int:
        """
        Count imported data revisions for a project.

        Args:
            project_id: Project ID

        Returns:
            int: Number of revisions

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Counting imported data revisions for project {project_id}")

        try:
            stmt = select(self.model).where(
                and_(
                    self.model.project_id == project_id,
                    self.model.is_deleted == False,
                )
            )
            result = len(list(self.db_session.scalars(stmt).all()))

            logger.debug(f"Found {result} revisions for project {project_id}")
            return result

        except SQLAlchemyError as e:
            logger.error(
                f"Database error counting revisions for project {project_id}: {e}"
            )
            self._handle_db_exception(e, "imported data revision")
            raise


class ExportedDocumentRepository(BaseRepository[ExportedDocument]):
    """
    Repository for ExportedDocument entity data access operations.

    Extends BaseRepository with exported document-specific query methods
    and enhanced error handling for document operations.
    """

    def __init__(self, db_session: Session):
        """
        Initialize the ExportedDocument repository.

        Args:
            db_session: SQLAlchemy database session
        """
        super().__init__(db_session, ExportedDocument)
        logger.debug("ExportedDocumentRepository initialized")

    def get_by_project_id(
        self, project_id: int, skip: int = 0, limit: int = 100
    ) -> List[ExportedDocument]:
        """
        Get exported documents by project ID.

        Args:
            project_id: Project ID to filter by
            skip: Number of records to skip
            limit: Maximum number of records to return

        Returns:
            List[ExportedDocument]: List of exported documents

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Retrieving exported documents for project {project_id}")

        try:
            stmt = (
                select(self.model)
                .where(
                    and_(
                        self.model.project_id == project_id,
                        self.model.is_deleted == False,
                    )
                )
                .order_by(desc(self.model.created_at))
                .offset(skip)
                .limit(limit)
            )
            result = list(self.db_session.scalars(stmt).all())

            logger.debug(
                f"Retrieved {len(result)} exported documents for project {project_id}"
            )
            return result

        except SQLAlchemyError as e:
            logger.error(
                f"Database error retrieving exported documents for project {project_id}: {e}"
            )
            self._handle_db_exception(e, "exported document")
            raise

    def get_latest_by_document_type(
        self, project_id: int, document_type: str
    ) -> Optional[ExportedDocument]:
        """
        Get latest document by project ID and document type.

        Args:
            project_id: Project ID
            document_type: Type of document

        Returns:
            Optional[ExportedDocument]: Latest document or None if not found

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(
            f"Retrieving latest {document_type} document for project {project_id}"
        )

        try:
            stmt = select(self.model).where(
                and_(
                    self.model.project_id == project_id,
                    self.model.document_type == document_type,
                    self.model.is_latest_revision == True,
                    self.model.is_deleted == False,
                )
            )
            result = self.db_session.scalar(stmt)

            if result:
                logger.debug(
                    f"Found latest {document_type} document: {result.filename}"
                )
            else:
                logger.debug(f"No latest {document_type} document found")

            return result

        except SQLAlchemyError as e:
            logger.error(
                f"Database error retrieving latest {document_type} document: {e}"
            )
            self._handle_db_exception(e, "exported document")
            raise

    def get_by_document_type(
        self, project_id: int, document_type: str
    ) -> List[ExportedDocument]:
        """
        Get all documents by project ID and document type.

        Args:
            project_id: Project ID
            document_type: Type of document

        Returns:
            List[ExportedDocument]: List of documents ordered by creation date

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(
            f"Retrieving all {document_type} documents for project {project_id}"
        )

        try:
            stmt = (
                select(self.model)
                .where(
                    and_(
                        self.model.project_id == project_id,
                        self.model.document_type == document_type,
                        self.model.is_deleted == False,
                    )
                )
                .order_by(desc(self.model.created_at))
            )
            result = list(self.db_session.scalars(stmt).all())

            logger.debug(f"Retrieved {len(result)} {document_type} documents")
            return result

        except SQLAlchemyError as e:
            logger.error(f"Database error retrieving {document_type} documents: {e}")
            self._handle_db_exception(e, "exported document")
            raise

    def mark_others_as_not_latest(
        self, project_id: int, document_type: str, exclude_id: Optional[int] = None
    ) -> int:
        """
        Mark all other documents as not latest for a document type except the specified one.

        Args:
            project_id: Project ID
            document_type: Type of document
            exclude_id: ID of document to exclude from update

        Returns:
            int: Number of documents updated

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(
            f"Marking other {document_type} documents as not latest for project {project_id}"
        )

        try:
            # Build the where clause
            where_conditions = [
                self.model.project_id == project_id,
                self.model.document_type == document_type,
                self.model.is_latest_revision == True,
                self.model.is_deleted == False,
            ]

            if exclude_id is not None:
                where_conditions.append(self.model.id != exclude_id)

            # Get documents to update
            stmt = select(self.model).where(and_(*where_conditions))
            documents_to_update = list(self.db_session.scalars(stmt).all())

            # Update them
            count = 0
            for document in documents_to_update:
                document.is_latest_revision = False
                count += 1

            logger.debug(f"Marked {count} {document_type} documents as not latest")
            return count

        except SQLAlchemyError as e:
            logger.error(
                f"Database error marking {document_type} documents as not latest: {e}"
            )
            self._handle_db_exception(e, "exported document")
            raise

    def get_with_related_data(self, document_id: int) -> Optional[ExportedDocument]:
        """
        Get exported document with related project and user data.

        Args:
            document_id: Document ID

        Returns:
            Optional[ExportedDocument]: Document with related data or None

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Retrieving document {document_id} with related data")

        try:
            stmt = (
                select(self.model)
                .where(self.model.id == document_id)
                .options(
                    selectinload(self.model.project),
                    selectinload(self.model.generated_by_user),
                )
            )
            result = self.db_session.scalar(stmt)

            if result:
                logger.debug(f"Retrieved document with related data: {result.filename}")
            else:
                logger.debug(f"Document {document_id} not found")

            return result

        except SQLAlchemyError as e:
            logger.error(
                f"Database error retrieving document {document_id} with related data: {e}"
            )
            self._handle_db_exception(e, "exported document")
            raise

    def count_by_project_id(self, project_id: int) -> int:
        """
        Count exported documents for a project.

        Args:
            project_id: Project ID

        Returns:
            int: Number of documents

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Counting exported documents for project {project_id}")

        try:
            stmt = select(self.model).where(
                and_(
                    self.model.project_id == project_id,
                    self.model.is_deleted == False,
                )
            )
            result = len(list(self.db_session.scalars(stmt).all()))

            logger.debug(f"Found {result} documents for project {project_id}")
            return result

        except SQLAlchemyError as e:
            logger.error(
                f"Database error counting documents for project {project_id}: {e}"
            )
            self._handle_db_exception(e, "exported document")
            raise


class CalculationStandardRepository(BaseRepository[CalculationStandard]):
    """
    Repository for CalculationStandard entity data access operations.

    Extends BaseRepository with calculation standard-specific query methods
    and enhanced error handling for standards operations.
    """

    def __init__(self, db_session: Session):
        """
        Initialize the CalculationStandard repository.

        Args:
            db_session: SQLAlchemy database session
        """
        super().__init__(db_session, CalculationStandard)
        logger.debug("CalculationStandardRepository initialized")

    def get_by_standard_code(self, standard_code: str) -> Optional[CalculationStandard]:
        """
        Get calculation standard by standard code.

        Args:
            standard_code: Standard code to search for

        Returns:
            Optional[CalculationStandard]: Standard or None if not found

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Retrieving calculation standard by code: {standard_code}")

        try:
            stmt = select(self.model).where(
                and_(
                    self.model.standard_code == standard_code.upper(),
                    self.model.is_deleted == False,
                )
            )
            result = self.db_session.scalar(stmt)

            if result:
                logger.debug(f"Found calculation standard: {result.name}")
            else:
                logger.debug(f"No calculation standard found for code: {standard_code}")

            return result

        except SQLAlchemyError as e:
            logger.error(
                f"Database error retrieving calculation standard by code {standard_code}: {e}"
            )
            self._handle_db_exception(e, "calculation standard")
            raise

    def get_by_name(self, name: str) -> Optional[CalculationStandard]:
        """
        Get calculation standard by name.

        Args:
            name: Standard name to search for

        Returns:
            Optional[CalculationStandard]: Standard or None if not found

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Retrieving calculation standard by name: {name}")

        try:
            stmt = select(self.model).where(
                and_(
                    self.model.name == name,
                    self.model.is_deleted == False,
                )
            )
            result = self.db_session.scalar(stmt)

            if result:
                logger.debug(f"Found calculation standard: {result.standard_code}")
            else:
                logger.debug(f"No calculation standard found for name: {name}")

            return result

        except SQLAlchemyError as e:
            logger.error(
                f"Database error retrieving calculation standard by name {name}: {e}"
            )
            self._handle_db_exception(e, "calculation standard")
            raise

    def search_by_name_or_code(self, search_term: str) -> List[CalculationStandard]:
        """
        Search calculation standards by name or code.

        Args:
            search_term: Term to search for in name or code

        Returns:
            List[CalculationStandard]: List of matching standards

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Searching calculation standards for: {search_term}")

        try:
            search_pattern = f"%{search_term}%"
            stmt = (
                select(self.model)
                .where(
                    and_(
                        (
                            self.model.name.ilike(search_pattern)
                            | self.model.standard_code.ilike(search_pattern)
                        ),
                        self.model.is_deleted == False,
                    )
                )
                .order_by(self.model.name)
            )
            result = list(self.db_session.scalars(stmt).all())

            logger.debug(
                f"Found {len(result)} calculation standards matching: {search_term}"
            )
            return result

        except SQLAlchemyError as e:
            logger.error(
                f"Database error searching calculation standards for {search_term}: {e}"
            )
            self._handle_db_exception(e, "calculation standard")
            raise

    def get_all_active(
        self, skip: int = 0, limit: int = 100
    ) -> List[CalculationStandard]:
        """
        Get all active calculation standards.

        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return

        Returns:
            List[CalculationStandard]: List of active standards

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug("Retrieving all active calculation standards")

        try:
            stmt = (
                select(self.model)
                .where(self.model.is_deleted == False)
                .order_by(self.model.name)
                .offset(skip)
                .limit(limit)
            )
            result = list(self.db_session.scalars(stmt).all())

            logger.debug(f"Retrieved {len(result)} active calculation standards")
            return result

        except SQLAlchemyError as e:
            logger.error(f"Database error retrieving active calculation standards: {e}")
            self._handle_db_exception(e, "calculation standard")
            raise

    def count_active(self) -> int:
        """
        Count active calculation standards.

        Returns:
            int: Number of active standards

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug("Counting active calculation standards")

        try:
            stmt = select(self.model).where(self.model.is_deleted == False)
            result = len(list(self.db_session.scalars(stmt).all()))

            logger.debug(f"Found {result} active calculation standards")
            return result

        except SQLAlchemyError as e:
            logger.error(f"Database error counting active calculation standards: {e}")
            self._handle_db_exception(e, "calculation standard")
            raise

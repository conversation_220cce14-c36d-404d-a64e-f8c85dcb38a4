# backend/tests/test_api/test_heat_tracing_routes.py
"""
Tests for Heat Tracing API Routes

This module contains comprehensive tests for all heat tracing API endpoints including:
- CRUD operation endpoint tests
- Request/response validation tests
- Error handling tests
- Authentication and authorization tests
"""

import json
import pytest
from fastapi.testclient import TestClient
from unittest.mock import Mock, patch

# Mark all tests in this file
pytestmark = [pytest.mark.unit, pytest.mark.api, pytest.mark.integration, pytest.mark.heat_tracing]

from core.errors.exceptions import (
    DatabaseError,
    DataValidationError,
    DuplicateEntryError,
    NotFoundError,
)
from core.schemas.heat_tracing_schemas import (
    PipeReadSchema,
    PipeListResponseSchema,
    VesselReadSchema,
    VesselListResponseSchema,
    HeatLossCalculationResultSchema,
    StandardsValidationResultSchema,
    HeatTracingDesignResultSchema,
)


class TestPipeEndpoints:
    """Test pipe-related API endpoints."""

    def test_create_pipe_success(self, client: TestClient):
        """Test successful pipe creation."""
        pipe_data = {
            "name": "Test Pipe 001",
            "project_id": 1,
            "pipe_material_id": 10,
            "insulation_material_id": 15,
            "nominal_diameter_mm": 100.0,
            "wall_thickness_mm": 5.0,
            "outer_diameter_mm": 110.0,
            "length_m": 50.0,
            "insulation_thickness_mm": 50.0,
            "fluid_type": "Process Water",
            "line_tag": "L-001",
        }

        with patch(
            "backend.api.v1.heat_tracing_routes.get_heat_tracing_service"
        ) as mock_service:
            # Setup mock service
            mock_service_instance = Mock()
            mock_service.return_value = mock_service_instance

            # Setup mock response
            mock_pipe = PipeReadSchema(
                id=1,
                name="Test Pipe 001",
                project_id=1,
                pipe_material_id=10,
                insulation_material_id=15,
                length_m=50.0,
                insulation_thickness_mm=50.0,
                created_at="2024-01-15T10:30:00Z",
                updated_at="2024-01-15T10:30:00Z",
                is_deleted=False,
                deleted_at=None,
                deleted_by_user_id=None,
            )
            mock_service_instance.create_pipe.return_value = mock_pipe

            # Make request
            response = client.post("/api/v1/heat-tracing/pipes", json=pipe_data)

            # Assertions
            assert response.status_code == 201
            response_data = response.json()
            assert response_data["name"] == "Test Pipe 001"
            assert response_data["id"] == 1

            # Verify service was called
            mock_service_instance.create_pipe.assert_called_once()

    def test_create_pipe_validation_error(self, client: TestClient):
        """Test pipe creation with validation error."""
        invalid_pipe_data = {
            "name": "",  # Empty name
            "project_id": 1,
            "pipe_material_id": 10,
            "insulation_material_id": 15,
            "length_m": 50.0,
            "insulation_thickness_mm": 50.0,
        }

        with patch(
            "backend.api.v1.heat_tracing_routes.get_heat_tracing_service"
        ) as mock_service:
            mock_service_instance = Mock()
            mock_service.return_value = mock_service_instance

            # Setup mock to raise validation error
            mock_service_instance.create_pipe.side_effect = DataValidationError(
                details={"name": "Pipe name cannot be empty"}
            )

            # Make request
            response = client.post("/api/v1/heat-tracing/pipes", json=invalid_pipe_data)

            # Assertions
            assert response.status_code == 400
            assert "validation" in response.json()["detail"].lower()

    def test_create_pipe_duplicate_error(self, client: TestClient):
        """Test pipe creation with duplicate name."""
        pipe_data = {
            "name": "Duplicate Pipe",
            "project_id": 1,
            "pipe_material_id": 10,
            "insulation_material_id": 15,
            "length_m": 50.0,
            "insulation_thickness_mm": 50.0,
        }

        with patch(
            "backend.api.v1.heat_tracing_routes.get_heat_tracing_service"
        ) as mock_service:
            mock_service_instance = Mock()
            mock_service.return_value = mock_service_instance

            # Setup mock to raise duplicate error
            mock_service_instance.create_pipe.side_effect = DuplicateEntryError(
                message="A pipe with this name already exists in this project."
            )

            # Make request
            response = client.post("/api/v1/heat-tracing/pipes", json=pipe_data)

            # Assertions
            assert response.status_code == 409
            assert "already exists" in response.json()["detail"]

    def test_get_pipe_success(self, client: TestClient):
        """Test successful pipe retrieval."""
        with patch(
            "backend.api.v1.heat_tracing_routes.get_heat_tracing_service"
        ) as mock_service:
            mock_service_instance = Mock()
            mock_service.return_value = mock_service_instance

            # Setup mock response
            mock_pipe = PipeReadSchema(
                id=1,
                name="Test Pipe 001",
                project_id=1,
                pipe_material_id=10,
                insulation_material_id=15,
                length_m=50.0,
                insulation_thickness_mm=50.0,
                created_at="2024-01-15T10:30:00Z",
                updated_at="2024-01-15T10:30:00Z",
                is_deleted=False,
                deleted_at=None,
                deleted_by_user_id=None,
            )
            mock_service_instance.get_pipe_details.return_value = mock_pipe

            # Make request
            response = client.get("/api/v1/heat-tracing/pipes/1")

            # Assertions
            assert response.status_code == 200
            response_data = response.json()
            assert response_data["id"] == 1
            assert response_data["name"] == "Test Pipe 001"

    def test_get_pipe_not_found(self, client: TestClient):
        """Test pipe retrieval with non-existent ID."""
        with patch(
            "backend.api.v1.heat_tracing_routes.get_heat_tracing_service"
        ) as mock_service:
            mock_service_instance = Mock()
            mock_service.return_value = mock_service_instance

            # Setup mock to raise not found error
            mock_service_instance.get_pipe_details.side_effect = NotFoundError(
                code="PIPE_NOT_FOUND", detail="Pipe with ID 999 not found"
            )

            # Make request
            response = client.get("/api/v1/heat-tracing/pipes/999")

            # Assertions
            assert response.status_code == 404
            assert "not found" in response.json()["detail"].lower()

    def test_update_pipe_success(self, client: TestClient):
        """Test successful pipe update."""
        update_data = {"name": "Updated Pipe Name", "length_m": 75.0}

        with patch(
            "backend.api.v1.heat_tracing_routes.get_heat_tracing_service"
        ) as mock_service:
            mock_service_instance = Mock()
            mock_service.return_value = mock_service_instance

            # Setup mock response
            mock_pipe = PipeReadSchema(
                id=1,
                name="Updated Pipe Name",
                project_id=1,
                pipe_material_id=10,
                insulation_material_id=15,
                length_m=75.0,
                insulation_thickness_mm=50.0,
                created_at="2024-01-15T10:30:00Z",
                updated_at="2024-01-15T10:30:00Z",
                is_deleted=False,
                deleted_at=None,
                deleted_by_user_id=None,
            )
            mock_service_instance.update_pipe.return_value = mock_pipe

            # Make request
            response = client.put("/api/v1/heat-tracing/pipes/1", json=update_data)

            # Assertions
            assert response.status_code == 200
            response_data = response.json()
            assert response_data["name"] == "Updated Pipe Name"
            assert response_data["length_m"] == 75.0

    def test_delete_pipe_success(self, client: TestClient):
        """Test successful pipe deletion."""
        with patch(
            "backend.api.v1.heat_tracing_routes.get_heat_tracing_service"
        ) as mock_service:
            mock_service_instance = Mock()
            mock_service.return_value = mock_service_instance

            # Setup mock (delete returns None)
            mock_service_instance.delete_pipe.return_value = None

            # Make request
            response = client.delete("/api/v1/heat-tracing/pipes/1")

            # Assertions
            assert response.status_code == 204
            assert response.content == b""

    def test_list_pipes_success(self, client: TestClient):
        """Test successful pipes list retrieval."""
        with patch(
            "backend.api.v1.heat_tracing_routes.get_heat_tracing_service"
        ) as mock_service:
            mock_service_instance = Mock()
            mock_service.return_value = mock_service_instance

            # Setup mock response
            from core.schemas.heat_tracing_schemas import PipeSummarySchema

            mock_pipes = [
                PipeSummarySchema(
                    id=1,
                    name="Pipe 1",
                    project_id=1,
                    length_m=50.0,
                    line_tag="L-001",
                    calculated_heat_loss_wm=25.5,
                    created_at="2024-01-15T10:30:00Z",
                ),
                PipeSummarySchema(
                    id=2,
                    name="Pipe 2",
                    project_id=1,
                    length_m=30.0,
                    line_tag="L-002",
                    calculated_heat_loss_wm=15.2,
                    created_at="2024-01-15T10:30:00Z",
                ),
            ]

            mock_list_response = PipeListResponseSchema(
                pipes=mock_pipes, total=2, page=1, per_page=10, total_pages=1
            )
            mock_service_instance.get_pipes_list.return_value = mock_list_response

            # Make request
            response = client.get(
                "/api/v1/heat-tracing/projects/1/pipes?page=1&per_page=10"
            )

            # Assertions
            assert response.status_code == 200
            response_data = response.json()
            assert response_data["total"] == 2
            assert len(response_data["pipes"]) == 2
            assert response_data["page"] == 1


class TestVesselEndpoints:
    """Test vessel-related API endpoints."""

    def test_create_vessel_success(self, client: TestClient):
        """Test successful vessel creation."""
        vessel_data = {
            "name": "Test Vessel T-001",
            "project_id": 1,
            "material_id": 12,
            "insulation_material_id": 15,
            "dimensions_json": '{"type": "cylinder", "diameter": 2.0, "height": 3.0}',
            "surface_area_m2": 25.13,
            "insulation_thickness_mm": 75.0,
            "equipment_tag": "T-001",
        }

        with patch(
            "backend.api.v1.heat_tracing_routes.get_heat_tracing_service"
        ) as mock_service:
            mock_service_instance = Mock()
            mock_service.return_value = mock_service_instance

            # Setup mock response
            mock_vessel = VesselReadSchema(
                id=1,
                name="Test Vessel T-001",
                project_id=1,
                material_id=12,
                insulation_material_id=15,
                surface_area_m2=25.13,
                insulation_thickness_mm=75.0,
                equipment_tag="T-001",
                created_at="2024-01-15T10:30:00Z",
                updated_at="2024-01-15T10:30:00Z",
                is_deleted=False,
                deleted_at=None,
                deleted_by_user_id=None,
            )
            mock_service_instance.create_vessel.return_value = mock_vessel

            # Make request
            response = client.post("/api/v1/heat-tracing/vessels", json=vessel_data)

            # Assertions
            assert response.status_code == 201
            response_data = response.json()
            assert response_data["name"] == "Test Vessel T-001"
            assert response_data["id"] == 1

    def test_get_vessel_success(self, client: TestClient):
        """Test successful vessel retrieval."""
        with patch(
            "backend.api.v1.heat_tracing_routes.get_heat_tracing_service"
        ) as mock_service:
            mock_service_instance = Mock()
            mock_service.return_value = mock_service_instance

            # Setup mock response
            mock_vessel = VesselReadSchema(
                id=1,
                name="Test Vessel T-001",
                project_id=1,
                material_id=12,
                insulation_material_id=15,
                surface_area_m2=25.13,
                insulation_thickness_mm=75.0,
                equipment_tag="T-001",
                created_at="2024-01-15T10:30:00Z",
                updated_at="2024-01-15T10:30:00Z",
                is_deleted=False,
                deleted_at=None,
                deleted_by_user_id=None,
            )
            mock_service_instance.get_vessel_details.return_value = mock_vessel

            # Make request
            response = client.get("/api/v1/heat-tracing/vessels/1")

            # Assertions
            assert response.status_code == 200
            response_data = response.json()
            assert response_data["id"] == 1
            assert response_data["name"] == "Test Vessel T-001"


class TestCalculationEndpoints:
    """Test calculation-related API endpoints."""

    def test_calculate_pipe_heat_loss_success(self, client: TestClient):
        """Test successful pipe heat loss calculation."""
        calculation_data = {
            "pipe_diameter": 0.11,
            "pipe_length": 50.0,
            "fluid_temperature": 60.0,
            "ambient_temperature": -10.0,
            "insulation_thickness": 0.05,
            "insulation_type": "mineral_wool",
            "wind_speed": 5.0,
            "pipe_material": "carbon_steel",
        }

        with patch(
            "backend.api.v1.heat_tracing_routes.get_heat_tracing_service"
        ) as mock_service:
            mock_service_instance = Mock()
            mock_service.return_value = mock_service_instance

            # Setup mock response
            mock_result = HeatLossCalculationResultSchema(
                heat_loss_rate=25.5,
                total_heat_loss=1275.0,
                surface_temperature=15.0,
                required_power=1530.0,
                safety_factor=1.2,
                calculation_metadata={"method": "steady_state"},
            )
            mock_service_instance.calculate_pipe_heat_loss.return_value = mock_result

            # Make request
            response = client.post(
                "/api/v1/heat-tracing/pipes/1/calculate-heat-loss",
                json=calculation_data,
            )

            # Assertions
            assert response.status_code == 200
            response_data = response.json()
            assert response_data["heat_loss_rate"] == 25.5
            assert response_data["total_heat_loss"] == 1275.0

    def test_validate_standards_compliance_success(self, client: TestClient):
        """Test successful standards compliance validation."""
        validation_data = {
            "heat_loss_result": {
                "heat_loss_rate": 25.5,
                "total_heat_loss": 1275.0,
                "surface_temperature": 15.0,
                "required_power": 1530.0,
                "safety_factor": 1.2,
            },
            "design_parameters": {"fluid_temperature": 60.0},
            "project_standards": ["TR_50410"],
            "hazardous_area_zone": "Zone_1",
            "gas_group": "IIA",
            "temperature_class": "T3",
        }

        with patch(
            "backend.api.v1.heat_tracing_routes.get_heat_tracing_service"
        ) as mock_service:
            mock_service_instance = Mock()
            mock_service.return_value = mock_service_instance

            # Setup mock response
            mock_result = StandardsValidationResultSchema(
                is_compliant=True,
                standard="TR 50410 / IEC 60079-30-1",
                violations=[],
                warnings=[],
                applied_factors={"safety_factor": 1.2},
                metadata={"validation_method": "automated"},
            )
            mock_service_instance.validate_standards_compliance.return_value = (
                mock_result
            )

            # Make request
            response = client.post(
                "/api/v1/heat-tracing/validate-standards-compliance",
                json=validation_data,
            )

            # Assertions
            assert response.status_code == 200
            response_data = response.json()
            assert response_data["is_compliant"] is True
            assert response_data["standard"] == "TR 50410 / IEC 60079-30-1"


class TestDesignWorkflowEndpoints:
    """Test design workflow API endpoints."""

    def test_execute_design_workflow_success(self, client: TestClient):
        """Test successful design workflow execution."""
        design_data = {
            "project_id": 1,
            "pipe_ids": [1, 2],
            "vessel_ids": None,
            "design_parameters": {
                "fluid_temperature": 60.0,
                "ambient_temperature": -10.0,
            },
            "standards_context": {
                "heat_loss_result": {
                    "heat_loss_rate": 25.5,
                    "total_heat_loss": 1275.0,
                    "surface_temperature": 15.0,
                    "required_power": 1530.0,
                    "safety_factor": 1.2,
                },
                "design_parameters": {},
                "project_standards": ["TR_50410"],
                "hazardous_area_zone": "Zone_1",
                "gas_group": "IIA",
                "temperature_class": "T3",
            },
            "auto_assign_circuits": True,
            "optimization_enabled": True,
        }

        with patch(
            "backend.api.v1.heat_tracing_routes.get_heat_tracing_service"
        ) as mock_service:
            mock_service_instance = Mock()
            mock_service.return_value = mock_service_instance

            # Setup mock response
            mock_result = HeatTracingDesignResultSchema(
                project_id=1,
                designed_pipes=[],
                designed_vessels=[],
                created_circuits=[],
                calculation_results=[],
                validation_results=[],
                design_summary={
                    "total_pipes_designed": 2,
                    "total_vessels_designed": 0,
                    "total_circuits_created": 2,
                    "total_power_requirement_w": 3060.0,
                },
                warnings=[],
                errors=[],
            )
            mock_service_instance.execute_heat_tracing_design.return_value = mock_result

            # Make request
            response = client.post(
                "/api/v1/heat-tracing/projects/1/design-workflow", json=design_data
            )

            # Assertions
            assert response.status_code == 200
            response_data = response.json()
            assert response_data["project_id"] == 1
            assert response_data["design_summary"]["total_pipes_designed"] == 2

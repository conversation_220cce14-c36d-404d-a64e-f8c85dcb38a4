{"tests/test_repositories/test_project_repository.py::TestProjectRepository::test_create_project": true, "tests/test_api/test_document_routes.py::TestCalculationStandardRoutes::test_create_calculation_standard": true, "tests/test_services/test_heat_tracing_service.py::TestHeatTracingServicePipeOperations::test_create_pipe_success": true, "tests/test_services/test_heat_tracing_service.py::TestHeatTracingServicePipeOperations::test_create_pipe_validation_error": true, "tests/test_services/test_heat_tracing_service.py::TestHeatTracingServicePipeOperations::test_get_pipe_details_success": true, "tests/test_services/test_heat_tracing_service.py::TestHeatTracingServicePipeOperations::test_update_pipe_success": true, "tests/test_services/test_heat_tracing_service.py::TestHeatTracingServicePipeOperations::test_get_pipes_list_success": true, "tests/test_services/test_heat_tracing_service.py::TestHeatTracingServiceVesselOperations::test_create_vessel_success": true, "tests/test_services/test_heat_tracing_service.py::TestHeatTracingServiceVesselOperations::test_create_vessel_invalid_dimensions": true, "tests/test_services/test_heat_tracing_service.py::TestHeatTracingServiceVesselOperations::test_get_vessel_details_success": true, "tests/test_services/test_heat_tracing_service.py::TestHeatTracingServiceCalculations::test_calculate_pipe_heat_loss_success": true, "tests/test_services/test_heat_tracing_service.py::TestHeatTracingServiceCalculations::test_calculate_pipe_heat_loss_pipe_not_found": true, "tests/test_services/test_heat_tracing_service.py::TestHeatTracingServiceCalculations::test_validate_standards_compliance_success": true, "tests/test_api/test_document_routes.py::TestCalculationStandardRoutes::test_get_calculation_standard_by_id": true, "tests/test_api/test_document_routes.py::TestCalculationStandardRoutes::test_get_calculation_standard_by_code": true, "tests/test_api/test_document_routes.py::TestCalculationStandardRoutes::test_list_calculation_standards": true, "tests/test_api/test_document_routes.py::TestCalculationStandardRoutes::test_update_calculation_standard": true, "tests/test_api/test_document_routes.py::TestCalculationStandardRoutes::test_delete_calculation_standard": true, "tests/test_api/test_document_routes.py::TestCalculationStandardRoutes::test_create_calculation_standard_duplicate_code": true, "tests/test_api/test_document_routes.py::TestCalculationStandardRoutes::test_calculation_standard_validation_errors": true, "tests/test_api/test_document_routes.py::TestImportedDataRevisionRoutes::test_create_imported_data_revision": true, "tests/test_api/test_document_routes.py::TestImportedDataRevisionRoutes::test_list_imported_data_revisions_by_project": true, "tests/test_api/test_document_routes.py::TestExportedDocumentRoutes::test_create_exported_document": true, "tests/test_api/test_document_routes.py::TestExportedDocumentRoutes::test_list_exported_documents_by_project": true, "tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_create_pipe_success": true, "tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_create_pipe_validation_error": true, "tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_create_pipe_duplicate_error": true, "tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_get_pipe_success": true, "tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_update_pipe_success": true, "tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_delete_pipe_success": true, "tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_list_pipes_success": true, "tests/test_api/test_heat_tracing_routes.py::TestVesselEndpoints::test_create_vessel_success": true, "tests/test_api/test_heat_tracing_routes.py::TestVesselEndpoints::test_get_vessel_success": true, "tests/test_api/test_heat_tracing_routes.py::TestCalculationEndpoints::test_calculate_pipe_heat_loss_success": true, "tests/test_api/test_heat_tracing_routes.py::TestCalculationEndpoints::test_validate_standards_compliance_success": true, "tests/test_api/test_heat_tracing_routes.py::TestDesignWorkflowEndpoints::test_execute_design_workflow_success": true, "tests/test_api/test_project_routes.py::TestProjectRoutes::test_create_project_success": true, "tests/test_api/test_project_routes.py::TestProjectRoutes::test_create_project_validation_error": true, "tests/test_api/test_project_routes.py::TestProjectRoutes::test_create_project_duplicate_error": true, "tests/test_api/test_project_routes.py::TestProjectRoutes::test_get_project_by_id_success": true, "tests/test_api/test_project_routes.py::TestProjectRoutes::test_get_project_by_code_success": true, "tests/test_api/test_project_routes.py::TestProjectRoutes::test_get_project_not_found": true, "tests/test_api/test_project_routes.py::TestProjectRoutes::test_update_project_success": true, "tests/test_api/test_project_routes.py::TestProjectRoutes::test_update_project_not_found": true, "tests/test_api/test_project_routes.py::TestProjectRoutes::test_delete_project_success": true, "tests/test_api/test_project_routes.py::TestProjectRoutes::test_delete_project_not_found": true, "tests/test_api/test_project_routes.py::TestProjectRoutes::test_list_projects_success": true, "tests/test_api/test_project_routes.py::TestProjectRoutes::test_list_projects_with_pagination": true, "tests/test_api/test_project_routes.py::TestProjectRoutes::test_list_projects_include_deleted": true, "tests/test_api/test_project_routes.py::TestProjectRoutes::test_invalid_pagination_parameters": true, "tests/test_integration/test_simple_document_integration.py::TestDocumentIntegration::test_calculation_standard_service_operations": true, "tests/test_repositories/test_activity_log_repository.py::TestActivityLogRepository::test_database_error_handling": true, "tests/test_repositories/test_document_repository.py::TestImportedDataRevisionRepository::test_create_imported_data_revision": true, "tests/test_repositories/test_document_repository.py::TestImportedDataRevisionRepository::test_get_by_project_id": true, "tests/test_repositories/test_document_repository.py::TestImportedDataRevisionRepository::test_get_active_revision_by_filename": true, "tests/test_repositories/test_document_repository.py::TestImportedDataRevisionRepository::test_deactivate_other_revisions": true, "tests/test_repositories/test_document_repository.py::TestExportedDocumentRepository::test_create_exported_document": true, "tests/test_repositories/test_document_repository.py::TestExportedDocumentRepository::test_get_latest_by_document_type": true, "tests/test_repositories/test_document_repository.py::TestExportedDocumentRepository::test_mark_others_as_not_latest": true, "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_by_code_nonexistent": true, "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_unique_constraint_name": true, "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_unique_constraint_project_number": true, "tests/test_services/test_component_service.py::TestComponentService::test_get_components_list_success": true, "tests/test_services/test_project_service.py::TestProjectService::test_create_project_success": true, "tests/test_services/test_project_service.py::TestProjectService::test_create_project_duplicate_name_error": true, "tests/test_services/test_project_service.py::TestProjectService::test_get_project_details_by_id": true, "tests/test_services/test_project_service.py::TestProjectService::test_get_project_details_by_code": true, "tests/test_services/test_project_service.py::TestProjectService::test_update_project_success": true, "tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_create_switchboard_success": true, "tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_create_switchboard_invalid_voltage": true, "tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_get_switchboard_success": true, "tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_update_switchboard_success": true, "tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_update_switchboard_invalid_voltage": true, "tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_create_feeder_success": true, "tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_add_switchboard_component_success": true, "tests/test_api/test_switchboard_api.py::TestSwitchboardAPI::test_create_switchboard_success": true, "tests/test_api/test_switchboard_api.py::TestSwitchboardAPI::test_get_switchboard_success": true, "tests/test_api/test_switchboard_api.py::TestSwitchboardAPI::test_list_switchboards_success": true, "tests/test_api/test_switchboard_api.py::TestSwitchboardAPI::test_update_switchboard_success": true, "tests/test_api/test_switchboard_api.py::TestSwitchboardAPI::test_create_feeder_success": true, "tests/test_api/test_switchboard_api.py::TestSwitchboardAPI::test_get_feeder_success": true, "tests/test_api/test_switchboard_api.py::TestSwitchboardAPI::test_add_switchboard_component_success": true, "tests/test_api/test_switchboard_api.py::TestSwitchboardAPI::test_get_load_summary_success": true, "tests/test_api/test_switchboard_api.py::TestSwitchboardAPI::test_get_capacity_analysis_success": true, "tests/test_api/test_switchboard_api.py::TestSwitchboardAPI::test_business_logic_error_handling": true, "tests/test_api/test_user_api.py::TestUserAuthenticationAPI::test_login_success": true, "tests/test_api/test_user_api.py::TestUserAuthenticationAPI::test_login_invalid_credentials": true, "tests/test_api/test_user_api.py::TestUserAuthenticationAPI::test_change_password_invalid_current": true, "tests/test_api/test_user_api.py::TestUserManagementAPI::test_create_user_email_exists": true, "tests/test_api/test_user_api.py::TestUserManagementAPI::test_get_user_success": true, "tests/test_api/test_user_api.py::TestUserManagementAPI::test_list_users_success": true, "tests/test_api/test_user_api.py::TestUserManagementAPI::test_list_users_with_search": true, "tests/test_api/test_user_api.py::TestUserManagementAPI::test_update_user_success": true, "tests/test_api/test_user_api.py::TestUserPreferencesAPI::test_get_user_preferences_success": true, "tests/test_api/test_user_api.py::TestUserPreferencesAPI::test_get_user_preferences_not_found": true, "tests/test_api/test_user_api.py::TestUserPreferencesAPI::test_update_user_preferences_success": true, "tests/test_services/test_user_service.py::TestUserService::test_create_user_success": true, "tests/test_services/test_user_service.py::TestUserService::test_get_user_success": true, "tests/test_services/test_user_service.py::TestUserService::test_login_success": true, "tests/test_services/test_user_service.py::TestUserService::test_login_invalid_password": true, "tests/test_services/test_user_service.py::TestUserService::test_change_password_success": true, "tests/test_services/test_user_service.py::TestUserService::test_change_password_invalid_current": true, "tests/test_services/test_user_service.py::TestUserService::test_update_user_success": true, "tests/test_services/test_user_service.py::TestUserService::test_deactivate_user_success": true, "tests/test_services/test_user_service.py::TestUserService::test_get_user_preferences_success": true, "tests/test_services/test_user_service.py::TestUserService::test_create_or_update_user_preferences_success": true, "tests/test_services/test_user_service.py::TestUserService::test_search_users_success": true, "tests/test_services/test_user_service.py::TestUserService::test_hash_password": true, "tests/test_services/test_user_service.py::TestUserService::test_verify_password": true, "tests/test_api/test_user_api.py::TestUserAuthenticationAPI::test_logout_success": true, "tests/test_api/test_user_api.py::TestUserAuthenticationAPI::test_change_password_success": true, "tests/test_api/test_user_api.py::TestUserManagementAPI::test_create_user_success": true, "tests/test_api/test_user_api.py::TestUserManagementAPI::test_get_user_not_found": true, "tests/test_api/test_user_api.py::TestUserManagementAPI::test_deactivate_user_success": true, "tests/test_api/test_user_api.py::TestUserPreferencesAPI::test_delete_user_preferences_success": true, "tests/test_services/test_user_service.py::TestUserService::test_create_user_email_already_exists": true, "tests/test_services/test_user_service.py::TestUserService::test_get_user_not_found": true, "tests/test_services/test_user_service.py::TestUserService::test_get_user_inactive": true, "tests/test_services/test_user_service.py::TestUserService::test_login_invalid_email": true, "tests/test_services/test_user_service.py::TestUserService::test_login_inactive_user": true, "tests/test_services/test_user_service.py::TestUserService::test_get_user_preferences_not_found": true, "tests/test_services/test_user_service.py::TestUserService::test_count_active_users_success": true, "tests/test_api/test_activity_log_routes.py::TestActivityLogRoutes::test_create_activity_log_success": true, "tests/test_api/test_activity_log_routes.py::TestActivityLogRoutes::test_get_activity_log_success": true, "tests/test_api/test_activity_log_routes.py::TestActivityLogRoutes::test_get_activity_log_not_found": true, "tests/test_api/test_activity_log_routes.py::TestActivityLogRoutes::test_list_activity_logs_success": true, "tests/test_api/test_activity_log_routes.py::TestActivityLogRoutes::test_list_activity_logs_with_filters": true, "tests/test_api/test_activity_log_routes.py::TestActivityLogRoutes::test_update_activity_log_success": true, "tests/test_api/test_activity_log_routes.py::TestActivityLogRoutes::test_get_user_activity_logs_success": true, "tests/test_api/test_activity_log_routes.py::TestActivityLogRoutes::test_get_user_activity_summary_success": true, "tests/test_api/test_activity_log_routes.py::TestActivityLogRoutes::test_get_entity_activity_logs_success": true, "tests/test_api/test_activity_log_routes.py::TestActivityLogRoutes::test_get_security_events_success": true, "tests/test_api/test_activity_log_routes.py::TestActivityLogRoutes::test_log_security_event_success": true, "tests/test_api/test_activity_log_routes.py::TestActivityLogRoutes::test_get_recent_activity_success": true, "tests/test_api/test_activity_log_routes.py::TestActivityLogRoutes::test_search_activity_logs_success": true, "tests/test_api/test_activity_log_routes.py::TestActivityLogRoutes::test_delete_old_activity_logs_success": true, "tests/test_api/test_activity_log_routes.py::TestActivityLogRoutes::test_invalid_input_error_handling": true, "tests/test_api/test_activity_log_routes.py::TestActivityLogRoutes::test_database_error_handling": true, "tests/test_api/test_activity_log_routes.py::TestActivityLogRoutes::test_validation_error_handling": true, "tests/test_api/test_document_routes.py::TestCalculationStandardRoutes::test_get_nonexistent_calculation_standard": true, "tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_get_pipe_not_found": true, "tests/test_api/test_switchboard_api.py::TestSwitchboardAPI::test_create_switchboard_invalid_data": true, "tests/test_api/test_switchboard_api.py::TestSwitchboardAPI::test_get_switchboard_not_found": true, "tests/test_api/test_switchboard_api.py::TestSwitchboardAPI::test_delete_switchboard_success": true, "tests/test_services/test_component_service.py::TestComponentService::test_create_component_success": true, "tests/test_services/test_component_service.py::TestComponentService::test_create_component_category_not_found": true, "tests/test_services/test_component_service.py::TestComponentService::test_create_component_duplicate_name": true, "tests/test_services/test_component_service.py::TestComponentService::test_create_component_integrity_error": true, "tests/test_services/test_component_service.py::TestComponentService::test_get_component_details_success": true, "tests/test_services/test_component_service.py::TestComponentService::test_get_component_details_not_found": true, "tests/test_services/test_component_service.py::TestComponentService::test_get_component_details_deleted": true, "tests/test_services/test_component_service.py::TestComponentService::test_update_component_success": true, "tests/test_services/test_component_service.py::TestComponentService::test_update_component_not_found": true, "tests/test_services/test_component_service.py::TestComponentService::test_delete_component_success": true, "tests/test_services/test_component_service.py::TestComponentService::test_delete_component_not_found": true, "tests/test_services/test_component_service.py::TestComponentService::test_get_components_list_with_category_filter": true, "tests/test_services/test_component_service.py::TestComponentService::test_get_components_list_with_search": true, "tests/test_services/test_electrical_service.py::TestElectricalService::test_cable_sizing_calculation_success": true, "tests/test_services/test_electrical_service.py::TestElectricalService::test_voltage_drop_calculation_success": true, "tests/test_services/test_electrical_service.py::TestElectricalService::test_electrical_standards_validation_success": true, "tests/test_services/test_electrical_service.py::TestElectricalService::test_calculate_load_for_electrical_node_success": true, "tests/test_services/test_electrical_service.py::TestElectricalService::test_calculate_load_for_nonexistent_node": true, "tests/test_services/test_electrical_service.py::TestElectricalService::test_cable_sizing_calculation_error_handling": true, "tests/test_services/test_electrical_service.py::TestElectricalService::test_voltage_drop_calculation_error_handling": true, "tests/test_services/test_electrical_service.py::TestElectricalService::test_generate_recommendations": true, "tests/test_services/test_project_service.py::TestProjectService::test_create_project_validation_error": true, "tests/test_services/test_project_service.py::TestProjectService::test_get_project_details_not_found": true, "tests/test_services/test_project_service.py::TestProjectService::test_delete_project_success": true, "tests/test_services/test_project_service.py::TestProjectService::test_get_projects_list_success": true, "tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_get_switchboard_not_found": true, "tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_delete_switchboard_success": true, "tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_delete_switchboard_not_found": true, "tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_create_feeder_switchboard_not_found": true, "tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_add_switchboard_component_switchboard_not_found": true, "tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_add_switchboard_component_component_not_found": true, "tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_get_switchboard_load_summary_success": true, "tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_get_switchboard_capacity_analysis_success": true}
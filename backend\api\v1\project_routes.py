# backend/api/v1/project_routes.py
"""
Project API Routes

This module defines the FastAPI routes for Project entity operations including:
- CRUD operations (Create, Read, Update, Delete)
- Project listing with pagination
- Project search functionality
"""

from fastapi import APIRouter, Depends, HTTPException, Query, Response, status
from sqlalchemy.orm import Session

from config.logging_config import get_logger

# Dependency injection for database session
from core.database import get_db
from core.errors.error_factory import ErrorFactory
from core.errors.exceptions import (
    DatabaseError,
    DataValidationError,
    ProjectNotFoundError,
)
from core.schemas.error import ErrorResponseSchema
from core.schemas.project_schemas import (
    ProjectCreateSchema,
    ProjectListResponseSchema,
    ProjectReadSchema,
    ProjectUpdateSchema,
)

# Import services and schemas
from core.services.project_service import ProjectService

# Initialize logger for this module
logger = get_logger(__name__)


# Dependency injection for ProjectService (optional, could be in a common dependency)
def get_project_service(db: Session = Depends(get_db)) -> ProjectService:
    from core.repositories.project_repository import ProjectRepository

    project_repo = ProjectRepository(db)
    return ProjectService(project_repo)


router = APIRouter()


@router.post(
    "/",
    response_model=ProjectReadSchema,
    status_code=status.HTTP_201_CREATED,
    summary="Create a new project",
    responses={
        status.HTTP_400_BAD_REQUEST: {
            "model": ErrorResponseSchema,
            "description": "Invalid input data",
        },
        status.HTTP_409_CONFLICT: {
            "model": ErrorResponseSchema,
            "description": "Project with this name/number already exists",
        },
    },
)
async def create_project(
    project_data: ProjectCreateSchema,
    project_service: ProjectService = Depends(get_project_service),
):
    try:
        new_project = project_service.create_project(project_data)
        return new_project
    except DataValidationError as e:  # Pydantic validation error from service
        raise HTTPException(
            status_code=e.status_code, detail=e.detail
        )  # Re-raise for global handler to catch
    except Exception as e:  # Catch other service-level exceptions (e.g., from DB)
        # Use your ErrorFactory to create a standardized exception
        app_exception = ErrorFactory.create_exception("500_001", reason=str(e))
        raise HTTPException(
            status_code=app_exception.status_code, detail=app_exception.detail
        )


@router.get(
    "/{project_id}",
    response_model=ProjectReadSchema,
    summary="Retrieve project details by ID",
    responses={
        status.HTTP_404_NOT_FOUND: {
            "model": ErrorResponseSchema,
            "description": "Project not found",
        },
        status.HTTP_500_INTERNAL_SERVER_ERROR: {
            "model": ErrorResponseSchema,
            "description": "Internal server error",
        },
    },
)
async def get_project(
    project_id: str,  # Using string for UUIDs if you implement them
    project_service: ProjectService = Depends(get_project_service),
):
    try:
        project = project_service.get_project_details(project_id)
        return project
    except ProjectNotFoundError as e:
        # FastAPI's HTTPException will be caught by the global handler
        raise HTTPException(status_code=e.status_code, detail=e.detail)
    except Exception as e:
        app_exception = ErrorFactory.create_exception("500_001", reason=str(e))
        raise HTTPException(
            status_code=app_exception.status_code, detail=app_exception.detail
        )


@router.put(
    "/{project_id}",
    response_model=ProjectReadSchema,
    summary="Update project details",
    responses={
        status.HTTP_400_BAD_REQUEST: {
            "model": ErrorResponseSchema,
            "description": "Invalid input data",
        },
        status.HTTP_404_NOT_FOUND: {
            "model": ErrorResponseSchema,
            "description": "Project not found",
        },
    },
)
async def update_project(
    project_id: str,
    project_data: ProjectUpdateSchema,
    project_service: ProjectService = Depends(get_project_service),
):
    try:
        updated_project = project_service.update_project(project_id, project_data)
        return updated_project
    except (DataValidationError, ProjectNotFoundError) as e:
        raise HTTPException(status_code=e.status_code, detail=e.detail)
    except Exception as e:
        app_exception = ErrorFactory.create_exception("500_001", reason=str(e))
        raise HTTPException(
            status_code=app_exception.status_code, detail=app_exception.detail
        )


@router.delete(
    "/{project_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete a project (soft delete)",
    responses={
        status.HTTP_404_NOT_FOUND: {
            "model": ErrorResponseSchema,
            "description": "Project not found",
        }
    },
)
async def delete_project(
    project_id: str, project_service: ProjectService = Depends(get_project_service)
):
    try:
        # Assume delete_project in service implements soft delete
        project_service.delete_project(project_id)
        return Response(status_code=status.HTTP_204_NO_CONTENT)
    except ProjectNotFoundError as e:
        raise HTTPException(status_code=e.status_code, detail=e.detail)
    except Exception as e:
        app_exception = ErrorFactory.create_exception("500_001", reason=str(e))
        raise HTTPException(
            status_code=app_exception.status_code, detail=app_exception.detail
        )


@router.get(
    "/",
    response_model=ProjectListResponseSchema,
    summary="List projects with pagination",
    responses={
        status.HTTP_500_INTERNAL_SERVER_ERROR: {
            "model": ErrorResponseSchema,
            "description": "Internal server error",
        },
    },
)
async def list_projects(
    page: int = Query(1, ge=1, description="Page number (1-based)"),
    per_page: int = Query(10, ge=1, le=100, description="Number of projects per page"),
    include_deleted: bool = Query(False, description="Include soft-deleted projects"),
    project_service: ProjectService = Depends(get_project_service),
):
    """
    Retrieve a paginated list of projects.

    - **page**: Page number (1-based, default: 1)
    - **per_page**: Number of projects per page (1-100, default: 10)
    - **include_deleted**: Whether to include soft-deleted projects (default: false)
    """
    logger.debug(
        f"Listing projects: page={page}, per_page={per_page}, include_deleted={include_deleted}"
    )

    try:
        projects_list = project_service.get_projects_list(
            page=page, per_page=per_page, include_deleted=include_deleted
        )
        return projects_list
    except DatabaseError as e:
        logger.error(f"Database error listing projects: {e}")
        raise HTTPException(status_code=e.status_code, detail=e.detail)
    except Exception as e:
        logger.error(f"Unexpected error listing projects: {e}", exc_info=True)
        app_exception = ErrorFactory.create_exception("500_001", reason=str(e))
        raise HTTPException(
            status_code=app_exception.status_code, detail=app_exception.detail
        )

[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "ultimate-electrical-designer-backend"
version = "0.1.0"
description = "Backend API for Ultimate Electrical Designer - Heat Tracing and Electrical Design System"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "Ultimate Electrical Designer Team", email = "<EMAIL>"}
]
maintainers = [
    {name = "Ultimate Electrical Designer Team", email = "<EMAIL>"}
]
keywords = ["electrical", "heat-tracing", "engineering", "design", "api", "fastapi"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "Intended Audience :: Manufacturing",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Scientific/Engineering",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Framework :: FastAPI",
]
requires-python = ">=3.11"
dependencies = [
    # Core Framework
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    "pydantic>=2.5.0",
    "pydantic-settings>=2.1.0",

    # Database
    "sqlalchemy>=2.0.23",
    "alembic>=1.13.0",
    "psycopg2-binary>=2.9.9",  # PostgreSQL/SQL Server support

    # Authentication & Security
    "python-jose[cryptography]>=3.3.0",
    "passlib[bcrypt]>=1.7.4",
    "python-multipart>=0.0.6",

    # Utilities
    "python-dotenv>=1.0.0",
    "structlog>=23.2.0",
    "rich>=13.7.0",
    "typer>=0.9.0",

    # Scientific Computing
    "numpy>=1.24.0",
    "scipy>=1.11.0",
    "pandas>=2.1.0",

    # File Processing
    "openpyxl>=3.1.0",
    "xlsxwriter>=3.1.0",

    # HTTP Client
    "httpx>=0.25.0",
    "requests>=2.31.0",
]

[project.optional-dependencies]
dev = [
    # Testing
    "pytest>=7.4.0",
    "pytest-cov>=4.1.0",
    "pytest-asyncio>=0.21.0",
    "pytest-mock>=3.12.0",
    "pytest-xdist>=3.5.0",
    "pytest-benchmark>=4.0.0",
    "pytest-timeout>=2.2.0",
    "pytest-html>=4.1.0",
    "coverage[toml]>=7.3.0",
    "factory-boy>=3.3.0",
    "faker>=20.1.0",

    # Code Quality
    "ruff>=0.1.6",
    "mypy>=1.7.0",
    "pre-commit>=3.6.0",

    # Security Testing
    "bandit>=1.7.5",
    "safety>=2.3.0",

    # Performance Testing
    "locust>=2.17.0",
    "memory-profiler>=0.61.0",

    # Documentation
    "mkdocs>=1.5.0",
    "mkdocs-material>=9.4.0",
    "mkdocstrings[python]>=0.24.0",
]

test = [
    "pytest>=7.4.0",
    "pytest-cov>=4.1.0",
    "pytest-asyncio>=0.21.0",
    "pytest-mock>=3.12.0",
    "factory-boy>=3.3.0",
    "faker>=20.1.0",
]

security = [
    "bandit>=1.7.5",
    "safety>=2.3.0",
]

performance = [
    "locust>=2.17.0",
    "memory-profiler>=0.61.0",
    "pytest-benchmark>=4.0.0",
]

docs = [
    "mkdocs>=1.5.0",
    "mkdocs-material>=9.4.0",
    "mkdocstrings[python]>=0.24.0",
]

[project.urls]
Homepage = "https://github.com/ultimate-electrical-designer/backend"
Documentation = "https://ultimate-electrical-designer.readthedocs.io/"
Repository = "https://github.com/ultimate-electrical-designer/backend"
"Bug Tracker" = "https://github.com/ultimate-electrical-designer/backend/issues"

[project.scripts]
ued-backend = "main:app"

# ============================================================================
# TOOL CONFIGURATION
# ============================================================================

[tool.setuptools.packages.find]
where = ["."]
include = ["core*", "api*", "config*", "tests*"]
exclude = ["tests.*", "*.tests.*", "*.tests"]

[tool.setuptools.package-data]
"*" = ["*.json", "*.yaml", "*.yml", "*.toml", "*.ini"]

# ============================================================================
# PYTEST CONFIGURATION
# ============================================================================

[tool.pytest.ini_options]
minversion = "7.0"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--verbose",
    "--tb=short",
    "--cov=core",
    "--cov=api",
    "--cov=config",
    "--cov-report=term-missing",
    "--cov-report=html:htmlcov",
    "--cov-report=xml:coverage.xml",
    "--cov-fail-under=90",
    "--durations=10",
    "--timeout=300",
]
markers = [
    # Test Categories
    "unit: Unit tests for individual components",
    "integration: Integration tests for component interactions",
    "api: API endpoint tests",
    "database: Database-related tests",
    "security: Security and authentication tests",
    "performance: Performance and load tests",
    "smoke: Quick smoke tests for basic functionality",
    "regression: Regression tests for bug fixes",

    # Component-specific markers
    "project: Project entity tests",
    "component: Component entity tests",
    "heat_tracing: Heat tracing entity tests",
    "electrical: Electrical entity tests",
    "switchboard: Switchboard entity tests",
    "user: User entity tests",
    "document: Document entity tests",
    "activity_log: Activity log entity tests",

    # Infrastructure markers
    "calculations: Calculation engine tests",
    "standards: Standards compliance tests",
    "repository: Repository layer tests",
    "service: Service layer tests",
    "schema: Schema validation tests",

    # Test execution markers
    "slow: Tests that take more than 5 seconds",
    "fast: Tests that complete in under 1 second",
    "external: Tests requiring external services",
    "mock: Tests using mocked dependencies",
]
filterwarnings = [
    "error",
    "ignore::UserWarning",
    "ignore::DeprecationWarning",
    "ignore::PendingDeprecationWarning",
]
log_cli = true
log_cli_level = "INFO"
log_cli_format = "%(asctime)s [%(levelname)8s] %(name)s: %(message)s"
log_cli_date_format = "%Y-%m-%d %H:%M:%S"

# ============================================================================
# COVERAGE CONFIGURATION
# ============================================================================

[tool.coverage.run]
source = ["core", "api", "config"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/migrations/*",
    "*/venv/*",
    "*/env/*",
    "*/.venv/*",
    "setup.py",
    "conftest.py",
]
branch = true
parallel = true

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
    "TYPE_CHECKING",
]
ignore_errors = true
show_missing = true
precision = 2
fail_under = 90

[tool.coverage.html]
directory = "htmlcov"

[tool.coverage.xml]
output = "coverage.xml"

# ============================================================================
# RUFF CONFIGURATION
# ============================================================================

[tool.ruff]
target-version = "py311"
line-length = 88
indent-width = 4
respect-gitignore = true

# Include and exclude patterns
include = ["*.py", "*.pyi", "**/pyproject.toml"]
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".hg",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "venv",
    "migrations",
    "htmlcov",
]

[tool.ruff.lint]
# Enable rules focused on security, performance, and functionality
select = [
    # Security and safety
    "S",    # flake8-bandit (security)
    "B",    # flake8-bugbear (likely bugs)

    # Performance
    "PERF", # Perflint (performance)
    "UP",   # pyupgrade (modern Python usage)

    # Functionality and correctness
    "E",    # pycodestyle errors
    "W",    # pycodestyle warnings
    "F",    # Pyflakes (undefined names, unused imports)
    "C4",   # flake8-comprehensions (list/dict comprehensions)
    "PIE",  # flake8-pie (unnecessary code)
    "SIM",  # flake8-simplify (code simplification)
    "RET",  # flake8-return (return statement issues)
    "ARG",  # flake8-unused-arguments
    "PTH",  # flake8-use-pathlib (use pathlib over os.path)

    # Import organization
    "I",    # isort (import sorting)

    # Type checking
    "T20",  # flake8-print (print statements)
    "PYI",  # flake8-pyi (type stub files)

    # Exception handling
    "BLE",  # flake8-blind-except (bare except)
    "TRY",  # tryceratops (exception handling)

    # Async code
    "ASYNC", # flake8-async (async/await issues)

    # Logging
    "LOG",  # flake8-logging (logging issues)
]

# Ignore rules that are too strict or don't affect security/performance/functionality
ignore = [
    # Style preferences (not security/performance/functionality)
    "E501",   # Line too long (handled by formatter)

    # Documentation (not critical for functionality)
    "D",      # pydocstyle (docstring conventions)

    # Naming conventions (style preference)
    "N",      # pep8-naming

    # Too strict for practical development
    "S101",   # Use of assert (common in tests)
    "S311",   # Use of random (not always security issue)
    "TRY003", # Avoid specifying long messages outside exception class
    "B008",   # Do not perform function calls in argument defaults (FastAPI dependency injection)
    "ARG002", # Unused method argument (common in overrides)
    "PTH123", # Path.open() instead of open() (not always better)

    # FastAPI specific ignores
    "B904",   # raise ... from None in except blocks (FastAPI error handling)
    "S104",   # Possible binding to all interfaces (FastAPI server binding)
]

# Allow fix for all enabled rules
fixable = ["ALL"]
unfixable = []

# Allow unused variables when underscore-prefixed
dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"

[tool.ruff.lint.per-file-ignores]
# Test files can have additional flexibility
"tests/**/*.py" = [
    "S101",   # Use of assert (required in tests)
    "ARG",    # Unused function arguments (test fixtures)
    "S106",   # Possible hardcoded password (test data)
    "S108",   # Probable insecure usage of temp file/directory
    "B011",   # Do not call assert False (used in tests)
    "TRY301", # Abstract raise to an inner function (test helpers)
]

# Configuration files
"conftest.py" = ["ARG", "S101"]
"**/conftest.py" = ["ARG", "S101"]

# Migration files (if using Alembic)
"**/migrations/**/*.py" = ["ALL"]

[tool.ruff.lint.isort]
known-first-party = ["core", "api", "config"]
known-third-party = ["fastapi", "sqlalchemy", "pydantic", "alembic"]
section-order = ["future", "standard-library", "third-party", "first-party", "local-folder"]
split-on-trailing-comma = true

[tool.ruff.lint.flake8-bandit]
# Security checks configuration
check-typed-exception = true

[tool.ruff.lint.flake8-bugbear]
# Extend immutable calls to include FastAPI dependencies
extend-immutable-calls = ["fastapi.Depends", "fastapi.Query", "fastapi.Path"]

[tool.ruff.format]
# Formatting configuration
quote-style = "double"
indent-style = "space"
skip-magic-trailing-comma = false
line-ending = "auto"

# ============================================================================
# MYPY CONFIGURATION
# ============================================================================

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
show_error_codes = true
show_error_context = true
pretty = true

# SQLAlchemy plugin
plugins = ["sqlalchemy.ext.mypy.plugin"]

# Per-module options
[[tool.mypy.overrides]]
module = "tests.*"
disallow_untyped_defs = false
disallow_incomplete_defs = false

[[tool.mypy.overrides]]
module = [
    "alembic.*",
    "uvicorn.*",
    "bcrypt.*",
    "passlib.*",
    "jose.*",
    "factory.*",
    "faker.*",
]
ignore_missing_imports = true

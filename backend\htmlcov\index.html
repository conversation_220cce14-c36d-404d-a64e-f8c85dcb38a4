<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_718ce007.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">58%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button current">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.0">coverage.py v7.8.0</a>,
            created at 2025-06-01 22:35 +0300
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_bfd9059a16e3fa4e_main_router_py.html">backend\api\main_router.py</a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_activity_log_routes_py.html">backend\api\v1\activity_log_routes.py</a></td>
                <td>165</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="119 165">72%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_document_routes_py.html">backend\api\v1\document_routes.py</a></td>
                <td>173</td>
                <td>124</td>
                <td>0</td>
                <td class="right" data-ratio="49 173">28%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_heat_tracing_routes_py.html">backend\api\v1\heat_tracing_routes.py</a></td>
                <td>209</td>
                <td>170</td>
                <td>0</td>
                <td class="right" data-ratio="39 209">19%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_project_routes_py.html">backend\api\v1\project_routes.py</a></td>
                <td>68</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="22 68">32%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_switchboard_routes_py.html">backend\api\v1\switchboard_routes.py</a></td>
                <td>157</td>
                <td>52</td>
                <td>0</td>
                <td class="right" data-ratio="105 157">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_599edd1a5c19dad3_user_routes_py.html">backend\api\v1\user_routes.py</a></td>
                <td>169</td>
                <td>49</td>
                <td>0</td>
                <td class="right" data-ratio="120 169">71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f96405ab037cc57a___init___py.html">backend\core\calculations\__init__.py</a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f96405ab037cc57a_calculation_service_py.html">backend\core\calculations\calculation_service.py</a></td>
                <td>120</td>
                <td>60</td>
                <td>0</td>
                <td class="right" data-ratio="60 120">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_da9c56813bb6ca47___init___py.html">backend\core\calculations\electrical_sizing\__init__.py</a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_da9c56813bb6ca47_cable_sizing_py.html">backend\core\calculations\electrical_sizing\cable_sizing.py</a></td>
                <td>82</td>
                <td>71</td>
                <td>0</td>
                <td class="right" data-ratio="11 82">13%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_da9c56813bb6ca47_voltage_drop_py.html">backend\core\calculations\electrical_sizing\voltage_drop.py</a></td>
                <td>82</td>
                <td>72</td>
                <td>0</td>
                <td class="right" data-ratio="10 82">12%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee7a23c30801740___init___py.html">backend\core\calculations\heat_loss\__init__.py</a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee7a23c30801740_insulation_properties_py.html">backend\core\calculations\heat_loss\insulation_properties.py</a></td>
                <td>76</td>
                <td>76</td>
                <td>0</td>
                <td class="right" data-ratio="0 76">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee7a23c30801740_pipe_heat_loss_py.html">backend\core\calculations\heat_loss\pipe_heat_loss.py</a></td>
                <td>99</td>
                <td>99</td>
                <td>0</td>
                <td class="right" data-ratio="0 99">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee7a23c30801740_vessel_heat_loss_py.html">backend\core\calculations\heat_loss\vessel_heat_loss.py</a></td>
                <td>64</td>
                <td>64</td>
                <td>0</td>
                <td class="right" data-ratio="0 64">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ab7802d47b18b7f9___init___py.html">backend\core\database\__init__.py</a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ab7802d47b18b7f9_engine_py.html">backend\core\database\engine.py</a></td>
                <td>83</td>
                <td>63</td>
                <td>0</td>
                <td class="right" data-ratio="20 83">24%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ab7802d47b18b7f9_initialization_py.html">backend\core\database\initialization.py</a></td>
                <td>106</td>
                <td>85</td>
                <td>0</td>
                <td class="right" data-ratio="21 106">20%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ab7802d47b18b7f9_session_py.html">backend\core\database\session.py</a></td>
                <td>78</td>
                <td>52</td>
                <td>0</td>
                <td class="right" data-ratio="26 78">33%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_71ce108dda7d12d3_error_factory_py.html">backend\core\errors\error_factory.py</a></td>
                <td>12</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="6 12">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_71ce108dda7d12d3_error_registry_py.html">backend\core\errors\error_registry.py</a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_71ce108dda7d12d3_error_templates_py.html">backend\core\errors\error_templates.py</a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_71ce108dda7d12d3_exceptions_py.html">backend\core\errors\exceptions.py</a></td>
                <td>40</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="37 40">92%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24___init___py.html">backend\core\models\__init__.py</a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_activity_log_py.html">backend\core\models\activity_log.py</a></td>
                <td>15</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="14 15">93%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_base_py.html">backend\core\models\base.py</a></td>
                <td>57</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="53 57">93%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_components_py.html">backend\core\models\components.py</a></td>
                <td>24</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="22 24">92%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_documents_py.html">backend\core\models\documents.py</a></td>
                <td>42</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="39 42">93%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_electrical_py.html">backend\core\models\electrical.py</a></td>
                <td>139</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="134 139">96%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_enums_py.html">backend\core\models\enums.py</a></td>
                <td>75</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="75 75">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_heat_tracing_py.html">backend\core\models\heat_tracing.py</a></td>
                <td>134</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="130 134">97%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_project_py.html">backend\core\models\project.py</a></td>
                <td>74</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="59 74">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_switchboard_py.html">backend\core\models\switchboard.py</a></td>
                <td>53</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="49 53">92%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7ac94d1f4eebeb24_users_py.html">backend\core\models\users.py</a></td>
                <td>30</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="28 30">93%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_activity_log_repository_py.html">backend\core\repositories\activity_log_repository.py</a></td>
                <td>228</td>
                <td>66</td>
                <td>0</td>
                <td class="right" data-ratio="162 228">71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_base_repository_py.html">backend\core\repositories\base_repository.py</a></td>
                <td>67</td>
                <td>35</td>
                <td>0</td>
                <td class="right" data-ratio="32 67">48%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_component_repository_py.html">backend\core\repositories\component_repository.py</a></td>
                <td>117</td>
                <td>97</td>
                <td>0</td>
                <td class="right" data-ratio="20 117">17%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_document_repository_py.html">backend\core\repositories\document_repository.py</a></td>
                <td>235</td>
                <td>174</td>
                <td>0</td>
                <td class="right" data-ratio="61 235">26%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_electrical_repository_py.html">backend\core\repositories\electrical_repository.py</a></td>
                <td>374</td>
                <td>222</td>
                <td>0</td>
                <td class="right" data-ratio="152 374">41%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_heat_tracing_repository_py.html">backend\core\repositories\heat_tracing_repository.py</a></td>
                <td>322</td>
                <td>136</td>
                <td>0</td>
                <td class="right" data-ratio="186 322">58%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_project_repository_py.html">backend\core\repositories\project_repository.py</a></td>
                <td>120</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="90 120">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_switchboard_repository_py.html">backend\core\repositories\switchboard_repository.py</a></td>
                <td>185</td>
                <td>155</td>
                <td>0</td>
                <td class="right" data-ratio="30 185">16%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6805001cdfee4172_user_repository_py.html">backend\core\repositories\user_repository.py</a></td>
                <td>160</td>
                <td>135</td>
                <td>0</td>
                <td class="right" data-ratio="25 160">16%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_activity_log_schemas_py.html">backend\core\schemas\activity_log_schemas.py</a></td>
                <td>183</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="181 183">99%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_base_py.html">backend\core\schemas\base.py</a></td>
                <td>42</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="39 42">93%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_component_schemas_py.html">backend\core\schemas\component_schemas.py</a></td>
                <td>82</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="80 82">98%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_document_schemas_py.html">backend\core\schemas\document_schemas.py</a></td>
                <td>187</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="178 187">95%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_electrical_schemas_py.html">backend\core\schemas\electrical_schemas.py</a></td>
                <td>409</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="400 409">98%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_error_py.html">backend\core\schemas\error.py</a></td>
                <td>18</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="18 18">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_heat_tracing_schemas_py.html">backend\core\schemas\heat_tracing_schemas.py</a></td>
                <td>330</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="325 330">98%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_project_schemas_py.html">backend\core\schemas\project_schemas.py</a></td>
                <td>103</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="101 103">98%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_switchboard_schemas_py.html">backend\core\schemas\switchboard_schemas.py</a></td>
                <td>156</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="153 156">98%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e64413ff4bf96b58_user_schemas_py.html">backend\core\schemas\user_schemas.py</a></td>
                <td>132</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="127 132">96%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_activity_log_service_py.html">backend\core\services\activity_log_service.py</a></td>
                <td>228</td>
                <td>60</td>
                <td>0</td>
                <td class="right" data-ratio="168 228">74%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_component_service_py.html">backend\core\services\component_service.py</a></td>
                <td>217</td>
                <td>96</td>
                <td>0</td>
                <td class="right" data-ratio="121 217">56%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_document_service_py.html">backend\core\services\document_service.py</a></td>
                <td>308</td>
                <td>253</td>
                <td>0</td>
                <td class="right" data-ratio="55 308">18%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_electrical_service_py.html">backend\core\services\electrical_service.py</a></td>
                <td>193</td>
                <td>82</td>
                <td>0</td>
                <td class="right" data-ratio="111 193">58%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_heat_tracing_service_py.html">backend\core\services\heat_tracing_service.py</a></td>
                <td>326</td>
                <td>138</td>
                <td>0</td>
                <td class="right" data-ratio="188 326">58%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_project_service_py.html">backend\core\services\project_service.py</a></td>
                <td>158</td>
                <td>56</td>
                <td>0</td>
                <td class="right" data-ratio="102 158">65%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_switchboard_service_py.html">backend\core\services\switchboard_service.py</a></td>
                <td>186</td>
                <td>43</td>
                <td>0</td>
                <td class="right" data-ratio="143 186">77%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ff90eff64d9c93aa_user_service_py.html">backend\core\services\user_service.py</a></td>
                <td>222</td>
                <td>99</td>
                <td>0</td>
                <td class="right" data-ratio="123 222">55%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3d2460180cf335ae___init___py.html">backend\core\standards\__init__.py</a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b97b1182853e7f12___init___py.html">backend\core\standards\iec_60079_30_1\__init__.py</a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b97b1182853e7f12_hazardous_area_compliance_py.html">backend\core\standards\iec_60079_30_1\hazardous_area_compliance.py</a></td>
                <td>69</td>
                <td>69</td>
                <td>0</td>
                <td class="right" data-ratio="0 69">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b97b1182853e7f12_temperature_class_limits_py.html">backend\core\standards\iec_60079_30_1\temperature_class_limits.py</a></td>
                <td>40</td>
                <td>40</td>
                <td>0</td>
                <td class="right" data-ratio="0 40">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3d2460180cf335ae_standards_manager_py.html">backend\core\standards\standards_manager.py</a></td>
                <td>189</td>
                <td>146</td>
                <td>0</td>
                <td class="right" data-ratio="43 189">23%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e6723df25685444a___init___py.html">backend\core\standards\tr_50410\__init__.py</a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e6723df25685444a_heat_loss_factors_py.html">backend\core\standards\tr_50410\heat_loss_factors.py</a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td>8077</td>
                <td>3399</td>
                <td>0</td>
                <td class="right" data-ratio="4678 8077">58%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.0">coverage.py v7.8.0</a>,
            created at 2025-06-01 22:35 +0300
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href="z_e6723df25685444a_heat_loss_factors_py.html"></a>
        <a id="nextFileLink" class="nav" href="z_bfd9059a16e3fa4e_main_router_py.html"></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>

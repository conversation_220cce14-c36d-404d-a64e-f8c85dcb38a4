# backend/api/v1/switchboard_routes.py
"""
Switchboard API Routes

This module provides REST API endpoints for switchboard operations including:
- CRUD operations for switchboards and feeders
- Component installation and management
- Load distribution and capacity analysis
- Electrical integration and connection management
"""

from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session

try:
    from config.logging_config import get_logger
    from core.database.session import get_db_session
    from core.errors.exceptions import (
        BaseApplicationException,
        BusinessLogicError,
        DatabaseError,
        NotFoundError,
    )
    from core.schemas.switchboard_schemas import (
        FeederComponentCreateSchema,
        FeederComponentPaginatedResponseSchema,
        FeederComponentReadSchema,
        FeederComponentSummarySchema,
        FeederComponentUpdateSchema,
        FeederCreateSchema,
        FeederPaginatedResponseSchema,
        FeederReadSchema,
        FeederSummarySchema,
        FeederUpdateSchema,
        SwitchboardCapacityAnalysisSchema,
        SwitchboardComponentCreateSchema,
        SwitchboardComponentPaginatedResponseSchema,
        SwitchboardComponentReadSchema,
        SwitchboardComponentSummarySchema,
        SwitchboardComponentUpdateSchema,
        SwitchboardCreateSchema,
        SwitchboardLoadSummarySchema,
        SwitchboardPaginatedResponseSchema,
        SwitchboardReadSchema,
        SwitchboardSummarySchema,
        SwitchboardUpdateSchema,
    )
    from core.services.switchboard_service import SwitchboardService
except ImportError:
    # For testing and relative imports
    import sys
    import os

    sys.path.insert(
        0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    )
    from config.logging_config import get_logger
    from core.database.session import get_db_session
    from core.errors.exceptions import (
        BaseApplicationException,
        BusinessLogicError,
        DatabaseError,
        NotFoundError,
    )
    from core.schemas.switchboard_schemas import (
        FeederComponentCreateSchema,
        FeederComponentPaginatedResponseSchema,
        FeederComponentReadSchema,
        FeederComponentSummarySchema,
        FeederComponentUpdateSchema,
        FeederCreateSchema,
        FeederPaginatedResponseSchema,
        FeederReadSchema,
        FeederSummarySchema,
        FeederUpdateSchema,
        SwitchboardCapacityAnalysisSchema,
        SwitchboardComponentCreateSchema,
        SwitchboardComponentPaginatedResponseSchema,
        SwitchboardComponentReadSchema,
        SwitchboardComponentSummarySchema,
        SwitchboardComponentUpdateSchema,
        SwitchboardCreateSchema,
        SwitchboardLoadSummarySchema,
        SwitchboardPaginatedResponseSchema,
        SwitchboardReadSchema,
        SwitchboardSummarySchema,
        SwitchboardUpdateSchema,
    )
    from core.services.switchboard_service import SwitchboardService

# Initialize logger for this module
logger = get_logger(__name__)

# Create router
router = APIRouter(prefix="/switchboards", tags=["switchboards"])


def handle_switchboard_exceptions(e: Exception) -> HTTPException:
    """
    Handle switchboard-specific exceptions and convert to HTTP responses.

    Args:
        e: Exception to handle

    Returns:
        HTTPException: Appropriate HTTP exception
    """
    if isinstance(e, BusinessLogicError):
        logger.warning(f"Business logic error: {e.detail}")
        return HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=e.detail,
        )
    elif isinstance(e, NotFoundError):
        logger.warning(f"Resource not found: {e.detail}")
        return HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=e.detail,
        )
    elif isinstance(e, DatabaseError):
        logger.error(f"Database error: {e.detail}")
        return HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )
    elif isinstance(e, BaseApplicationException):
        logger.error(f"Application error: {e.detail}")
        return HTTPException(
            status_code=e.status_code,
            detail=e.detail,
        )
    else:
        logger.error(f"Unexpected error: {str(e)}", exc_info=True)
        return HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


# ============================================================================
# SWITCHBOARD ENDPOINTS
# ============================================================================


@router.post(
    "",
    response_model=SwitchboardReadSchema,
    status_code=status.HTTP_201_CREATED,
    summary="Create switchboard",
    description="Create a new switchboard with specified electrical properties",
)
async def create_switchboard(
    switchboard_data: SwitchboardCreateSchema,
    db: Session = Depends(get_db_session),
) -> SwitchboardReadSchema:
    """Create a new switchboard."""
    logger.info(f"Creating switchboard: {switchboard_data.name}")

    try:
        switchboard_service = SwitchboardService(db)
        switchboard = switchboard_service.create_switchboard(switchboard_data)

        logger.info(f"Created switchboard {switchboard.id}: {switchboard.name}")
        return switchboard

    except Exception as e:
        raise handle_switchboard_exceptions(e)


@router.get(
    "/{switchboard_id}",
    response_model=SwitchboardReadSchema,
    summary="Get switchboard",
    description="Retrieve a specific switchboard by ID",
)
async def get_switchboard(
    switchboard_id: int,
    db: Session = Depends(get_db_session),
) -> SwitchboardReadSchema:
    """Get a specific switchboard by ID."""
    logger.debug(f"Retrieving switchboard {switchboard_id}")

    try:
        switchboard_service = SwitchboardService(db)
        switchboard = switchboard_service.get_switchboard(switchboard_id)

        return switchboard

    except Exception as e:
        raise handle_switchboard_exceptions(e)


@router.get(
    "",
    response_model=SwitchboardPaginatedResponseSchema,
    summary="List switchboards",
    description="Retrieve a paginated list of switchboards for a project",
)
async def list_switchboards(
    project_id: int = Query(..., description="Project ID to filter by"),
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(
        10, ge=1, le=100, description="Maximum number of records to return"
    ),
    voltage_level_v: Optional[int] = Query(None, description="Filter by voltage level"),
    switchboard_type: Optional[str] = Query(
        None, description="Filter by switchboard type"
    ),
    db: Session = Depends(get_db_session),
) -> SwitchboardPaginatedResponseSchema:
    """List switchboards with optional filtering."""
    logger.debug(f"Listing switchboards for project {project_id}")

    try:
        switchboard_service = SwitchboardService(db)

        if voltage_level_v:
            switchboards = switchboard_service.switchboard_repo.get_by_voltage_level(
                project_id, voltage_level_v, skip, limit
            )
        elif switchboard_type:
            switchboards = switchboard_service.switchboard_repo.get_by_type(
                project_id, switchboard_type, skip, limit
            )
        else:
            switchboards = switchboard_service.get_switchboards_by_project(
                project_id, skip, limit
            )

        total = switchboard_service.switchboard_repo.count_by_project(project_id)

        switchboard_summaries = [
            SwitchboardSummarySchema.model_validate(sb) for sb in switchboards
        ]

        return SwitchboardPaginatedResponseSchema(
            switchboards=switchboard_summaries,
            total=total,
            page=(skip // limit) + 1,
            per_page=limit,
            total_pages=(total + limit - 1) // limit,
        )

    except Exception as e:
        raise handle_switchboard_exceptions(e)


@router.put(
    "/{switchboard_id}",
    response_model=SwitchboardReadSchema,
    summary="Update switchboard",
    description="Update an existing switchboard",
)
async def update_switchboard(
    switchboard_id: int,
    switchboard_data: SwitchboardUpdateSchema,
    db: Session = Depends(get_db_session),
) -> SwitchboardReadSchema:
    """Update an existing switchboard."""
    logger.info(f"Updating switchboard {switchboard_id}")

    try:
        switchboard_service = SwitchboardService(db)
        switchboard = switchboard_service.update_switchboard(
            switchboard_id, switchboard_data
        )

        logger.info(f"Updated switchboard {switchboard_id}")
        return switchboard

    except Exception as e:
        raise handle_switchboard_exceptions(e)


@router.delete(
    "/{switchboard_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete switchboard",
    description="Soft delete a switchboard",
)
async def delete_switchboard(
    switchboard_id: int,
    deleted_by_user_id: Optional[int] = Query(
        None, description="ID of user performing deletion"
    ),
    db: Session = Depends(get_db_session),
) -> None:
    """Soft delete a switchboard."""
    logger.info(f"Deleting switchboard {switchboard_id}")

    try:
        switchboard_service = SwitchboardService(db)
        switchboard_service.delete_switchboard(switchboard_id, deleted_by_user_id)

        logger.info(f"Deleted switchboard {switchboard_id}")

    except Exception as e:
        raise handle_switchboard_exceptions(e)


# ============================================================================
# FEEDER ENDPOINTS
# ============================================================================


@router.post(
    "/feeders",
    response_model=FeederReadSchema,
    status_code=status.HTTP_201_CREATED,
    summary="Create feeder",
    description="Create a new feeder within a switchboard",
)
async def create_feeder(
    feeder_data: FeederCreateSchema,
    db: Session = Depends(get_db_session),
) -> FeederReadSchema:
    """Create a new feeder."""
    logger.info(f"Creating feeder: {feeder_data.name}")

    try:
        switchboard_service = SwitchboardService(db)
        feeder = switchboard_service.create_feeder(feeder_data)

        logger.info(f"Created feeder {feeder.id}: {feeder.name}")
        return feeder

    except Exception as e:
        raise handle_switchboard_exceptions(e)


@router.get(
    "/feeders/{feeder_id}",
    response_model=FeederReadSchema,
    summary="Get feeder",
    description="Retrieve a specific feeder by ID",
)
async def get_feeder(
    feeder_id: int,
    db: Session = Depends(get_db_session),
) -> FeederReadSchema:
    """Get a specific feeder by ID."""
    logger.debug(f"Retrieving feeder {feeder_id}")

    try:
        switchboard_service = SwitchboardService(db)
        feeder = switchboard_service.get_feeder(feeder_id)

        return feeder

    except Exception as e:
        raise handle_switchboard_exceptions(e)


@router.get(
    "/{switchboard_id}/feeders",
    response_model=FeederPaginatedResponseSchema,
    summary="List feeders",
    description="Retrieve a paginated list of feeders for a switchboard",
)
async def list_feeders(
    switchboard_id: int,
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(
        10, ge=1, le=100, description="Maximum number of records to return"
    ),
    db: Session = Depends(get_db_session),
) -> FeederPaginatedResponseSchema:
    """List feeders for a switchboard."""
    logger.debug(f"Listing feeders for switchboard {switchboard_id}")

    try:
        switchboard_service = SwitchboardService(db)
        feeders = switchboard_service.get_feeders_by_switchboard(
            switchboard_id, skip, limit
        )

        total = switchboard_service.feeder_repo.count_by_switchboard(switchboard_id)

        feeder_summaries = [
            FeederSummarySchema.model_validate(feeder) for feeder in feeders
        ]

        return FeederPaginatedResponseSchema(
            feeders=feeder_summaries,
            total=total,
            page=(skip // limit) + 1,
            per_page=limit,
            total_pages=(total + limit - 1) // limit,
        )

    except Exception as e:
        raise handle_switchboard_exceptions(e)


# ============================================================================
# COMPONENT INSTALLATION ENDPOINTS
# ============================================================================


@router.post(
    "/components",
    response_model=SwitchboardComponentReadSchema,
    status_code=status.HTTP_201_CREATED,
    summary="Add switchboard component",
    description="Install a component in a switchboard",
)
async def add_switchboard_component(
    component_data: SwitchboardComponentCreateSchema,
    db: Session = Depends(get_db_session),
) -> SwitchboardComponentReadSchema:
    """Add a component to a switchboard."""
    logger.info(
        f"Adding component {component_data.component_id} to switchboard {component_data.switchboard_id}"
    )

    try:
        switchboard_service = SwitchboardService(db)
        component = switchboard_service.add_switchboard_component(component_data)

        logger.info(f"Added component {component.id} to switchboard")
        return component

    except Exception as e:
        raise handle_switchboard_exceptions(e)


@router.get(
    "/{switchboard_id}/components",
    response_model=SwitchboardComponentPaginatedResponseSchema,
    summary="List switchboard components",
    description="Retrieve components installed in a switchboard",
)
async def list_switchboard_components(
    switchboard_id: int,
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(
        10, ge=1, le=100, description="Maximum number of records to return"
    ),
    db: Session = Depends(get_db_session),
) -> SwitchboardComponentPaginatedResponseSchema:
    """List components in a switchboard."""
    logger.debug(f"Listing components for switchboard {switchboard_id}")

    try:
        switchboard_service = SwitchboardService(db)
        components = (
            switchboard_service.switchboard_component_repo.get_by_switchboard_id(
                switchboard_id, skip, limit
            )
        )

        total = switchboard_service.switchboard_component_repo.count_by_switchboard(
            switchboard_id
        )

        component_summaries = [
            SwitchboardComponentSummarySchema.model_validate(comp)
            for comp in components
        ]

        return SwitchboardComponentPaginatedResponseSchema(
            switchboard_components=component_summaries,
            total=total,
            page=(skip // limit) + 1,
            per_page=limit,
            total_pages=(total + limit - 1) // limit,
        )

    except Exception as e:
        raise handle_switchboard_exceptions(e)


# ============================================================================
# ELECTRICAL INTEGRATION ENDPOINTS
# ============================================================================


@router.get(
    "/{switchboard_id}/load-summary",
    response_model=SwitchboardLoadSummarySchema,
    summary="Get switchboard load summary",
    description="Calculate load distribution summary for a switchboard",
)
async def get_switchboard_load_summary(
    switchboard_id: int,
    db: Session = Depends(get_db_session),
) -> SwitchboardLoadSummarySchema:
    """Get load distribution summary for a switchboard."""
    logger.debug(f"Calculating load summary for switchboard {switchboard_id}")

    try:
        switchboard_service = SwitchboardService(db)
        load_summary = switchboard_service.get_switchboard_load_summary(switchboard_id)

        return load_summary

    except Exception as e:
        raise handle_switchboard_exceptions(e)


@router.get(
    "/{switchboard_id}/capacity-analysis",
    response_model=SwitchboardCapacityAnalysisSchema,
    summary="Get switchboard capacity analysis",
    description="Perform capacity analysis for a switchboard",
)
async def get_switchboard_capacity_analysis(
    switchboard_id: int,
    db: Session = Depends(get_db_session),
) -> SwitchboardCapacityAnalysisSchema:
    """Get capacity analysis for a switchboard."""
    logger.debug(f"Performing capacity analysis for switchboard {switchboard_id}")

    try:
        switchboard_service = SwitchboardService(db)
        capacity_analysis = switchboard_service.get_switchboard_capacity_analysis(
            switchboard_id
        )

        return capacity_analysis

    except Exception as e:
        raise handle_switchboard_exceptions(e)
